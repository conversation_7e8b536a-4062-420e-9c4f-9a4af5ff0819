# Configuration Guide

This document provides comprehensive information about all configuration files, environment variables, and setup requirements for the miNEURO application.

## 📋 Table of Contents

- [Environment Setup](#environment-setup)
- [Configuration Files](#configuration-files)
- [Build Configuration](#build-configuration)
- [Development Tools](#development-tools)
- [Testing Configuration](#testing-configuration)
- [Deployment Configuration](#deployment-configuration)

## 🌍 Environment Setup

### Prerequisites

```bash
# Required versions
Node.js: 18.0.0 or higher
npm: 9.0.0 or higher
Git: Latest stable version

# Verify installation
node --version    # Should be 18.0.0+
npm --version     # Should be 9.0.0+
git --version     # Any recent version
```

### Environment Variables

**Development Environment** (`.env.local`):
```bash
# Application Configuration
VITE_APP_TITLE="miNEURO - Neurosurgical Excellence"
VITE_APP_DESCRIPTION="Comprehensive neurosurgical and spine care services"
VITE_APP_URL="http://localhost:5173"

# API Configuration (if applicable)
VITE_API_BASE_URL="https://api.mineuro.com.au"
VITE_API_VERSION="v1"

# Analytics and Tracking
VITE_GOOGLE_ANALYTICS_ID="GA_MEASUREMENT_ID"
VITE_GOOGLE_TAG_MANAGER_ID="GTM_CONTAINER_ID"

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_ERROR_REPORTING=true
VITE_ENABLE_PERFORMANCE_MONITORING=true

# Development Settings
VITE_DEV_MODE=true
VITE_DEBUG_MODE=false
```

**Production Environment** (`.env.production`):
```bash
# Application Configuration
VITE_APP_TITLE="miNEURO - Neurosurgical Excellence"
VITE_APP_DESCRIPTION="Comprehensive neurosurgical and spine care services"
VITE_APP_URL="https://mineuro.com.au"

# API Configuration
VITE_API_BASE_URL="https://api.mineuro.com.au"
VITE_API_VERSION="v1"

# Analytics and Tracking
VITE_GOOGLE_ANALYTICS_ID="PRODUCTION_GA_ID"
VITE_GOOGLE_TAG_MANAGER_ID="PRODUCTION_GTM_ID"

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_ERROR_REPORTING=true
VITE_ENABLE_PERFORMANCE_MONITORING=true

# Production Settings
VITE_DEV_MODE=false
VITE_DEBUG_MODE=false
```

## ⚙️ Configuration Files

### Package.json

**Key Scripts**:
```json
{
  "scripts": {
    "dev": "vite --port 5173 --host",
    "build": "tsc && vite build",
    "build:dev": "tsc && vite build --mode development",
    "preview": "vite preview --port 4173",
    "type-check": "tsc --noEmit",
    "type-check:watch": "tsc --noEmit --watch",
    "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
    "lint:fix": "eslint . --ext ts,tsx --fix",
    "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,css,md}\"",
    "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,css,md}\"",
    "test": "vitest",
    "test:ui": "vitest --ui",
    "test:coverage": "vitest --coverage",
    "test:watch": "vitest --watch",
    "production-check": "npm run type-check && npm run lint && npm run test && npm run build"
  }
}
```

**Dependencies Overview**:
```json
{
  "dependencies": {
    "react": "^18.3.1",
    "react-dom": "^18.3.1",
    "react-router-dom": "^7.6.3",
    "@radix-ui/react-*": "Latest stable versions",
    "tailwindcss": "^3.4.11",
    "lucide-react": "^0.468.0",
    "clsx": "^2.1.1",
    "tailwind-merge": "^2.5.4"
  },
  "devDependencies": {
    "typescript": "^5.7.2",
    "vite": "^6.3.5",
    "vitest": "^2.1.8",
    "@testing-library/react": "^16.1.0",
    "eslint": "^9.17.0",
    "prettier": "^3.4.2",
    "husky": "^9.1.7"
  }
}
```

### TypeScript Configuration

**tsconfig.json**:
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "noImplicitReturns": true,
    "exactOptionalPropertyTypes": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["src"],
  "exclude": ["node_modules", "dist"]
}
```

**Key TypeScript Settings**:
- **Strict Mode**: Enabled for maximum type safety
- **Path Mapping**: `@/*` maps to `src/*` for clean imports
- **Modern Target**: ES2020 for optimal performance
- **React JSX**: Latest JSX transform

### Tailwind CSS Configuration

**tailwind.config.ts**:
```typescript
import type { Config } from 'tailwindcss'

const config: Config = {
  darkMode: ['class'],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  prefix: '',
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px',
      },
    },
    extend: {
      colors: {
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      spacing: {
        'mobile-xs': 'var(--spacing-mobile-xs)',
        'mobile-sm': 'var(--spacing-mobile-sm)',
        'mobile-md': 'var(--spacing-mobile-md)',
        'mobile-lg': 'var(--spacing-mobile-lg)',
        'touch-sm': 'var(--spacing-touch-sm)',
        'touch-md': 'var(--spacing-touch-md)',
        'touch-lg': 'var(--spacing-touch-lg)',
      },
      fontSize: {
        'mobile-xs': 'var(--font-size-mobile-xs)',
        'mobile-sm': 'var(--font-size-mobile-sm)',
        'mobile-md': 'var(--font-size-mobile-md)',
        'mobile-lg': 'var(--font-size-mobile-lg)',
      },
      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
      },
    },
  },
  plugins: [require('tailwindcss-animate')],
}

export default config
```

## 🔧 Build Configuration

### Vite Configuration

**vite.config.ts**:
```typescript
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    port: 5173,
    host: true,
    open: true,
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom'],
          ui: ['@radix-ui/react-slot', '@radix-ui/react-tabs'],
        },
      },
    },
  },
  optimizeDeps: {
    include: ['react', 'react-dom', 'react-router-dom'],
  },
})
```

**Key Build Features**:
- **Code Splitting**: Automatic and manual chunk splitting
- **Source Maps**: Enabled for debugging
- **Optimization**: Dependency pre-bundling
- **Alias Resolution**: Clean import paths with `@/` prefix

## 🛠️ Development Tools

### ESLint Configuration

**eslint.config.js**:
```javascript
import js from '@eslint/js'
import globals from 'globals'
import reactHooks from 'eslint-plugin-react-hooks'
import reactRefresh from 'eslint-plugin-react-refresh'
import tseslint from 'typescript-eslint'

export default tseslint.config(
  { ignores: ['dist'] },
  {
    extends: [js.configs.recommended, ...tseslint.configs.recommended],
    files: ['**/*.{ts,tsx}'],
    languageOptions: {
      ecmaVersion: 2020,
      globals: globals.browser,
    },
    plugins: {
      'react-hooks': reactHooks,
      'react-refresh': reactRefresh,
    },
    rules: {
      ...reactHooks.configs.recommended.rules,
      'react-refresh/only-export-components': [
        'warn',
        { allowConstantExport: true },
      ],
      '@typescript-eslint/no-unused-vars': 'error',
      '@typescript-eslint/no-explicit-any': 'error',
      '@typescript-eslint/prefer-const': 'error',
    },
  },
)
```

### Prettier Configuration

**.prettierrc**:
```json
{
  "semi": false,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "es5",
  "printWidth": 100,
  "bracketSpacing": true,
  "arrowParens": "avoid",
  "endOfLine": "lf"
}
```

### Husky Git Hooks

**.husky/pre-commit**:
```bash
#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# Run type checking
npm run type-check

# Run linting
npm run lint

# Run tests
npm run test --run

# Check formatting
npm run format:check
```

## 🧪 Testing Configuration

### Vitest Configuration

**vitest.config.ts**:
```typescript
import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/tests/setup.ts'],
    css: true,
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/tests/',
        '**/*.d.ts',
        '**/*.config.*',
      ],
    },
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
})
```

### Test Setup

**src/tests/setup.ts**:
```typescript
import '@testing-library/jest-dom'
import { cleanup } from '@testing-library/react'
import { afterEach, vi } from 'vitest'

// Cleanup after each test
afterEach(() => {
  cleanup()
})

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))
```

## 🚀 Deployment Configuration

### Production Build

**Build Process**:
```bash
# Full production build with validation
npm run production-check

# Individual steps
npm run type-check    # TypeScript validation
npm run lint         # Code quality check
npm run test         # Test execution
npm run build        # Production build
```

### Build Output Structure

```
dist/
├── assets/          # Optimized assets
│   ├── index-[hash].js    # Main application bundle
│   ├── vendor-[hash].js   # Vendor dependencies
│   └── [component]-[hash].js # Code-split chunks
├── images/          # Optimized images
├── icons/           # Icon assets
├── index.html       # Main HTML file
└── manifest.json    # Web app manifest
```

### Performance Optimization

**Bundle Analysis**:
```bash
# Analyze bundle size
npm run build -- --analyze

# Check bundle composition
npx vite-bundle-analyzer dist
```

**Optimization Features**:
- Tree shaking for unused code elimination
- Code splitting for optimal loading
- Asset optimization and compression
- Source map generation for debugging

## 🔍 Troubleshooting

### Common Issues

1. **Port Already in Use**:
   ```bash
   # Kill process on port 5173
   npx kill-port 5173
   
   # Or use different port
   npm run dev -- --port 3000
   ```

2. **TypeScript Errors**:
   ```bash
   # Clear TypeScript cache
   npx tsc --build --clean
   
   # Restart TypeScript server in VS Code
   Ctrl+Shift+P > "TypeScript: Restart TS Server"
   ```

3. **Build Failures**:
   ```bash
   # Clear node_modules and reinstall
   rm -rf node_modules package-lock.json
   npm install
   
   # Clear Vite cache
   rm -rf node_modules/.vite
   ```

### Environment Validation

**Check Configuration**:
```bash
# Verify Node.js version
node --version

# Check npm configuration
npm config list

# Validate TypeScript setup
npx tsc --showConfig

# Test build process
npm run build:dev
```

## 📚 Additional Resources

- [Vite Documentation](https://vitejs.dev/) - Build tool configuration
- [TypeScript Configuration](https://www.typescriptlang.org/tsconfig) - TypeScript setup
- [Tailwind CSS Configuration](https://tailwindcss.com/docs/configuration) - Styling setup
- [Vitest Configuration](https://vitest.dev/config/) - Testing setup
- [ESLint Configuration](https://eslint.org/docs/user-guide/configuring/) - Linting setup
