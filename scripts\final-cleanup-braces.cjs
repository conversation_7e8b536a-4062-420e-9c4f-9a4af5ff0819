const fs = require('fs');
const path = require('path');

// Function to recursively find all TypeScript and TSX files
function findTsFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      if (!['node_modules', '.git', 'dist', 'build'].includes(file)) {
        findTsFiles(filePath, fileList);
      }
    } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// Function to clean up extra closing braces
function cleanupExtraBraces(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Split content into lines for analysis
    const lines = content.split('\n');
    const cleanedLines = [];
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const nextLine = i < lines.length - 1 ? lines[i + 1] : '';
      const prevLine = i > 0 ? lines[i - 1] : '';
      
      // Check for pattern: "  }\n  }\n\n" (double closing braces)
      if (line.trim() === '}' && 
          nextLine.trim() === '}' && 
          prevLine.trim().endsWith('}')) {
        
        // Skip this extra closing brace
        modified = true;
        console.log(`Removed extra closing brace in: ${filePath} at line ${i + 1}`);
        continue;
      }
      
      // Check for pattern where we have "}\n  }\n\n" after a method
      if (line.trim() === '}' && 
          nextLine.trim() === '' && 
          i + 2 < lines.length &&
          lines[i + 2].trim() === '' &&
          prevLine.trim() === '}') {
        
        // Skip this extra closing brace
        modified = true;
        console.log(`Removed orphaned closing brace in: ${filePath} at line ${i + 1}`);
        continue;
      }
      
      cleanedLines.push(line);
    }
    
    if (modified) {
      const newContent = cleanedLines.join('\n');
      fs.writeFileSync(filePath, newContent, 'utf8');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Main execution
console.log('🔧 Final cleanup of extra closing braces...');

const srcDir = path.join(process.cwd(), 'src');
const tsFiles = findTsFiles(srcDir);

let fixedCount = 0;
let totalFiles = tsFiles.length;

tsFiles.forEach(filePath => {
  if (cleanupExtraBraces(filePath)) {
    fixedCount++;
  }
});

console.log(`\n✅ Processed ${totalFiles} files`);
console.log(`🔧 Cleaned up extra braces in ${fixedCount} files`);

if (fixedCount > 0) {
  console.log('\n🎯 Next steps:');
  console.log('1. Run npm run build to test the build process');
  console.log('2. Run npm run type-check to verify TypeScript compilation');
}
