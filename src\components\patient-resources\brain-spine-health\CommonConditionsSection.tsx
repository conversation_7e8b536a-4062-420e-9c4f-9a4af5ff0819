import { <PERSON>, Target, Stethoscope, ArrowRight } from 'lucide-react';
import React from 'react';
import { Link } from 'react-router-dom';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { TabsContent } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';

interface CommonCondition {
  id: string;
  name: string;
  description: string;
  symptoms: string[];
  causes: string[];
  treatments: string[];
  urgency: 'routine' | 'urgent' | 'emergency';
  path: string;
}

interface CommonConditionsSectionProps {
  conditions: CommonCondition[];
}

const CommonConditionsSection: React.FC<CommonConditionsSectionProps> = ({ conditions }) => {
  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'emergency': return 'bg-error-light/30 text-foreground border-error/50';
      case 'urgent': return 'bg-info-light/30 text-foreground border-info/50';
      default: return 'bg-success-light/30 text-foreground border-success/50';
    }
  };

  return (
    <TabsContent value="conditions" className="mt-0 space-y-16">
      <div className="text-center space-y-6">
        <h3 className="text-3xl md:text-4xl font-bold text-foreground tracking-tight">
          Common Neurological Conditions
        </h3>
        <p className="text-lg md:text-xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
          Understanding how diseases affect the nervous system helps in early recognition,
          appropriate treatment, and better outcomes for patients.
        </p>
        <div className="w-16 h-1 bg-gradient-to-r from-primary to-primary/50 mx-auto rounded-full"></div>
      </div>

      <div className="space-y-12">
        <div className="grid gap-10">
        {conditions.map((condition) => (
          <Card key={condition.id} className={cn("group bg-card/90 backdrop-blur-sm border border-border/50 hover:border-primary/30 hover:shadow-2xl transition-all duration-500 hover:scale-[1.01] hover:-translate-y-1 overflow-hidden", getUrgencyColor(condition.urgency))}>
            <div className="relative">
              <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-primary/5 to-transparent rounded-bl-full"></div>
              <CardHeader className="pb-8 relative">
                <div className="flex items-start justify-between mb-6">
                  <CardTitle className="text-2xl md:text-3xl font-bold text-primary leading-tight flex-1">
                    {condition.name}
                  </CardTitle>
                  <Badge
                    variant={condition.urgency === 'emergency' ? 'destructive' : condition.urgency === 'urgent' ? 'default' : 'secondary'}
                    className="font-semibold px-4 py-2 text-sm ml-4"
                  >
                    {condition.urgency}
                  </Badge>
                </div>
                <CardDescription className="text-base md:text-lg text-muted-foreground leading-relaxed">
                  {condition.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid gap-6 sm:grid-cols-1 md:grid-cols-2">
                  <div className="space-y-3">
                    <h4 className="font-semibold text-sm mb-3 flex items-center gap-2 text-primary uppercase tracking-wide">
                      <div className="p-1 rounded bg-primary/10">
                        <Eye className="h-4 w-4" />
                      </div>
                      Symptoms
                    </h4>
                    <ul className="text-sm space-y-2">
                      {condition.symptoms.map((symptom, index) => (
                        <li key={index} className="flex items-start gap-3">
                          <span className="w-2 h-2 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                          <span className="text-muted-foreground leading-relaxed">{symptom}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div className="space-y-3">
                    <h4 className="font-semibold text-sm mb-3 flex items-center gap-2 text-primary uppercase tracking-wide">
                      <div className="p-1 rounded bg-primary/10">
                        <Target className="h-4 w-4" />
                      </div>
                      Common Causes
                    </h4>
                    <ul className="text-sm space-y-2">
                      {condition.causes.map((cause, index) => (
                        <li key={index} className="flex items-start gap-3">
                          <span className="w-2 h-2 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                          <span className="text-muted-foreground leading-relaxed">{cause}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
                <div className="space-y-3">
                  <h4 className="font-semibold text-sm mb-3 flex items-center gap-2 text-primary uppercase tracking-wide">
                    <div className="p-1 rounded bg-primary/10">
                      <Stethoscope className="h-4 w-4" />
                    </div>
                    Treatment Approaches
                  </h4>
                  <ul className="text-sm space-y-2">
                    {condition.treatments.map((treatment, index) => (
                      <li key={index} className="flex items-start gap-3">
                        <span className="w-2 h-2 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                        <span className="text-muted-foreground leading-relaxed">{treatment}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                <div className="pt-6 border-t border-border/30">
                  <Button asChild variant="default" size="sm" className="font-semibold">
                    <Link to={condition.path} className="flex items-center gap-2">
                      Learn More About {condition.name}
                      <ArrowRight className="h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </div>
          </Card>
          ))}
        </div>
      </div>
    </TabsContent>
  );
};

export default CommonConditionsSection;
