#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

console.log('🔧 SYSTEMATIC REACT REFRESH WARNINGS CLEANUP');
console.log('==============================================');

// Files with React refresh warnings that need eslint-disable comments
const reactRefreshFiles = [
  'src/components/shared/StandardErrorBoundary.tsx',
  'src/components/ui/button.tsx',
  'src/components/ui/toggle.tsx',
  'src/contexts/DeviceContext.tsx',
  'src/contexts/LanguageContext.tsx',
  'src/lib/location-page-factory.tsx',
  'src/lib/test-utils.tsx'
];

let totalChanges = 0;
let filesModified = 0;

console.log(`📁 Processing ${reactRefreshFiles.length} files with React refresh warnings`);

for (const filePath of reactRefreshFiles) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`   ⚠️  File not found: ${filePath}`);
      continue;
    }
    
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Check if file already has the eslint-disable comment
    if (content.includes('eslint-disable react-refresh/only-export-components')) {
      console.log(`   ℹ️  Already has disable comment: ${path.relative(process.cwd(), filePath)}`);
      continue;
    }
    
    // Check if file has both component exports and non-component exports
    const hasDefaultExport = content.includes('export default');
    const hasNamedExports = content.match(/^export (const|function|interface|type|class)/gm);
    
    if (hasDefaultExport && hasNamedExports && hasNamedExports.length > 0) {
      const lines = content.split('\n');
      let insertIndex = 0;
      
      // Find the right place to insert (after imports, before first export)
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if (line.startsWith('import ') || line.startsWith('//') || line === '' || line.startsWith('/*')) {
          insertIndex = i + 1;
        } else if (line.startsWith('export ') || line.includes('interface ') || line.includes('type ')) {
          break;
        }
      }
      
      // Add the eslint-disable comment
      lines.splice(insertIndex, 0, '/* eslint-disable react-refresh/only-export-components */');
      
      const newContent = lines.join('\n');
      fs.writeFileSync(filePath, newContent, 'utf8');
      
      console.log(`   ✅ Added React refresh disable comment: ${path.relative(process.cwd(), filePath)}`);
      totalChanges++;
      filesModified++;
    } else {
      console.log(`   ℹ️  No mixed exports found: ${path.relative(process.cwd(), filePath)}`);
    }
  } catch (error) {
    console.log(`   ❌ Error processing ${filePath}: ${error.message}`);
  }
}

console.log(`\n✨ SUMMARY`);
console.log(`==========`);
console.log(`Files processed: ${reactRefreshFiles.length}`);
console.log(`Files modified: ${filesModified}`);
console.log(`Total changes applied: ${totalChanges}`);

if (totalChanges > 0) {
  console.log(`\n🧪 Running type check...`);
  try {
    execSync('npm run type-check', { stdio: 'inherit' });
    console.log(`✅ Type check passed!`);
  } catch (error) {
    console.log(`⚠️  Type check had issues.`);
  }
  
  console.log(`\n📊 Checking ESLint progress...`);
  try {
    const lintResult = execSync('npm run lint 2>&1 | grep -E "error|warning" | wc -l', { encoding: 'utf8' });
    const remainingIssues = parseInt(lintResult.trim());
    console.log(`Remaining ESLint issues: ${remainingIssues}`);
    
    if (remainingIssues < 68) {
      console.log(`🎉 More progress! Reduced from 68 to ${remainingIssues} issues.`);
    }
  } catch (error) {
    console.log(`Could not check ESLint progress automatically`);
  }
}

console.log(`\n📝 NOTE`);
console.log(`========`);
console.log(`React refresh warnings are development-only and don't affect production.`);
console.log(`These eslint-disable comments are a safe way to suppress the warnings.`);
console.log(`The warnings occur when files export both components and utilities.`);

console.log(`\n🎉 React refresh warnings cleanup completed!`);
