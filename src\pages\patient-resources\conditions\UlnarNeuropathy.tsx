import React, { useEffect } from 'react';

import { ConditionCauses } from '@/components/medical-conditions/shared/ConditionCauses';
import { ConditionHero } from '@/components/medical-conditions/shared/ConditionHero';
import ConditionOverviewSection from '@/components/medical-conditions/shared/ConditionOverviewSection';
import ConditionQuickFacts from '@/components/medical-conditions/shared/ConditionQuickFacts';
import { ConditionSymptoms } from '@/components/medical-conditions/shared/ConditionSymptoms';
import { ConditionTreatment } from '@/components/medical-conditions/shared/ConditionTreatment';
import {
  UlnarNerveAnatomySection,
  UlnarTreatmentComparison,
  UlnarExerciseGuide,
  UlnarErgonomicGuide,
  UlnarWarningSignsSection
} from '@/components/medical-conditions/ulnar-neuropathy';
import StandardPageLayout from '@/components/StandardPageLayout';
import { ulnarNeuropathyData } from '@/data/conditions/ulnarNeuropathy';

/**
 * Ulnar Neuropathy Component
 * Comprehensive guide to ulnar neuropathy (cubital tunnel syndrome) following established patterns
 * Reuses shared medical condition components for consistency
 */
const UlnarNeuropathy: React.FC = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <StandardPageLayout 
      title="Ulnar Neuropathy (Cubital Tunnel Syndrome) - Comprehensive Guide" 
      showHeader={false}
    >
      <main className="flex-1 pt-20">
        <ConditionHero
          title={ulnarNeuropathyData.hero.title}
          subtitle={ulnarNeuropathyData.hero.subtitle}
          backgroundImage={ulnarNeuropathyData.hero.backgroundImage}
          badge={ulnarNeuropathyData.hero.badge}
        />

        <ConditionQuickFacts facts={ulnarNeuropathyData.quickFacts} />

        <ConditionOverviewSection
          title={ulnarNeuropathyData.overview.title}
          description={ulnarNeuropathyData.overview.description}
          keyPoints={ulnarNeuropathyData.overview.keyPoints}
          imageSrc={ulnarNeuropathyData.overview.imageSrc}
          imageAlt={ulnarNeuropathyData.overview.imageAlt}
          imageCaption={ulnarNeuropathyData.overview.imageCaption}
        />

        <ConditionCauses
          causes={ulnarNeuropathyData.causes}
        />

        <ConditionSymptoms
          symptomCategories={ulnarNeuropathyData.symptoms}
        />

        <UlnarNerveAnatomySection
          anatomyData={ulnarNeuropathyData.anatomy.ulnarNerve}
        />

        <UlnarTreatmentComparison
          conservativeOptions={ulnarNeuropathyData.treatmentOptions.conservative}
          surgicalOptions={ulnarNeuropathyData.treatmentOptions.surgical}
        />

        <UlnarExerciseGuide
          exerciseCategories={ulnarNeuropathyData.exerciseCategories}
        />

        <UlnarErgonomicGuide
          ergonomicCategories={ulnarNeuropathyData.ergonomicCategories}
        />

        <UlnarWarningSignsSection
          warningSignsData={ulnarNeuropathyData.warningSignsData}
        />

        <ConditionTreatment
          conservativeOptions={ulnarNeuropathyData.treatmentOptions.conservative}
          surgicalOptions={ulnarNeuropathyData.treatmentOptions.surgical}
        />
      </main>
    </StandardPageLayout>
  );
};

UlnarNeuropathy.displayName = 'UlnarNeuropathy';

export default UlnarNeuropathy;
