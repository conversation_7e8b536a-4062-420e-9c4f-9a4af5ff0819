# Component Library Documentation

This document provides comprehensive documentation for all UI components in the miNEURO application, including props, usage examples, and best practices.

## 📋 Table of Contents

- [Base UI Components](#base-ui-components)
- [Layout Components](#layout-components)
- [Shared Components](#shared-components)
- [Medical Components](#medical-components)
- [Form Components](#form-components)
- [Navigation Components](#navigation-components)
- [Usage Patterns](#usage-patterns)

## 🧱 Base UI Components

### Button

Professional button component with multiple variants and accessibility features.

**Location**: `src/components/ui/button.tsx`

**Props**:
```typescript
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  asChild?: boolean
  accessibleLabel?: string
}
```

**Usage**:
```tsx
import { Button } from '@/components/ui/button'

// Primary button
<Button variant="default">Book Appointment</Button>

// Secondary action
<Button variant="outline">Learn More</Button>

// Icon button with accessibility
<Button variant="ghost" size="icon" accessibleLabel="Close menu">
  <X className="h-4 w-4" />
</Button>
```

### Card

Flexible card component with semantic HTML and accessibility features.

**Location**: `src/components/ui/card.tsx`

**Props**:
```typescript
interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  as?: 'article' | 'section' | 'div'
  'aria-label'?: string
  'aria-labelledby'?: string
}
```

**Usage**:
```tsx
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'

<Card as="article" aria-labelledby="condition-title">
  <CardHeader>
    <CardTitle id="condition-title">Herniated Disc</CardTitle>
  </CardHeader>
  <CardContent>
    <p>Comprehensive information about herniated disc conditions...</p>
  </CardContent>
</Card>
```

### Tabs

Enhanced tab navigation with accessibility and responsive design.

**Location**: `src/components/ui/tabs.tsx`

**Props**:
```typescript
interface TabsListProps extends React.ComponentPropsWithoutRef<typeof TabsPrimitive.List> {
  className?: string
}

interface TabsTriggerProps extends React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger> {
  className?: string
}
```

**Usage**:
```tsx
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs'

<Tabs defaultValue="overview" aria-label="Condition information">
  <TabsList>
    <TabsTrigger value="overview" aria-label="View condition overview">
      Overview
    </TabsTrigger>
    <TabsTrigger value="symptoms" aria-label="View symptoms information">
      Symptoms
    </TabsTrigger>
  </TabsList>
  <TabsContent value="overview">
    <p>Condition overview content...</p>
  </TabsContent>
</Tabs>
```

## 🏗️ Layout Components

### StandardPageLayout

Main layout wrapper for all pages with SEO and accessibility features.

**Location**: `src/components/StandardPageLayout.tsx`

**Props**:
```typescript
interface StandardPageLayoutProps {
  children: ReactNode
  title?: string
  subtitle?: string
  backgroundImage?: string
  enableParallax?: boolean
  className?: string
  seoData?: SEOData
  pageType?: 'default' | 'landing' | 'article' | 'service' | 'location'
  showHeader?: boolean
  headerClassName?: string
  contentClassName?: string
  enableErrorBoundary?: boolean
  customErrorFallback?: ReactNode
}
```

**Usage**:
```tsx
import StandardPageLayout from '@/components/StandardPageLayout'

<StandardPageLayout
  title="Herniated Disc Treatment"
  subtitle="Comprehensive care for disc-related conditions"
  pageType="article"
  seoData={{
    title: "Herniated Disc Treatment | miNEURO",
    description: "Expert treatment for herniated disc conditions...",
    keywords: ["herniated disc", "spine treatment", "neurosurgery"]
  }}
>
  <main>
    {/* Page content */}
  </main>
</StandardPageLayout>
```

### ResponsiveContainer

Responsive container with device-aware sizing.

**Location**: `src/components/shared/CommonLayoutPatterns.tsx`

**Props**:
```typescript
interface ResponsiveContainerProps {
  children: React.ReactNode
  className?: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
}
```

**Usage**:
```tsx
import { ResponsiveContainer } from '@/components/shared/CommonLayoutPatterns'

<ResponsiveContainer size="lg">
  <h1>Page Title</h1>
  <p>Content that adapts to screen size...</p>
</ResponsiveContainer>
```

## 🔗 Shared Components

### SectionHeader

Standardized section header with consistent typography and spacing.

**Location**: `src/components/shared/SectionHeader.tsx`

**Props**:
```typescript
interface SectionHeaderProps {
  title: string
  subtitle?: string
  description?: string
  centered?: boolean
  className?: string
  titleClassName?: string
  subtitleClassName?: string
  descriptionClassName?: string
}
```

**Usage**:
```tsx
import SectionHeader from '@/components/shared/SectionHeader'

<SectionHeader
  title="Medical Conditions"
  subtitle="Comprehensive Information"
  description="Learn about various neurological and spine conditions with expert insights."
  centered={true}
/>
```

### StandardErrorBoundary

Robust error boundary with retry functionality and user-friendly fallbacks.

**Location**: `src/components/shared/StandardErrorBoundary.tsx`

**Props**:
```typescript
interface StandardErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<ErrorFallbackProps>
  onError?: (error: Error, errorInfo: ErrorInfo) => void
  enableRetry?: boolean
  maxRetries?: number
  resetOnPropsChange?: boolean
  resetKeys?: Array<string | number>
}
```

**Usage**:
```tsx
import { StandardErrorBoundary } from '@/components/shared/StandardErrorBoundary'

<StandardErrorBoundary
  enableRetry={true}
  maxRetries={3}
  onError={(error, errorInfo) => {
    console.error('Component error:', error, errorInfo)
  }}
>
  <ComplexMedicalComponent />
</StandardErrorBoundary>
```

## 🏥 Medical Components

### MedicalConditionTabs

Specialized tab component for medical condition information.

**Location**: `src/components/medical-conditions/MedicalConditionTabs.tsx`

**Props**:
```typescript
interface MedicalConditionTabsProps {
  title?: string
  tabs: TabContent[]
  defaultTab?: string
  className?: string
}

interface TabContent {
  id: string
  label: string
  content: React.ReactNode
}
```

**Usage**:
```tsx
import { MedicalConditionTabs } from '@/components/medical-conditions/MedicalConditionTabs'

const conditionTabs = [
  {
    id: 'overview',
    label: 'Overview',
    content: <ConditionOverview />
  },
  {
    id: 'symptoms',
    label: 'Symptoms',
    content: <SymptomsList />
  }
]

<MedicalConditionTabs
  title="Herniated Disc Information"
  tabs={conditionTabs}
  defaultTab="overview"
/>
```

### AnatomySectionBase

Base component for anatomy sections with interactive features.

**Location**: `src/components/shared/AnatomySectionBase.tsx`

**Props**:
```typescript
interface AnatomyComponent {
  component: string
  description: string
  normalFunction: string[]
  pathologyEffects: string[]
  type?: 'brain' | 'spine' | 'nerve' | 'vascular'
}

interface AnatomySectionBaseProps {
  title: string
  subtitle?: string
  components: AnatomyComponent[]
  className?: string
}
```

## 📝 Form Components

### Enhanced Form Fields

Form components with validation and accessibility features.

**Usage Patterns**:
```tsx
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'

<form>
  <div className="space-y-2">
    <Label htmlFor="email">Email Address</Label>
    <Input
      id="email"
      type="email"
      placeholder="Enter your email"
      required
      aria-describedby="email-error"
    />
    <p id="email-error" className="text-sm text-destructive">
      {/* Error message */}
    </p>
  </div>
  
  <Button type="submit">Submit</Button>
</form>
```

## 🧭 Navigation Components

### Navbar

Main navigation component with responsive design and accessibility.

**Location**: `src/components/Navbar.tsx`

**Features**:
- Responsive mobile menu
- Keyboard navigation
- ARIA labels and roles
- Theme toggle integration
- Active route highlighting

### ThemeToggle

Theme switching component with accessibility features.

**Location**: `src/components/ThemeToggle.tsx`

**Props**:
```typescript
interface ThemeToggleProps {
  className?: string
}
```

## 📐 Usage Patterns

### Component Composition

```tsx
// Good: Composable components
<Card>
  <CardHeader>
    <CardTitle>Title</CardTitle>
    <CardDescription>Description</CardDescription>
  </CardHeader>
  <CardContent>
    <p>Content</p>
  </CardContent>
</Card>

// Good: Using layout components
<ResponsiveContainer size="lg">
  <FlexLayout direction="column" gap="lg">
    <SectionHeader title="Section Title" />
    <div>Content</div>
  </FlexLayout>
</ResponsiveContainer>
```

### Accessibility Best Practices

```tsx
// Always provide meaningful labels
<Button aria-label="Close navigation menu">
  <X className="h-4 w-4" />
</Button>

// Use semantic HTML
<Card as="article" aria-labelledby="article-title">
  <CardHeader>
    <CardTitle id="article-title">Article Title</CardTitle>
  </CardHeader>
</Card>

// Provide error context
<Input
  aria-invalid={hasError}
  aria-describedby={hasError ? "error-message" : undefined}
/>
{hasError && (
  <p id="error-message" role="alert">
    Error message
  </p>
)}
```

### Responsive Design

```tsx
// Use device-aware components
const { isMobile } = useDeviceDetection()

<FlexLayout
  direction={isMobile ? "column" : "row"}
  gap={isMobile ? "sm" : "lg"}
>
  <div>Content</div>
</FlexLayout>

// Responsive grid layouts
<div className={cn(
  "grid gap-6",
  isMobile ? "grid-cols-1" : "grid-cols-2 lg:grid-cols-3"
)}>
  {items.map(item => <Card key={item.id}>{item.content}</Card>)}
</div>
```

## 🎯 Best Practices

1. **Always use TypeScript interfaces** for component props
2. **Include accessibility attributes** (ARIA labels, roles, etc.)
3. **Use semantic HTML elements** when possible
4. **Implement responsive design** with mobile-first approach
5. **Provide error boundaries** for complex components
6. **Follow consistent naming conventions** for props and components
7. **Include comprehensive JSDoc comments** for complex components
8. **Test components** with accessibility tools and screen readers

## 📚 Additional Resources

- [Radix UI Documentation](https://www.radix-ui.com/) - Base component primitives
- [Tailwind CSS Documentation](https://tailwindcss.com/) - Styling utilities
- [WCAG Guidelines](https://www.w3.org/WAI/WCAG21/quickref/) - Accessibility standards
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/) - Testing best practices
