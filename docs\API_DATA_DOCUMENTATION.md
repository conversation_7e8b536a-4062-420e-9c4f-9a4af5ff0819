# API and Data Documentation

This document provides comprehensive documentation for all data structures, interfaces, and service patterns used throughout the miNEURO application.

## 📋 Table of Contents

- [Data Architecture](#data-architecture)
- [Type Definitions](#type-definitions)
- [Data Structures](#data-structures)
- [Service Patterns](#service-patterns)
- [Hooks and State Management](#hooks-and-state-management)
- [Content Management](#content-management)

## 🏗️ Data Architecture

### Overview

The miNEURO application uses a **static data architecture** with TypeScript interfaces for type safety. All content is stored in structured TypeScript files within the `src/data/` directory.

### Data Organization

```
src/data/
├── appointments/        # Appointment-related data
├── conditions/         # Medical condition information
├── consultingRooms/    # Consulting room details
├── expertise/          # Expertise and procedure data
├── faq/               # Frequently asked questions
├── footer/            # Footer content and links
├── homepage.ts        # Homepage content
├── lifestyle-modifications/ # Lifestyle and health data
├── locations/         # Clinic location information
├── medicolegal/       # Legal and medical-legal content
├── navigation/        # Navigation menu structure
├── pages/             # Page-specific data
├── patient-resources/ # Patient education resources
├── peripheral-nerve-conditions/ # Nerve condition data
├── spine-conditions/  # Spine condition information
├── brain-spine-health.ts # Brain and spine health data
└── spine-conditions.ts   # Spine conditions data
```

## 🔧 Type Definitions

### Core Types

**Location**: `src/types/common.ts`

```typescript
// Base content structure
export interface BaseContent {
  id: string
  title: string
  description?: string
  lastUpdated?: string
  author?: string
  medicallyReviewed?: boolean
  reviewDate?: string
}

// SEO metadata
export interface SEOData {
  title: string
  description: string
  keywords?: string[]
  canonicalUrl?: string
  ogImage?: string
  structuredData?: Record<string, any>
}

// Medical condition base structure
export interface MedicalCondition extends BaseContent {
  category: 'spine' | 'brain' | 'peripheral-nerve'
  severity: 'mild' | 'moderate' | 'severe'
  prevalence?: string
  symptoms: string[]
  causes: string[]
  treatments: Treatment[]
  relatedConditions?: string[]
  urgencyLevel?: 'routine' | 'urgent' | 'emergency'
}

// Treatment information
export interface Treatment {
  type: 'conservative' | 'minimally-invasive' | 'surgical'
  name: string
  description: string
  effectiveness?: string
  recoveryTime?: string
  risks?: string[]
  suitability?: string[]
}
```

### Location Types

**Location**: `src/types/location.ts`

```typescript
export interface LocationData {
  id: string
  name: string
  address: {
    street: string
    suburb: string
    state: string
    postcode: string
    country: string
  }
  contact: {
    phone: string
    fax?: string
    email?: string
  }
  coordinates: {
    latitude: number
    longitude: number
  }
  operatingHours: OperatingHours[]
  services: string[]
  facilities: Facility[]
  parking: ParkingInfo
  publicTransport: TransportInfo[]
  nearbyHospitals: NearbyHospital[]
}

export interface OperatingHours {
  day: string
  open: string
  close: string
  isOpen: boolean
  notes?: string
}

export interface Facility {
  name: string
  description: string
  icon?: string
  available: boolean
}
```

### Homepage Types

**Location**: `src/types/homepage.ts`

```typescript
export interface HomepageData {
  hero: HeroSection
  services: ServiceSection[]
  testimonials: Testimonial[]
  locations: LocationSummary[]
  cta: CallToAction
  seo: SEOData
}

export interface HeroSection {
  title: string
  subtitle: string
  description: string
  primaryCTA: {
    text: string
    href: string
    variant: 'primary' | 'secondary'
  }
  secondaryCTA?: {
    text: string
    href: string
    variant: 'outline' | 'ghost'
  }
  backgroundImage?: string
  features?: string[]
}

export interface ServiceSection {
  id: string
  title: string
  description: string
  icon: string
  href: string
  features: string[]
  category: 'primary' | 'secondary'
}
```

## 📊 Data Structures

### Medical Conditions

**Example Structure**: Herniated Disc

```typescript
export const herniatedDisc: MedicalCondition = {
  id: 'herniated-disc',
  title: 'Herniated Disc',
  description: 'Comprehensive information about herniated disc conditions',
  category: 'spine',
  severity: 'moderate',
  prevalence: 'Affects 2-3% of the population annually',
  symptoms: [
    'Lower back pain',
    'Leg pain (sciatica)',
    'Numbness or tingling',
    'Muscle weakness'
  ],
  causes: [
    'Age-related disc degeneration',
    'Sudden injury or trauma',
    'Repetitive strain',
    'Genetic factors'
  ],
  treatments: [
    {
      type: 'conservative',
      name: 'Physical Therapy',
      description: 'Structured exercise program to strengthen supporting muscles',
      effectiveness: '70-80% success rate',
      recoveryTime: '6-12 weeks'
    },
    {
      type: 'minimally-invasive',
      name: 'Epidural Injection',
      description: 'Targeted steroid injection to reduce inflammation',
      effectiveness: '60-70% pain relief',
      recoveryTime: '1-2 weeks'
    }
  ],
  urgencyLevel: 'routine',
  medicallyReviewed: true,
  reviewDate: '2024-12-01'
}
```

### Location Data

**Example Structure**: Surrey Hills Clinic

```typescript
export const surreyHillsData: LocationData = {
  id: 'surrey-hills',
  name: 'Surrey Hills Clinic',
  address: {
    street: '123 Medical Centre Drive',
    suburb: 'Surrey Hills',
    state: 'VIC',
    postcode: '3127',
    country: 'Australia'
  },
  contact: {
    phone: '(03) 9123 4567',
    fax: '(03) 9123 4568',
    email: '<EMAIL>'
  },
  coordinates: {
    latitude: -37.8136,
    longitude: 145.0887
  },
  operatingHours: [
    {
      day: 'Monday',
      open: '08:00',
      close: '17:00',
      isOpen: true
    },
    {
      day: 'Saturday',
      open: '09:00',
      close: '13:00',
      isOpen: true
    }
  ],
  services: [
    'Neurosurgical Consultations',
    'Spine Surgery',
    'Pain Management',
    'Diagnostic Imaging'
  ],
  facilities: [
    {
      name: 'Wheelchair Access',
      description: 'Full wheelchair accessibility throughout the clinic',
      available: true
    }
  ],
  parking: {
    available: true,
    type: 'free',
    spaces: 20,
    timeLimit: '3 hours'
  }
}
```

## 🔄 Service Patterns

### Data Loading Hooks

**Custom Hooks for Data Management**:

```typescript
// useLocationData - Location information management
export const useLocationData = (locationId: string) => {
  const [data, setData] = useState<LocationData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    const loadLocationData = async () => {
      try {
        setLoading(true)
        const locationData = await import(`@/data/locations/${locationId}Data`)
        setData(locationData.default)
      } catch (err) {
        setError(err as Error)
      } finally {
        setLoading(false)
      }
    }

    loadLocationData()
  }, [locationId])

  return { data, loading, error }
}

// usePatientResourceData - Patient resource management
export const usePatientResourceData = (resourceType: string) => {
  const [resources, setResources] = useState<PatientResource[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const loadResources = async () => {
      try {
        const resourceData = await import(`@/data/patient-resources/${resourceType}`)
        setResources(resourceData.default)
      } catch (error) {
        console.error(`Failed to load ${resourceType} resources:`, error)
      } finally {
        setLoading(false)
      }
    }

    loadResources()
  }, [resourceType])

  return { resources, loading }
}
```

### Content Validation

**Type-Safe Content Validation**:

```typescript
// Content validation utilities
export const validateMedicalCondition = (condition: any): condition is MedicalCondition => {
  return (
    typeof condition.id === 'string' &&
    typeof condition.title === 'string' &&
    Array.isArray(condition.symptoms) &&
    Array.isArray(condition.causes) &&
    Array.isArray(condition.treatments) &&
    ['spine', 'brain', 'peripheral-nerve'].includes(condition.category)
  )
}

export const validateLocationData = (location: any): location is LocationData => {
  return (
    typeof location.id === 'string' &&
    typeof location.name === 'string' &&
    typeof location.address === 'object' &&
    typeof location.contact === 'object' &&
    Array.isArray(location.operatingHours)
  )
}
```

## 🎣 Hooks and State Management

### Context Providers

**Device Detection Context**:

```typescript
interface DeviceContextType {
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  screenSize: 'mobile' | 'tablet' | 'desktop'
  orientation: 'portrait' | 'landscape'
}

export const useDeviceDetection = (): DeviceContextType => {
  // Implementation details...
}
```

**Language Context**:

```typescript
interface LanguageContextType {
  currentLanguage: SupportedLanguage
  setLanguage: (language: SupportedLanguage) => void
  t: (key: string, params?: Record<string, string>) => string
}

export const useLanguage = (): LanguageContextType => {
  // Implementation details...
}
```

### Custom Hooks

**SEO Management**:

```typescript
export const useSEO = (seoData: SEOData) => {
  useEffect(() => {
    // Update document title
    document.title = seoData.title

    // Update meta tags
    const metaDescription = document.querySelector('meta[name="description"]')
    if (metaDescription) {
      metaDescription.setAttribute('content', seoData.description)
    }

    // Update Open Graph tags
    const ogTitle = document.querySelector('meta[property="og:title"]')
    if (ogTitle) {
      ogTitle.setAttribute('content', seoData.title)
    }
  }, [seoData])
}
```

## 📝 Content Management

### Content Structure Guidelines

1. **Consistent Naming**: Use kebab-case for IDs and file names
2. **Type Safety**: All content must conform to TypeScript interfaces
3. **Medical Review**: Medical content requires `medicallyReviewed: true`
4. **SEO Optimization**: Include comprehensive SEO metadata
5. **Accessibility**: Provide alternative text and descriptions

### Content Update Process

1. **Modify Data Files**: Update TypeScript data files in `src/data/`
2. **Type Validation**: Ensure content conforms to interfaces
3. **Medical Review**: Have medical content reviewed by professionals
4. **Testing**: Test content rendering and functionality
5. **SEO Check**: Verify SEO metadata and structured data

### Data File Examples

**Brain and Spine Health Data**:

```typescript
export const anatomicalStructures = [
  {
    id: 'cerebral-cortex',
    name: 'Cerebral Cortex',
    category: 'brain',
    description: 'The outer layer of the brain responsible for higher cognitive functions',
    functions: [
      'Conscious thought and decision making',
      'Language processing',
      'Memory formation',
      'Sensory processing'
    ],
    clinicalSignificance: 'Damage can result in cognitive impairment, language difficulties, or sensory deficits'
  }
]

export const commonConditions = [
  {
    id: 'brain-tumour',
    name: 'Brain Tumour',
    category: 'brain',
    urgency: 'high',
    symptoms: [
      'Persistent headaches',
      'Seizures',
      'Cognitive changes',
      'Motor weakness'
    ],
    diagnosticApproach: [
      'Neurological examination',
      'MRI brain scan',
      'Biopsy if indicated'
    ],
    treatmentOptions: [
      'Surgical resection',
      'Radiation therapy',
      'Chemotherapy',
      'Targeted therapy'
    ]
  }
]
```

## 🔍 Best Practices

1. **Type Safety**: Always use TypeScript interfaces for data structures
2. **Validation**: Implement runtime validation for critical data
3. **Error Handling**: Provide fallbacks for missing or invalid data
4. **Performance**: Use lazy loading for large datasets
5. **Caching**: Implement appropriate caching strategies
6. **Documentation**: Keep data structure documentation up to date
7. **Medical Accuracy**: Ensure all medical content is professionally reviewed
8. **Accessibility**: Include accessibility metadata in all content structures

## 📚 Additional Resources

- [TypeScript Handbook](https://www.typescriptlang.org/docs/) - Type system documentation
- [React Hooks Documentation](https://reactjs.org/docs/hooks-intro.html) - Hook patterns and best practices
- [Medical Content Guidelines](./MEDICAL_CONTENT_GUIDELINES.md) - Medical content standards
- [SEO Best Practices](./SEO_GUIDELINES.md) - SEO optimization guidelines
