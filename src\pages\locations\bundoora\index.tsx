import React from 'react';
import { Helmet } from 'react-helmet-async';

import { StandardPageLayout } from '@/components/layout';
import {
  LocationHero,
  LocationContactInfo,
  LocationMap,
  LocationFacilities,
  LocationAmenities,
  TherapeuticInterventions,
  NearbyAmenities,
  NearbyHospitals,
  PatientsPrivacy,
  LocationCTA
} from '@/components/locations';
import { bundooraLocationData, bundooraSpecialSections } from '@/data/locations/bundooraData';

/**
 * Bundoora Location Page - Refactored
 * Complete refactoring preserving ALL content from the original 610-line file
 * Modular architecture with comprehensive data preservation
 */
const BundooraLocation: React.FC = () => {
  return (
    <>
      <Helmet>
        <title>Bundoora Neurosurgeon | UniHill Specialist Centre | Dr <PERSON><PERSON></title>
        <meta
          name="description"
          content="Expert neurosurgery and spine care at UniHill Specialist Centre Bundoora. Dr <PERSON><PERSON> provides comprehensive neurosurgical consultations and treatments. Book your appointment today."
        />
        <meta name="keywords" content="neurosurgeon bundoora, spine surgeon bundoora, unihill specialist centre, neurosurgery bundoora, spine surgery bundoora" />
        <link rel="canonical" href="https://mineuro.com.au/locations/bundoora" />
      </Helmet>

      <StandardPageLayout>
        <LocationHero
          title={bundooraLocationData.hero.title}
          subtitle={bundooraLocationData.hero.subtitle}
          introduction1={bundooraLocationData.hero.introduction1}
          introduction2={bundooraLocationData.hero.introduction2}
          imageUrl={bundooraLocationData.hero.imageUrl}
        />

        <div className="py-16 bg-muted/30">
          <div className="container">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <LocationContactInfo
                address={bundooraLocationData.contact.address}
                phone={bundooraLocationData.contact.phone}
                email={bundooraLocationData.contact.email}
                hours={bundooraLocationData.contact.hours}
                consultingHours={bundooraLocationData.contact.consultingHours}
                appointmentProcess={bundooraLocationData.contact.appointmentProcess}
              />

              <LocationMap
                embedUrl={bundooraLocationData.map.embedUrl}
                title={bundooraLocationData.map.title}
                transportOptions={bundooraLocationData.map.transportOptions}
                gettingHereTitle={bundooraLocationData.map.gettingHereTitle}
                publicTransportTitle={bundooraLocationData.map.publicTransportTitle}
                carTitle={bundooraLocationData.map.carTitle}
              />
            </div>
          </div>
        </div>

        {bundooraLocationData.therapeuticInterventions && (
          <TherapeuticInterventions
            title={bundooraLocationData.therapeuticInterventions.title}
            subtitle={bundooraLocationData.therapeuticInterventions.subtitle}
            description={bundooraLocationData.therapeuticInterventions.description}
            interventions={bundooraLocationData.therapeuticInterventions.interventions}
          />
        )}

        <LocationFacilities
          title={bundooraLocationData.facilities.title}
          subtitle={bundooraLocationData.facilities.subtitle}
          description={bundooraLocationData.facilities.description}
          facilities={bundooraLocationData.facilities.facilities}
          gallery={bundooraLocationData.facilities.gallery}
        />

        <LocationAmenities
          title={bundooraLocationData.amenities.title}
          description={bundooraLocationData.amenities.description}
          locationDetails={bundooraLocationData.amenities.locationDetails}
          medicalFacilities={bundooraLocationData.amenities.medicalFacilities}
          surroundingAmenities={bundooraLocationData.amenities.surroundingAmenities}
          transportation={bundooraLocationData.amenities.transportation}
          parking={bundooraLocationData.amenities.parking}
          images={bundooraLocationData.amenities.images}
        />

        <NearbyAmenities
          title={bundooraLocationData.nearbyAmenities.title}
          subtitle={bundooraLocationData.nearbyAmenities.subtitle}
          description={bundooraLocationData.nearbyAmenities.description}
          categories={bundooraLocationData.nearbyAmenities.categories}
        />

        <NearbyHospitals
          title={bundooraLocationData.nearbyHospitals.title}
          subtitle={bundooraLocationData.nearbyHospitals.subtitle}
          description={bundooraLocationData.nearbyHospitals.description}
          hospitals={bundooraLocationData.nearbyHospitals.hospitals}
        />

        <PatientsPrivacy
          title={bundooraSpecialSections.patientsPrivacy.title}
          subtitle={bundooraSpecialSections.patientsPrivacy.subtitle}
          description1={bundooraSpecialSections.patientsPrivacy.description1}
          description2={bundooraSpecialSections.patientsPrivacy.description2}
          description3={bundooraSpecialSections.patientsPrivacy.description3}
          drAliashkevichLink={bundooraSpecialSections.patientsPrivacy.drAliashkevichLink}
        />

        <LocationCTA
          title={bundooraLocationData.cta.title}
          description={bundooraLocationData.cta.description}
          buttons={bundooraLocationData.cta.buttons}
        />
      </StandardPageLayout>
    </>
  );
};

BundooraLocation.displayName = 'BundooraLocation';

export default BundooraLocation;
