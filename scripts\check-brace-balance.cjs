const fs = require('fs');
const path = require('path');

function checkBraceBalance(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    
    let braceCount = 0;
    let maxBraceCount = 0;
    const braceLines = [];
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const lineNumber = i + 1;
      
      // Count opening braces
      const openBraces = (line.match(/\{/g) || []).length;
      // Count closing braces
      const closeBraces = (line.match(/\}/g) || []).length;
      
      braceCount += openBraces - closeBraces;
      maxBraceCount = Math.max(maxBraceCount, braceCount);
      
      if (openBraces > 0 || closeBraces > 0) {
        braceLines.push({
          line: lineNumber,
          content: line.trim(),
          openBraces,
          closeBraces,
          balance: braceCount
        });
      }
    }
    
    console.log(`File: ${filePath}`);
    console.log(`Final brace balance: ${braceCount}`);
    console.log(`Max nesting level: ${maxBraceCount}`);
    
    if (braceCount !== 0) {
      console.log('\n❌ UNBALANCED BRACES DETECTED!');
      console.log('\nLast 10 brace-related lines:');
      const lastLines = braceLines.slice(-10);
      lastLines.forEach(item => {
        console.log(`Line ${item.line}: ${item.content} (balance: ${item.balance})`);
      });
    } else {
      console.log('\n✅ Braces are balanced');
    }
    
    return braceCount === 0;
  } catch (error) {
    console.error(`Error checking ${filePath}:`, error.message);
    return false;
  }
}

// Check the en.ts file
const filePath = path.join(process.cwd(), 'src/locales/en.ts');
checkBraceBalance(filePath);
