import React from 'react';
import { Link } from 'react-router-dom';

import {
  BrainConditionsIcon,
  SpinalProblemsIcon,
  NerveProblemsIcon,
  MedicoLegalIcon
} from '@/components/shared/MedicalIcons';
import type { ExpertiseShowcaseProps } from '@/types/homepage';

// Removed unused imports - these components are not used in this file

/**
 * Expertise Showcase Component
 * Displays the specialised neurosurgical procedures section
 * Preserves all original content and styling from Index.tsx lines 439-652
 * Now uses Foundation Phase shared components for consistency
 */
const ExpertiseShowcase: React.FC<ExpertiseShowcaseProps> = ({ expertise }) => {
  return (
    <section className="section-spacing section-background">
      <div className="section-container">
        <div className="section-header-spacing text-center">
          <span className="text-primary font-semibold uppercase tracking-wider text-sm border-b-2 border-primary/30 pb-2 inline-block mb-6">
            Featured Procedures
          </span>
          <h2 className="text-headline mb-6">
            Specialised Neurosurgical Procedures
          </h2>
          <p className="lead max-w-3xl mx-auto">
            Comprehensive range of advanced neurosurgical procedures using cutting-edge technology and minimally invasive techniques.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {expertise.map((item) => (
            <ExpertiseCard
              key={item.id}
              expertise={item}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

/**
 * Individual Expertise Card Component
 * Renders each expertise area with its icon, title, description, and link
 */
interface ExpertiseCardProps {
  expertise: {
    id: string;
    title: string;
    description: string;
    icon: string;
    link: string;
    gradientColors: string;
  };
}

const ExpertiseCard: React.FC<ExpertiseCardProps> = ({ expertise }) => {
  const renderIcon = (iconType: string) => {
    const iconClassName = "w-12 h-12 text-primary drop-shadow-sm";

    switch (iconType) {
      case 'brain':
        return <BrainConditionsIcon className={iconClassName} />;

      case 'spine':
        return <SpinalProblemsIcon className={iconClassName} />;

      case 'nerve':
        return <NerveProblemsIcon className={iconClassName} />;

      case 'legal':
        return <MedicoLegalIcon className={iconClassName} />;

      default:
        return null;
    }
  };

  return (
    <div className="medical-card-interactive h-full flex flex-col text-center">
      <div className="flex justify-center mb-6">
        <div className="w-20 h-20 rounded-2xl bg-primary/10 border border-primary/20 flex items-center justify-center shadow-lg backdrop-blur-sm transition-all duration-300 hover:shadow-xl hover:scale-110">
          {renderIcon(expertise.icon)}
        </div>
      </div>

      <div className="flex-1 space-y-4">
        <h3 className="text-subtitle text-xl font-semibold">
          {expertise.title}
        </h3>
        <p className="text-foreground/80 leading-relaxed">
          {expertise.description}
        </p>
      </div>

      <div className="mt-6">
        <Link
          to={expertise.link}
          className="text-primary font-semibold hover:text-primary/80 transition-colors duration-200 inline-flex items-center"
        >
          Learn More
          <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </Link>
      </div>
    </div>
  );
};

ExpertiseShowcase.displayName = 'ExpertiseShowcase';

export default ExpertiseShowcase;
