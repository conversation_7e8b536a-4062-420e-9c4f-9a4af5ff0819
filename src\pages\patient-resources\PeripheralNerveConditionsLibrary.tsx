import React, { useEffect } from 'react';

import PageHeader from '@/components/PageHeader';
import {
  PeripheralNerveConditionCategory,
  PeripheralNerveIntroductionSection,
  PeripheralNerveAnatomySection,
  PeripheralNerveCTASection
} from '@/components/peripheral-nerve-conditions';
import StandardPageLayout from '@/components/StandardPageLayout';
import { useLanguage } from '@/contexts/LanguageContext';
import { peripheralNerveConditionsPageData } from '@/data/peripheral-nerve-conditions/peripheralNerveConditionsData';

/**
 * PeripheralNerveConditionsLibrary Page
 * Comprehensive library of peripheral nerve conditions organised by category
 * Includes nerve entrapments, compressions, tumors, and neuropathies
 * Follows the established pattern from spine conditions library
 */
const PeripheralNerveConditionsLibrary: React.FC = () => {
  const { language: _language } = useLanguage();

  useEffect(() => {
    // Scroll to top when component mounts
    window.scrollTo(0, 0);
  }, []);

  const pageData = peripheralNerveConditionsPageData;

  return (
    <StandardPageLayout 
      title="Peripheral Nerve Conditions Library - Comprehensive Guide" 
      showHeader={false}
    >
      <main className="flex-1 pt-20">
        {/* Page Header */}
        <PageHeader
          title={pageData.pageHeader.title}
          subtitle={pageData.pageHeader.subtitle}
          backgroundImage={pageData.pageHeader.backgroundImage}
          enableParallax={pageData.pageHeader.enableParallax}
        />

        {/* Introduction Section */}
        <PeripheralNerveIntroductionSection
          title={pageData.introduction.title}
          paragraphs={pageData.introduction.paragraphs}
          image={pageData.introduction.image}
        />

        {/* Categories Title */}
        <section className="py-8 bg-gradient-to-br from-background to-muted/10">
          <div className="container mx-auto px-4">
            <div className="text-center">
              <h2 className="text-4xl font-bold text-enhanced-heading mb-4">
                {pageData.categoriesTitle}
              </h2>
              <p className="text-xl text-enhanced-muted max-w-3xl mx-auto">
                Our comprehensive library covers peripheral nerve conditions organised by type and anatomical location.
              </p>
            </div>
          </div>
        </section>

        {/* Nerve Categories */}
        {pageData.nerveCategories.map((category, _index) => (
          <PeripheralNerveConditionCategory
            key={category.id}
            category={category}
            className={index % 2 === 0 ? 'bg-background' : 'bg-gradient-to-br from-muted/10 to-background'}
          />
        ))}

        {/* Nerve Anatomy Section */}
        <PeripheralNerveAnatomySection
          title={pageData.nerveAnatomy.title}
          subtitle={pageData.nerveAnatomy.subtitle}
          paragraphs={pageData.nerveAnatomy.paragraphs}
          anatomyList={pageData.nerveAnatomy.anatomyList}
          image={pageData.nerveAnatomy.image}
          ctaButton={pageData.nerveAnatomy.ctaButton}
        />

        {/* Call to Action Section */}
        <PeripheralNerveCTASection
          title={pageData.cta.title}
          description={pageData.cta.description}
          buttons={pageData.cta.buttons}
        />
      </main>
    </StandardPageLayout>
  );
};

PeripheralNerveConditionsLibrary.displayName = 'PeripheralNerveConditionsLibrary';

export default PeripheralNerveConditionsLibrary;
