import { Brain, Target, Disc, Heart } from 'lucide-react';
import React, { useMemo } from 'react';
import { Helmet } from 'react-helmet-async';

import PageHeader from '@/components/PageHeader';
import {
  AnatomicalStructuresSection,
  CommonConditionsSection,
  HealthResourcesSection,
  LifestyleStrategiesSection
} from '@/components/patient-resources/brain-spine-health';
import StandardPageLayout from '@/components/StandardPageLayout';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { anatomicalStructures, commonConditions, healthResources, lifestyleCategories } from '@/data/brain-spine-health';
import { cn } from '@/lib/utils';


const BrainAndSpineHealth: React.FC = () => {
  const { isMobile } = useDeviceDetection();

  // Memoize tab configuration for performance
  const tabConfig = useMemo(() => [
    { value: 'resources', icon: Brain, label: 'Resources', ariaLabel: 'Health resources and tools' },
    { value: 'anatomy', icon: Target, label: 'Anatomy', ariaLabel: 'Neuroanatomical structures' },
    { value: 'conditions', icon: Disc, label: 'Conditions', ariaLabel: 'Common neurological conditions' },
    { value: 'lifestyle', icon: Heart, label: 'Lifestyle', ariaLabel: 'Lifestyle strategies for optimal health' }
  ], []);

  // Ensure data is available
  if (!healthResources || !anatomicalStructures || !commonConditions || !lifestyleCategories) {
    return (
      <StandardPageLayout>
        <div className="container mx-auto px-4 py-16">
          <div className="text-center">
            <p className="text-muted-foreground">Loading health resources...</p>
          </div>
        </div>
      </StandardPageLayout>
    );
  }

  return (
    <StandardPageLayout>
      <Helmet>
        <title>Brain & Spine Health Resources | miNEURO</title>
        <meta name="description" content="Comprehensive resources for brain and spine health, including anatomy guides, condition information, and lifestyle strategies for optimal neurological wellness." />
        <meta name="keywords" content="brain health, spine health, neurological conditions, anatomy, lifestyle strategies, patient resources" />
      </Helmet>

      <PageHeader
        title="Brain & Spine Health Resources"
        subtitle="Comprehensive resources to support your neurological health journey"
        backgroundImage="https://images.unsplash.com/photo-**********-5c350d0d3c56?w=1200&h=400&fit=crop"
        enableParallax={true}
      />

      <main className="container mx-auto px-4 py-20">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6 tracking-tight">
              Explore Health Resources
            </h2>
            <p className="text-xl md:text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed font-light">
              Access evidence-based information, tools, and strategies to optimize your brain and spine health
            </p>
            <div className="mt-8 w-24 h-1 bg-gradient-to-r from-primary to-primary/50 mx-auto rounded-full"></div>
          </div>

        <Tabs defaultValue="resources" className="w-full" aria-label="Health resources navigation">
          <TabsList className={cn(
            "grid w-full mb-12 bg-card/90 backdrop-blur-md border border-border/40 rounded-xl p-2 shadow-lg",
            isMobile ? "grid-cols-2 gap-2" : "grid-cols-4 gap-1"
          )}>
            {tabConfig.map((tab) => {
              const Icon = tab.icon;
              return (
                <TabsTrigger
                  key={tab.value}
                  value={tab.value}
                  className={cn(
                    "group relative flex items-center justify-center",
                    "font-semibold tracking-wide",
                    "data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-lg",
                    "data-[state=active]:scale-[1.02] data-[state=active]:border-primary/30",
                    "hover:bg-muted/60 hover:text-foreground hover:shadow-md",
                    "transition-all duration-300 ease-out",
                    isMobile
                      ? "flex-col gap-2 px-3 py-4 text-xs min-h-[64px]"
                      : "flex-row gap-3 px-6 py-4 text-sm min-h-[56px]"
                  )}
                  aria-label={tab.ariaLabel}
                >
                  <Icon
                    className={cn(
                      "flex-shrink-0 transition-all duration-300",
                      "group-data-[state=active]:scale-110 group-data-[state=active]:drop-shadow-sm",
                      isMobile ? "h-5 w-5" : "h-5 w-5"
                    )}
                    aria-hidden="true"
                  />
                  <span className="transition-all duration-300 font-medium">
                    {tab.label}
                  </span>
                  {/* Enhanced active indicator */}
                  <div className="absolute inset-0 rounded-md bg-gradient-to-br from-primary/15 via-primary/10 to-primary/5 opacity-0 group-data-[state=active]:opacity-100 transition-all duration-300 -z-10" />
                  {/* Subtle glow effect */}
                  <div className="absolute inset-0 rounded-md shadow-lg opacity-0 group-data-[state=active]:opacity-20 transition-all duration-300 -z-20" />
                </TabsTrigger>
              );
            })}
          </TabsList>

          <HealthResourcesSection resources={healthResources} />
          <AnatomicalStructuresSection structures={anatomicalStructures} />
          <CommonConditionsSection conditions={commonConditions} />
          <LifestyleStrategiesSection categories={lifestyleCategories} />
        </Tabs>
        </div>
      </main>
    </StandardPageLayout>
  );
};

export default BrainAndSpineHealth;