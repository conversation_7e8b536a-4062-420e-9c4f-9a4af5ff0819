# Development Guidelines

This document outlines the coding standards, component patterns, and best practices established for the miNEURO application development.

## 📋 Table of Contents

- [Code Standards](#code-standards)
- [Component Patterns](#component-patterns)
- [TypeScript Guidelines](#typescript-guidelines)
- [Styling Standards](#styling-standards)
- [Testing Requirements](#testing-requirements)
- [Performance Guidelines](#performance-guidelines)
- [Accessibility Standards](#accessibility-standards)
- [Git Workflow](#git-workflow)

## 💻 Code Standards

### General Principles

1. **Consistency**: Follow established patterns throughout the codebase
2. **Readability**: Write self-documenting code with clear naming
3. **Maintainability**: Structure code for easy updates and debugging
4. **Performance**: Optimize for loading speed and runtime efficiency
5. **Accessibility**: Ensure all features work for users with disabilities

### File Organization

```
src/
├── components/
│   ├── ui/              # Base UI components (reusable)
│   ├── shared/          # Shared business logic components
│   ├── layout/          # Layout-specific components
│   └── [feature]/       # Feature-specific components
├── pages/               # Page components (one per route)
├── hooks/               # Custom React hooks
├── contexts/            # React context providers
├── lib/                 # Utility functions and configurations
├── data/                # Static data and content
├── types/               # TypeScript type definitions
└── tests/               # Test files and utilities
```

### Naming Conventions

```typescript
// Components: PascalCase
export const MedicalConditionCard = () => {}

// Files: kebab-case for components, camelCase for utilities
medical-condition-card.tsx
utils.ts

// Variables and functions: camelCase
const patientData = {}
const fetchPatientInfo = () => {}

// Constants: SCREAMING_SNAKE_CASE
const MAX_RETRY_ATTEMPTS = 3

// Types and interfaces: PascalCase
interface PatientData {}
type ConditionType = 'spine' | 'brain'

// CSS classes: kebab-case (Tailwind standard)
className="medical-card-header"
```

## 🧱 Component Patterns

### Component Structure

```typescript
// 1. Imports (grouped and ordered)
import React, { useState, useEffect } from 'react'
import { useRouter } from 'react-router-dom'

import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { useDeviceDetection } from '@/contexts/DeviceContext'
import { cn } from '@/lib/utils'
import type { MedicalCondition } from '@/types/medical'

// 2. Types and interfaces
interface MedicalConditionCardProps {
  condition: MedicalCondition
  variant?: 'default' | 'compact' | 'detailed'
  onSelect?: (condition: MedicalCondition) => void
  className?: string
}

// 3. Component implementation
export const MedicalConditionCard: React.FC<MedicalConditionCardProps> = ({
  condition,
  variant = 'default',
  onSelect,
  className
}) => {
  // 4. Hooks (in order: state, context, custom hooks, effects)
  const [isExpanded, setIsExpanded] = useState(false)
  const { isMobile } = useDeviceDetection()
  const router = useRouter()

  useEffect(() => {
    // Effect logic
  }, [condition.id])

  // 5. Event handlers
  const handleCardClick = () => {
    onSelect?.(condition)
    router.push(`/conditions/${condition.id}`)
  }

  // 6. Render logic
  return (
    <Card
      className={cn(
        "medical-condition-card",
        "cursor-pointer transition-all duration-200",
        "hover:shadow-lg hover:scale-[1.02]",
        variant === 'compact' && "p-4",
        className
      )}
      onClick={handleCardClick}
      role="button"
      tabIndex={0}
      aria-label={`View details for ${condition.title}`}
    >
      {/* Component content */}
    </Card>
  )
}

// 7. Default export (if applicable)
export default MedicalConditionCard
```

### Props Interface Guidelines

```typescript
// Base props for all components
interface BaseComponentProps {
  className?: string
  children?: React.ReactNode
  'data-testid'?: string
}

// Extend base props for specific components
interface ButtonProps extends BaseComponentProps {
  variant?: 'primary' | 'secondary' | 'outline'
  size?: 'sm' | 'md' | 'lg'
  disabled?: boolean
  loading?: boolean
  onClick?: () => void
  type?: 'button' | 'submit' | 'reset'
}

// Use discriminated unions for complex variants
type CardVariant = 
  | { variant: 'medical'; condition: MedicalCondition }
  | { variant: 'location'; location: LocationData }
  | { variant: 'service'; service: ServiceData }

interface CardProps extends BaseComponentProps {
  title: string
  description?: string
} & CardVariant
```

### Error Handling Pattern

```typescript
export const SafeMedicalComponent: React.FC<Props> = ({ data }) => {
  const [error, setError] = useState<Error | null>(null)
  const [loading, setLoading] = useState(true)

  // Error boundary for component-level errors
  if (error) {
    return (
      <div className="error-fallback" role="alert">
        <h3>Something went wrong</h3>
        <p>{error.message}</p>
        <Button onClick={() => setError(null)}>Try Again</Button>
      </div>
    )
  }

  // Loading state
  if (loading) {
    return <div className="loading-spinner" aria-label="Loading content" />
  }

  // Main content
  return <div>{/* Component content */}</div>
}
```

## 📝 TypeScript Guidelines

### Strict Configuration

```typescript
// tsconfig.json requirements
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true
  }
}
```

### Type Definitions

```typescript
// Use specific types instead of 'any'
// ❌ Bad
const data: any = fetchData()

// ✅ Good
interface ApiResponse<T> {
  data: T
  status: number
  message: string
}
const data: ApiResponse<MedicalCondition[]> = fetchData()

// Use union types for controlled values
type ConditionSeverity = 'mild' | 'moderate' | 'severe'
type PageStatus = 'loading' | 'success' | 'error'

// Use generic types for reusable interfaces
interface DataState<T> {
  data: T | null
  loading: boolean
  error: Error | null
}

// Use utility types for transformations
type PartialCondition = Partial<MedicalCondition>
type ConditionKeys = keyof MedicalCondition
type RequiredCondition = Required<Pick<MedicalCondition, 'id' | 'title'>>
```

### Hook Typing

```typescript
// Custom hook with proper typing
export const useApiData = <T>(url: string): DataState<T> => {
  const [state, setState] = useState<DataState<T>>({
    data: null,
    loading: true,
    error: null
  })

  useEffect(() => {
    const fetchData = async () => {
      try {
        setState(prev => ({ ...prev, loading: true }))
        const response = await fetch(url)
        const data: T = await response.json()
        setState({ data, loading: false, error: null })
      } catch (error) {
        setState({ data: null, loading: false, error: error as Error })
      }
    }

    fetchData()
  }, [url])

  return state
}
```

## 🎨 Styling Standards

### Tailwind CSS Guidelines

```typescript
// Use utility classes with consistent patterns
const cardStyles = cn(
  // Layout
  "flex flex-col",
  "w-full max-w-md",
  "p-6 gap-4",
  
  // Visual
  "bg-card border border-border",
  "rounded-lg shadow-sm",
  
  // Interactive
  "hover:shadow-md hover:scale-[1.02]",
  "transition-all duration-200",
  
  // Responsive
  "sm:p-8 sm:gap-6",
  "md:max-w-lg",
  
  // Conditional
  isActive && "ring-2 ring-primary",
  disabled && "opacity-50 pointer-events-none"
)

// Use CSS custom properties for theming
// globals.css
:root {
  --color-primary: 220 100% 50%;
  --color-primary-foreground: 0 0% 100%;
  --spacing-mobile-sm: 0.5rem;
  --spacing-mobile-md: 1rem;
}

// Component-specific styles when needed
.medical-card {
  @apply bg-gradient-to-br from-card to-card/80;
  @apply border-l-4 border-l-primary/20;
}
```

### Responsive Design Patterns

```typescript
// Mobile-first responsive design
const ResponsiveGrid: React.FC = ({ children }) => {
  const { isMobile, isTablet } = useDeviceDetection()
  
  return (
    <div className={cn(
      "grid gap-6",
      isMobile && "grid-cols-1",
      isTablet && "grid-cols-2",
      !isMobile && !isTablet && "grid-cols-3 lg:grid-cols-4"
    )}>
      {children}
    </div>
  )
}

// Touch-friendly sizing
const TouchButton: React.FC = ({ children, ...props }) => (
  <Button
    className={cn(
      "min-h-[44px] min-w-[44px]", // WCAG touch target size
      "px-mobile-md py-mobile-sm", // Mobile-optimized padding
      "text-mobile-sm sm:text-sm"   // Responsive text size
    )}
    {...props}
  >
    {children}
  </Button>
)
```

## 🧪 Testing Requirements

### Component Testing

```typescript
// Component test structure
import { render, screen, fireEvent } from '@testing-library/react'
import { vi } from 'vitest'
import { MedicalConditionCard } from './MedicalConditionCard'

describe('MedicalConditionCard', () => {
  const mockCondition = {
    id: 'test-condition',
    title: 'Test Condition',
    description: 'Test description',
    category: 'spine' as const,
    symptoms: ['symptom1', 'symptom2']
  }

  it('renders condition information correctly', () => {
    render(<MedicalConditionCard condition={mockCondition} />)
    
    expect(screen.getByText('Test Condition')).toBeInTheDocument()
    expect(screen.getByText('Test description')).toBeInTheDocument()
  })

  it('handles click events', () => {
    const onSelect = vi.fn()
    render(
      <MedicalConditionCard 
        condition={mockCondition} 
        onSelect={onSelect} 
      />
    )
    
    fireEvent.click(screen.getByRole('button'))
    expect(onSelect).toHaveBeenCalledWith(mockCondition)
  })

  it('meets accessibility requirements', () => {
    render(<MedicalConditionCard condition={mockCondition} />)
    
    const card = screen.getByRole('button')
    expect(card).toHaveAttribute('aria-label', 'View details for Test Condition')
    expect(card).toHaveAttribute('tabIndex', '0')
  })
})
```

### Testing Coverage Requirements

- **Unit Tests**: All utility functions and hooks
- **Component Tests**: All UI components with props and interactions
- **Integration Tests**: Page components and user workflows
- **Accessibility Tests**: WCAG compliance for all interactive elements
- **Performance Tests**: Bundle size and loading metrics

## ⚡ Performance Guidelines

### Code Splitting

```typescript
// Lazy load page components
const MedicalConditionsPage = lazy(() => import('@/pages/MedicalConditions'))
const LocationPage = lazy(() => import('@/pages/Location'))

// Lazy load heavy components
const ComplexChart = lazy(() => import('@/components/ComplexChart'))

// Use Suspense with fallbacks
<Suspense fallback={<LoadingSpinner />}>
  <MedicalConditionsPage />
</Suspense>
```

### Optimization Patterns

```typescript
// Memoize expensive calculations
const expensiveValue = useMemo(() => {
  return heavyCalculation(data)
}, [data])

// Memoize components to prevent unnecessary re-renders
const MemoizedCard = memo(({ condition }: { condition: MedicalCondition }) => {
  return <Card>{condition.title}</Card>
})

// Use callback for stable function references
const handleClick = useCallback((id: string) => {
  onConditionSelect(id)
}, [onConditionSelect])

// Optimize images
<img 
  src="/images/condition.jpg"
  alt="Medical condition illustration"
  loading="lazy"
  width={400}
  height={300}
/>
```

## ♿ Accessibility Standards

### WCAG AA+ Compliance

```typescript
// Semantic HTML structure
<main role="main">
  <section aria-labelledby="conditions-heading">
    <h2 id="conditions-heading">Medical Conditions</h2>
    <ul role="list">
      {conditions.map(condition => (
        <li key={condition.id} role="listitem">
          <article aria-labelledby={`condition-${condition.id}`}>
            <h3 id={`condition-${condition.id}`}>{condition.title}</h3>
          </article>
        </li>
      ))}
    </ul>
  </section>
</main>

// Interactive elements
<Button
  aria-label="Book appointment for spine consultation"
  aria-describedby="appointment-help"
>
  Book Appointment
</Button>
<div id="appointment-help" className="sr-only">
  Opens appointment booking form in a new window
</div>

// Form accessibility
<div className="form-field">
  <Label htmlFor="patient-name">Patient Name *</Label>
  <Input
    id="patient-name"
    required
    aria-invalid={hasError}
    aria-describedby={hasError ? "name-error" : undefined}
  />
  {hasError && (
    <p id="name-error" role="alert" className="error-message">
      Patient name is required
    </p>
  )}
</div>
```

### Keyboard Navigation

```typescript
// Keyboard event handling
const handleKeyDown = (event: KeyboardEvent) => {
  switch (event.key) {
    case 'Enter':
    case ' ':
      event.preventDefault()
      handleClick()
      break
    case 'Escape':
      handleClose()
      break
  }
}

// Focus management
const focusableElements = 'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'

const trapFocus = (container: HTMLElement) => {
  const focusable = container.querySelectorAll(focusableElements)
  const firstFocusable = focusable[0] as HTMLElement
  const lastFocusable = focusable[focusable.length - 1] as HTMLElement

  firstFocusable?.focus()
}
```

## 🔄 Git Workflow

### Branch Naming

```bash
# Feature branches
feature/medical-condition-search
feature/appointment-booking-form

# Bug fixes
fix/navigation-mobile-menu
fix/accessibility-focus-trap

# Documentation
docs/component-library-update
docs/api-documentation

# Refactoring
refactor/patient-data-structure
refactor/styling-system-update
```

### Commit Messages

```bash
# Format: type(scope): description
feat(conditions): add search functionality for medical conditions
fix(navigation): resolve mobile menu accessibility issues
docs(components): update component library documentation
refactor(data): restructure patient resource data types
test(forms): add comprehensive form validation tests
perf(images): optimize medical condition illustrations
style(ui): update button component styling consistency
```

### Pull Request Guidelines

1. **Clear Description**: Explain what changes were made and why
2. **Testing**: Include test results and coverage information
3. **Screenshots**: For UI changes, include before/after screenshots
4. **Accessibility**: Confirm WCAG compliance for new features
5. **Performance**: Note any performance impacts
6. **Documentation**: Update relevant documentation
7. **Breaking Changes**: Clearly mark any breaking changes

## 📚 Additional Resources

- [React Best Practices](https://react.dev/learn) - Official React documentation
- [TypeScript Handbook](https://www.typescriptlang.org/docs/) - TypeScript guidelines
- [Tailwind CSS Documentation](https://tailwindcss.com/docs) - Styling utilities
- [WCAG Guidelines](https://www.w3.org/WAI/WCAG21/quickref/) - Accessibility standards
- [Testing Library Documentation](https://testing-library.com/) - Testing best practices
