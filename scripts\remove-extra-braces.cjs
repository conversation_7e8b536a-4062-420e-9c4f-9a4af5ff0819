const fs = require('fs');
const path = require('path');

// Function to recursively find all TypeScript and TSX files
function findTsFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      if (!['node_modules', '.git', 'dist', 'build'].includes(file)) {
        findTsFiles(filePath, fileList);
      }
    } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// Function to remove extra closing braces that cause "Expected ;" errors
function removeExtraClosingBraces(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Split content into lines for analysis
    const lines = content.split('\n');
    const cleanedLines = [];
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const nextLine = i < lines.length - 1 ? lines[i + 1] : '';
      const lineAfterNext = i < lines.length - 2 ? lines[i + 2] : '';
      const lineAfterThat = i < lines.length - 3 ? lines[i + 3] : '';
      
      // Pattern: "  }\n  }\n\n\n  /**" - extra closing brace before method
      if (line.trim() === '}' && 
          nextLine.trim() === '}' && 
          lineAfterNext.trim() === '' &&
          lineAfterThat.trim() === '' &&
          i + 4 < lines.length &&
          lines[i + 4].trim().startsWith('/**')) {
        
        // Skip the extra closing brace
        cleanedLines.push(line);
        // Skip the extra brace (nextLine)
        modified = true;
        console.log(`Removed extra closing brace in: ${filePath} at line ${i + 2}`);
        continue;
      }
      
      cleanedLines.push(line);
    }
    
    if (modified) {
      const newContent = cleanedLines.join('\n');
      fs.writeFileSync(filePath, newContent, 'utf8');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Main execution
console.log('🔧 Removing extra closing braces...');

// Focus on the problematic files
const problematicFiles = [
  'src/lib/security.ts',
  'src/lib/mobile-optimization.ts',
  'src/lib/performance.ts',
  'src/lib/env-validation.ts'
];

let fixedCount = 0;

problematicFiles.forEach(filePath => {
  const fullPath = path.join(process.cwd(), filePath);
  if (fs.existsSync(fullPath)) {
    if (removeExtraClosingBraces(fullPath)) {
      fixedCount++;
    }
  }
});

console.log(`\n✅ Processed ${problematicFiles.length} files`);
console.log(`🔧 Removed extra braces in ${fixedCount} files`);

if (fixedCount > 0) {
  console.log('\n🎯 Next steps:');
  console.log('1. Run npm run build to test the build process');
}
