import React from 'react';
import { Helmet } from 'react-helmet-async';

import { StandardPageLayout } from '@/components/layout';
import {
  LocationHero,
  LocationContactInfo,
  LocationMap,
  LocationFacilities,
  LocationAmenities,
  TherapeuticInterventions,
  NearbyAmenities,
  NearbyHospitals,
  OtherConsultingLocations,
  PatientsPrivacy,
  LocationCTA
} from '@/components/locations';
import { mooneePondsLocationData, mooneePondsSpecialSections } from '@/data/locations/mooneePondsData';

/**
 * Moonee Ponds Location Page - Refactored
 * Complete refactoring preserving ALL content from the original 709-line file
 * Modular architecture with comprehensive data preservation
 */
const MooneePondsLocation: React.FC = () => {
  return (
    <>
      <Helmet>
        <title>Moonee Ponds Neurosurgeon | Moonee Ponds Specialist Centre | Dr <PERSON><PERSON></title>
        <meta
          name="description"
          content="Expert neurosurgery and spine care at Moonee Ponds Specialist Centre. Dr <PERSON><PERSON> provides comprehensive neurosurgical consultations and treatments. Book your appointment today."
        />
        <meta name="keywords" content="neurosurgeon moonee ponds, spine surgeon moonee ponds, moonee ponds specialist centre, neurosurgery moonee ponds, spine surgery moonee ponds" />
        <link rel="canonical" href="https://mineuro.com.au/locations/moonee-ponds" />
      </Helmet>

      <StandardPageLayout>
        <LocationHero
          title={mooneePondsLocationData.hero.title}
          subtitle={mooneePondsLocationData.hero.subtitle}
          introduction1={mooneePondsLocationData.hero.introduction1}
          introduction2={mooneePondsLocationData.hero.introduction2}
          imageUrl={mooneePondsLocationData.hero.imageUrl}
        />

        <div className="py-16 bg-muted/30">
          <div className="container">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <LocationContactInfo
                address={mooneePondsLocationData.contact.address}
                phone={mooneePondsLocationData.contact.phone}
                email={mooneePondsLocationData.contact.email}
                hours={mooneePondsLocationData.contact.hours}
                consultingHours={mooneePondsLocationData.contact.consultingHours}
                appointmentProcess={mooneePondsLocationData.contact.appointmentProcess}
              />

              <LocationMap
                embedUrl={mooneePondsLocationData.map.embedUrl}
                title={mooneePondsLocationData.map.title}
                transportOptions={mooneePondsLocationData.map.transportOptions}
                gettingHereTitle={mooneePondsLocationData.map.gettingHereTitle}
                publicTransportTitle={mooneePondsLocationData.map.publicTransportTitle}
                carTitle={mooneePondsLocationData.map.carTitle}
              />
            </div>
          </div>
        </div>

        {mooneePondsLocationData.therapeuticInterventions && (
          <TherapeuticInterventions
            title={mooneePondsLocationData.therapeuticInterventions.title}
            subtitle={mooneePondsLocationData.therapeuticInterventions.subtitle}
            description={mooneePondsLocationData.therapeuticInterventions.description}
            interventions={mooneePondsLocationData.therapeuticInterventions.interventions}
          />
        )}

        <LocationFacilities
          title={mooneePondsLocationData.facilities.title}
          subtitle={mooneePondsLocationData.facilities.subtitle}
          description={mooneePondsLocationData.facilities.description}
          facilities={mooneePondsLocationData.facilities.facilities}
          gallery={mooneePondsLocationData.facilities.gallery}
        />

        <LocationAmenities
          title={mooneePondsLocationData.amenities.title}
          description={mooneePondsLocationData.amenities.description}
          locationDetails={mooneePondsLocationData.amenities.locationDetails}
          medicalFacilities={mooneePondsLocationData.amenities.medicalFacilities}
          surroundingAmenities={mooneePondsLocationData.amenities.surroundingAmenities}
          transportation={mooneePondsLocationData.amenities.transportation}
          parking={mooneePondsLocationData.amenities.parking}
          images={mooneePondsLocationData.amenities.images}
        />

        <NearbyAmenities
          title={mooneePondsLocationData.nearbyAmenities.title}
          subtitle={mooneePondsLocationData.nearbyAmenities.subtitle}
          description={mooneePondsLocationData.nearbyAmenities.description}
          categories={mooneePondsLocationData.nearbyAmenities.categories}
        />

        <OtherConsultingLocations
          title={mooneePondsLocationData.otherLocations.title}
          subtitle={mooneePondsLocationData.otherLocations.subtitle}
          description={mooneePondsLocationData.otherLocations.description}
          locations={mooneePondsLocationData.otherLocations.locations}
        />

        <NearbyHospitals
          title={mooneePondsLocationData.nearbyHospitals.title}
          subtitle={mooneePondsLocationData.nearbyHospitals.subtitle}
          description={mooneePondsLocationData.nearbyHospitals.description}
          hospitals={mooneePondsLocationData.nearbyHospitals.hospitals}
        />

        <PatientsPrivacy
          title={mooneePondsSpecialSections.patientsPrivacy.title}
          subtitle={mooneePondsSpecialSections.patientsPrivacy.subtitle}
          description1={mooneePondsSpecialSections.patientsPrivacy.description1}
          description2={mooneePondsSpecialSections.patientsPrivacy.description2}
          description3={mooneePondsSpecialSections.patientsPrivacy.description3}
          drAliashkevichLink={mooneePondsSpecialSections.patientsPrivacy.drAliashkevichLink}
        />

        <LocationCTA
          title={mooneePondsLocationData.cta.title}
          description={mooneePondsLocationData.cta.description}
          buttons={mooneePondsLocationData.cta.buttons}
        />
      </StandardPageLayout>
    </>
  );
};

MooneePondsLocation.displayName = 'MooneePondsLocation';

export default MooneePondsLocation;
