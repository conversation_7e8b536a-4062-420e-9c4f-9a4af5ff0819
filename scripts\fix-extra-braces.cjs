const fs = require('fs');
const path = require('path');

// Function to recursively find all TypeScript and TSX files
function findTsFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      if (!['node_modules', '.git', 'dist', 'build'].includes(file)) {
        findTsFiles(filePath, fileList);
      }
    } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// Function to fix extra closing braces in a file
function fixExtraBraces(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Pattern to detect extra closing braces after interface/type declarations
    const extraBracePattern = /}\s*\n\s*}\s*\n\s*$/gm;
    if (extraBracePattern.test(content)) {
      content = content.replace(extraBracePattern, '}\n\n');
      modified = true;
      console.log(`Fixed extra closing brace at end in: ${filePath}`);
    }
    
    // Pattern to detect extra closing braces after interface/type declarations (not at end)
    const extraBraceMiddlePattern = /}\s*\n\s*}\s*\n\s*\n/g;
    if (extraBraceMiddlePattern.test(content)) {
      content = content.replace(extraBraceMiddlePattern, '}\n\n');
      modified = true;
      console.log(`Fixed extra closing brace in middle in: ${filePath}`);
    }
    
    // Pattern to detect orphaned closing braces
    const orphanedBracePattern = /^\s*}\s*$/gm;
    const lines = content.split('\n');
    let hasOrphanedBrace = false;
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const prevLine = i > 0 ? lines[i - 1] : '';
      const nextLine = i < lines.length - 1 ? lines[i + 1] : '';
      
      // Check if this line is just a closing brace
      if (/^\s*}\s*$/.test(line)) {
        // Check if the previous line already ends with a closing brace
        if (/}\s*$/.test(prevLine.trim()) && nextLine.trim() === '') {
          lines.splice(i, 1);
          hasOrphanedBrace = true;
          modified = true;
          i--; // Adjust index after removal
        }
      }
    }
    
    if (hasOrphanedBrace) {
      content = lines.join('\n');
      console.log(`Fixed orphaned closing braces in: ${filePath}`);
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Main execution
console.log('🔧 Fixing extra closing braces...');

const srcDir = path.join(process.cwd(), 'src');
const tsFiles = findTsFiles(srcDir);

let fixedCount = 0;
let totalFiles = tsFiles.length;

tsFiles.forEach(filePath => {
  if (fixExtraBraces(filePath)) {
    fixedCount++;
  }
});

console.log(`\n✅ Processed ${totalFiles} files`);
console.log(`🔧 Fixed extra braces in ${fixedCount} files`);

if (fixedCount > 0) {
  console.log('\n🎯 Next steps:');
  console.log('1. Run npm run build to test the build process');
  console.log('2. Run npm run type-check to verify TypeScript compilation');
}
