# miNEURO Website - Architecture Overview

**Document Version**: 3.0  
**Last Updated**: 2025-01-19  
**Status**: ✅ **CURRENT AND ACCURATE**

## 🏗️ System Architecture

### Overview
The miNEURO website is a comprehensive medical platform built with modern web technologies, designed for optimal performance, accessibility, and maintainability. The architecture follows medical-grade quality standards with robust error handling, comprehensive testing, and professional design patterns.

### Architectural Principles

1. **Medical-Grade Reliability**
   - Robust error boundaries and fallback mechanisms
   - Comprehensive accessibility (WCAG AA+ compliance)
   - Performance optimization for all devices and networks
   - Secure coding practices and data handling

2. **Maintainable Codebase**
   - Modular component architecture
   - Strict TypeScript implementation
   - Comprehensive testing coverage
   - Living documentation system

3. **Professional User Experience**
   - Mobile-first responsive design
   - Fast loading and smooth interactions
   - Universal design principles
   - Medical industry design standards

## 🛠️ Technology Stack

### Core Technologies
- **Frontend Framework**: React 18.3.1 with TypeScript 5.7.2
- **Build Tool**: Vite 6.3.5 with optimized configuration
- **Styling**: Tailwind CSS 3.4.11 with custom medical theme
- **UI Components**: Radix UI primitives with custom styling layer
- **Routing**: React Router DOM 7.6.3 with lazy loading
- **State Management**: React Context + Custom Hooks pattern
- **Testing**: Vitest + React Testing Library + Accessibility testing

### Development Tools
- **Package Manager**: npm with lock file management
- **Code Quality**: ESLint + Prettier with medical industry standards
- **Git Hooks**: Husky for pre-commit validation
- **Type Checking**: Strict TypeScript configuration
- **Performance**: Bundle analysis and optimization tools

### Build and Deployment
- **Development**: Hot module replacement with Vite
- **Production**: Optimized builds with code splitting
- **Testing**: Comprehensive test suites with coverage reporting
- **Quality Assurance**: Automated linting, type checking, and testing

## 📁 Project Structure

```
vas-41/
├── public/                 # Static assets and meta files
│   ├── images/            # Optimized medical images and illustrations
│   ├── icons/             # Icon assets and favicons
│   └── meta/              # SEO and social media assets
├── src/
│   ├── components/         # React component library
│   │   ├── ui/            # Base UI components (buttons, cards, tabs)
│   │   ├── shared/        # Shared business logic components
│   │   ├── layout/        # Layout and structural components
│   │   ├── medical-conditions/  # Medical condition components
│   │   ├── patient-resources/   # Patient education components
│   │   ├── appointments/  # Appointment booking components
│   │   └── [feature]/     # Feature-specific component groups
│   ├── pages/             # Page components (route handlers)
│   │   ├── medical-conditions/  # Condition detail pages
│   │   ├── patient-resources/   # Educational resource pages
│   │   ├── locations/     # Location-specific pages
│   │   └── [category]/    # Categorized page groups
│   ├── contexts/          # React context providers
│   │   ├── DeviceContext.tsx    # Device detection and responsive state
│   │   ├── LanguageContext.tsx  # Internationalization context
│   │   └── ThemeContext.tsx     # Theme and styling context
│   ├── hooks/             # Custom React hooks
│   │   ├── useDeviceDetection.ts # Device and screen size detection
│   │   ├── useSEO.ts      # SEO metadata management
│   │   └── useApiData.ts  # Data fetching and state management
│   ├── lib/               # Utility libraries and configurations
│   │   ├── utils.ts       # General utility functions
│   │   ├── constants.ts   # Application constants
│   │   └── validations.ts # Data validation utilities
│   ├── data/              # Static data and content management
│   │   ├── conditions/    # Medical condition data
│   │   ├── locations/     # Clinic location information
│   │   ├── patient-resources/ # Educational content
│   │   ├── brain-spine-health.ts # Brain and spine health data
│   │   └── [category]/    # Categorized data files
│   ├── types/             # TypeScript type definitions
│   │   ├── common.ts      # Shared type definitions
│   │   ├── medical.ts     # Medical-specific types
│   │   ├── location.ts    # Location data types
│   │   └── [domain].ts    # Domain-specific type files
│   ├── routes/            # Route definitions and configuration
│   │   ├── route-definitions.ts # Central route configuration
│   │   └── route-guards.ts      # Route protection logic
│   └── tests/             # Test utilities and configurations
│       ├── setup.ts       # Test environment setup
│       ├── mocks/         # Mock data and utilities
│       └── utils/         # Testing utility functions
├── docs/                  # Comprehensive documentation
│   ├── README.md          # Main project documentation
│   ├── COMPONENT_LIBRARY.md     # Component documentation
│   ├── API_DATA_DOCUMENTATION.md # Data structure documentation
│   ├── DEVELOPMENT_GUIDELINES.md # Coding standards
│   ├── ARCHITECTURE_OVERVIEW.md  # This document
│   └── [topic]/           # Topic-specific documentation
└── Configuration Files    # Build, linting, and deployment configs
    ├── package.json       # Dependencies and scripts
    ├── tsconfig.json      # TypeScript configuration
    ├── tailwind.config.ts # Tailwind CSS configuration
    ├── vite.config.ts     # Vite build configuration
    ├── vitest.config.ts   # Testing configuration
    └── eslint.config.js   # Linting configuration
```

## 🧱 Component Architecture

### Component Hierarchy

```
StandardPageLayout (Root Layout)
├── Navbar (Navigation)
├── Main Content Area
│   ├── Page-Specific Components
│   │   ├── Section Headers
│   │   ├── Content Cards
│   │   ├── Interactive Elements
│   │   └── Call-to-Action Components
│   └── Shared Components
│       ├── Error Boundaries
│       ├── Loading States
│       └── Accessibility Helpers
└── Footer (Site Footer)
```

### Component Categories

1. **Base UI Components** (`src/components/ui/`)
   - Button, Card, Input, Tabs, etc.
   - Radix UI primitives with custom styling
   - Fully accessible and responsive

2. **Layout Components** (`src/components/layout/`)
   - Page layouts and structural components
   - Responsive containers and grid systems
   - Navigation and footer components

3. **Shared Components** (`src/components/shared/`)
   - Reusable business logic components
   - Error boundaries and loading states
   - Common UI patterns and helpers

4. **Feature Components** (`src/components/[feature]/`)
   - Medical condition components
   - Patient resource components
   - Appointment booking components
   - Location-specific components

## 🔄 Data Flow Architecture

### Data Management Strategy

1. **Static Data**: TypeScript files in `src/data/`
   - Medical condition information
   - Location details
   - Educational content
   - Navigation structure

2. **State Management**: React Context + Hooks
   - Device detection context
   - Theme and language context
   - Component-level state with hooks

3. **Type Safety**: Comprehensive TypeScript
   - Strict type checking
   - Interface definitions for all data
   - Runtime validation where needed

### Data Flow Pattern

```
Data Files (src/data/) 
    ↓
Type Definitions (src/types/)
    ↓
Custom Hooks (src/hooks/)
    ↓
Context Providers (src/contexts/)
    ↓
Components (src/components/)
    ↓
Pages (src/pages/)
```

## 🎨 Design System

### Theme Architecture

1. **CSS Custom Properties**: Defined in `globals.css`
   - Color system with light/dark mode support
   - Spacing scale with mobile-optimized variants
   - Typography scale with medical-appropriate fonts

2. **Tailwind Configuration**: Extended in `tailwind.config.ts`
   - Custom color palette for medical applications
   - Responsive breakpoints and utilities
   - Component-specific utility classes

3. **Component Styling**: Consistent patterns
   - Utility-first approach with Tailwind CSS
   - Component variants using `cn()` utility
   - Responsive design with mobile-first approach

### Accessibility Architecture

1. **WCAG AA+ Compliance**
   - Semantic HTML structure
   - Proper ARIA labels and roles
   - Keyboard navigation support
   - Screen reader optimization

2. **Testing Strategy**
   - Automated accessibility testing
   - Manual testing with screen readers
   - Keyboard navigation validation
   - Color contrast verification

## 🚀 Performance Architecture

### Optimization Strategies

1. **Code Splitting**
   - Lazy loading for page components
   - Dynamic imports for heavy components
   - Route-based code splitting

2. **Bundle Optimization**
   - Tree shaking for unused code
   - Minification and compression
   - Asset optimization and caching

3. **Runtime Performance**
   - React.memo for expensive components
   - useMemo and useCallback for optimization
   - Efficient re-rendering patterns

### Loading Strategies

1. **Progressive Loading**
   - Critical CSS inlined
   - Non-critical resources deferred
   - Image lazy loading

2. **Caching Strategy**
   - Static asset caching
   - Service worker implementation (future)
   - Browser caching optimization

## 🧪 Testing Architecture

### Testing Strategy

1. **Unit Testing**
   - Component testing with React Testing Library
   - Hook testing with custom test utilities
   - Utility function testing

2. **Integration Testing**
   - Page component testing
   - User workflow testing
   - API integration testing

3. **Accessibility Testing**
   - Automated a11y testing
   - Screen reader testing
   - Keyboard navigation testing

4. **Performance Testing**
   - Bundle size monitoring
   - Loading performance testing
   - Runtime performance profiling

### Test Organization

```
src/tests/
├── setup.ts              # Test environment configuration
├── mocks/                 # Mock data and utilities
├── utils/                 # Testing utility functions
└── __tests__/             # Test files organized by feature
    ├── components/        # Component tests
    ├── hooks/             # Hook tests
    ├── pages/             # Page tests
    └── integration/       # Integration tests
```

## 🔧 Build and Deployment

### Development Workflow

1. **Local Development**
   - Hot module replacement with Vite
   - TypeScript type checking
   - ESLint and Prettier integration
   - Automated testing on file changes

2. **Quality Assurance**
   - Pre-commit hooks with Husky
   - Automated linting and formatting
   - Type checking and test execution
   - Build validation

3. **Production Build**
   - Optimized bundle generation
   - Asset optimization and compression
   - Source map generation
   - Performance analysis

### Deployment Strategy

1. **Build Process**
   - TypeScript compilation
   - Asset optimization
   - Bundle analysis
   - Quality checks

2. **Production Optimization**
   - Minification and compression
   - Cache optimization
   - Performance monitoring
   - Error tracking

## 📚 Documentation Architecture

### Documentation Strategy

1. **Living Documentation**
   - Code-level documentation with JSDoc
   - Component library documentation
   - API and data structure documentation
   - Development guidelines and standards

2. **User Documentation**
   - Setup and installation guides
   - Development workflow documentation
   - Testing and deployment guides
   - Troubleshooting and FAQ

3. **Maintenance Documentation**
   - Architecture overview (this document)
   - Design system documentation
   - Performance optimization guides
   - Security and accessibility guidelines

## 🔮 Future Considerations

### Planned Enhancements

1. **Internationalization**
   - Multi-language support infrastructure
   - Content management for translations
   - Locale-specific formatting

2. **Advanced Features**
   - Progressive Web App capabilities
   - Offline functionality
   - Advanced search capabilities
   - User personalization

3. **Performance Improvements**
   - Service worker implementation
   - Advanced caching strategies
   - Image optimization pipeline
   - CDN integration

### Scalability Considerations

1. **Code Organization**
   - Micro-frontend architecture consideration
   - Component library extraction
   - Shared utility packages

2. **Performance Scaling**
   - Bundle size optimization
   - Runtime performance monitoring
   - Advanced caching strategies
   - CDN and edge computing

This architecture provides a solid foundation for the miNEURO website while maintaining flexibility for future enhancements and scalability requirements.
