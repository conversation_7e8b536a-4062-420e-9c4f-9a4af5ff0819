import React from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Textarea } from '@/components/ui/textarea';

// TODO: Add explicit return types to functions

// TODO: Extract inline object types to interfaces
// Consider creating interfaces for complex object types


interface FormField {
  name: string;
  label: string;
  type: string;
  placeholder: string;
  options?: { value: string; label: string }[];
}

interface AppointmentRequestFormProps {
  title: string;
  fields: FormField[];
  submitText: string;
  confirmationText: string;
}

const AppointmentRequestForm: React.FC<AppointmentRequestFormProps> = ({
  title,
  fields,
  submitText,
  confirmationText
}) => {
  const renderField = (field: FormField) => {
    const fieldId = field.name;

    switch (field.type) {
      case 'select':
        return (
          <div key={field.name} className="space-y-2">
            <Label htmlFor={fieldId}>{field.label}</Label>
            <Select>
              <SelectTrigger id={fieldId}>
                <SelectValue placeholder={field.placeholder} />
              </SelectTrigger>
              <SelectContent>
                {field.options?.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        );

      case 'textarea':
        return (
          <div key={field.name} className="space-y-2">
            <Label htmlFor={fieldId}>{field.label}</Label>
            <Textarea id={fieldId} placeholder={field.placeholder} />
          </div>
        );

      default:
        return (
          <div key={field.name} className="space-y-2">
            <Label htmlFor={fieldId}>{field.label}</Label>
            <Input 
              id={fieldId} 
              type={field.type} 
              placeholder={field.placeholder} 
            />
          </div>
        );
    }
  };

  return (
    <div>
      <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{title}</h2>
      <Card className="shadow-md">
        <CardContent className="p-6">
          <form className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {fields.slice(0, 2).map(renderField)}
            </div>
            
            {fields.slice(2).map(renderField)}

            <Button type="submit" className="w-full">
              {submitText}
            </Button>

            <p className="text-sm text-enhanced-muted text-center mt-4">
              {confirmationText}
            </p>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default AppointmentRequestForm;
