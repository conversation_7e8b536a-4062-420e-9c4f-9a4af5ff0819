const fs = require('fs');
const path = require('path');

// Function to recursively find all TypeScript and TSX files
function findTsFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      if (!['node_modules', '.git', 'dist', 'build'].includes(file)) {
        findTsFiles(filePath, fileList);
      }
    } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// Function to fix extra commas in a file
function fixExtraCommas(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Fix semicolon followed by comma (;,)
    const semicolonCommaPattern = /;,/g;
    if (semicolonCommaPattern.test(content)) {
      content = content.replace(/;,/g, ';');
      modified = true;
      console.log(`Fixed semicolon-comma in: ${filePath}`);
    }
    
    // Fix comma before closing brace
    const commaBeforeCloseBracePattern = /,(\s*})/g;
    if (commaBeforeCloseBracePattern.test(content)) {
      content = content.replace(/,(\s*})/g, '$1');
      modified = true;
      console.log(`Fixed comma before closing brace in: ${filePath}`);
    }
    
    // Fix comma before semicolon
    const commaBeforeSemicolonPattern = /,(\s*;)/g;
    if (commaBeforeSemicolonPattern.test(content)) {
      content = content.replace(/,(\s*;)/g, '$1');
      modified = true;
      console.log(`Fixed comma before semicolon in: ${filePath}`);
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Main execution
console.log('🔧 Fixing extra commas...');

const srcDir = path.join(process.cwd(), 'src');
const tsFiles = findTsFiles(srcDir);

let fixedCount = 0;
let totalFiles = tsFiles.length;

tsFiles.forEach(filePath => {
  if (fixExtraCommas(filePath)) {
    fixedCount++;
  }
});

console.log(`\n✅ Processed ${totalFiles} files`);
console.log(`🔧 Fixed extra commas in ${fixedCount} files`);

if (fixedCount > 0) {
  console.log('\n🎯 Next steps:');
  console.log('1. Run npm run build to test the build process');
  console.log('2. Run npm run type-check to verify TypeScript compilation');
}
