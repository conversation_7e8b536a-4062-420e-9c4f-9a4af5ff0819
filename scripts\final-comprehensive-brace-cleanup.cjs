const fs = require('fs');
const path = require('path');

// Function to recursively find all TypeScript and TSX files
function findTsFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      if (!['node_modules', '.git', 'dist', 'build'].includes(file)) {
        findTsFiles(filePath, fileList);
      }
    } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// Function to remove all extra closing braces that cause "Expected ;" errors
function removeAllExtraClosingBraces(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Pattern 1: Remove "  }\n  }" patterns before method declarations
    const pattern1 = /(\s*})\s*\n\s*}\s*\n\s*\n\s*\n\s*\/\*\*/g;
    if (pattern1.test(content)) {
      content = content.replace(pattern1, '$1\n\n\n  /**');
      modified = true;
      console.log(`Fixed pattern 1 in: ${filePath}`);
    }
    
    // Pattern 2: Remove standalone "  }" lines that appear after method endings
    const lines = content.split('\n');
    const cleanedLines = [];
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const nextLine = i < lines.length - 1 ? lines[i + 1] : '';
      const lineAfterNext = i < lines.length - 2 ? lines[i + 2] : '';
      const lineAfterThat = i < lines.length - 3 ? lines[i + 3] : '';
      
      // Check for pattern: "  }\n  }\n\n\n  /**"
      if (line.trim() === '}' && 
          nextLine.trim() === '}' && 
          lineAfterNext.trim() === '' &&
          lineAfterThat.trim() === '' &&
          i + 4 < lines.length &&
          lines[i + 4].trim().startsWith('/**')) {
        
        // Keep the first closing brace, skip the extra one
        cleanedLines.push(line);
        // Skip the extra brace (nextLine)
        modified = true;
        console.log(`Removed extra closing brace in: ${filePath} at line ${i + 2}`);
        continue;
      }
      
      // Check for pattern: "  }\n  }\n\n" at end of file or before other content
      if (line.trim() === '}' && 
          nextLine.trim() === '}' && 
          (lineAfterNext.trim() === '' || i + 2 >= lines.length)) {
        
        // Keep the first closing brace, skip the extra one
        cleanedLines.push(line);
        // Skip the extra brace (nextLine)
        modified = true;
        console.log(`Removed extra closing brace at end in: ${filePath} at line ${i + 2}`);
        continue;
      }
      
      cleanedLines.push(line);
    }
    
    if (modified) {
      const newContent = cleanedLines.join('\n');
      fs.writeFileSync(filePath, newContent, 'utf8');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Main execution
console.log('🔧 Final comprehensive cleanup of all extra closing braces...');

// Focus on the problematic files
const problematicFiles = [
  'src/lib/security.ts',
  'src/lib/performance.ts',
  'src/lib/env-validation.ts',
  'src/lib/mobile-optimization.ts'
];

let fixedCount = 0;

problematicFiles.forEach(filePath => {
  const fullPath = path.join(process.cwd(), filePath);
  if (fs.existsSync(fullPath)) {
    if (removeAllExtraClosingBraces(fullPath)) {
      fixedCount++;
    }
  }
});

console.log(`\n✅ Processed ${problematicFiles.length} files`);
console.log(`🔧 Fixed extra braces in ${fixedCount} files`);

if (fixedCount > 0) {
  console.log('\n🎯 Next steps:');
  console.log('1. Run npm run build to test the build process');
} else {
  console.log('\n✅ No extra braces found');
}
