import { 
  <PERSON><PERSON><PERSON>, <PERSON>, Al<PERSON>Triangle, CheckCircle,
  Clock, Target, Lightbulb, Award
} from 'lucide-react';
import React, { useState } from 'react';

import SafeImage from '@/components/SafeImage';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface ErgonomicRecommendation {
  title: string;
  description: string;
  recommendations: string[];
  avoidActions: string[];
  equipment?: string[];
  imageUrl: string;
  priority: 'high' | 'medium' | 'low';
}

interface ErgonomicCategory {
  category: string;
  icon: unknown;
  description: string;
  recommendations: ErgonomicRecommendation[];
}

interface UlnarErgonomicGuideProps {
  ergonomicCategories: ErgonomicCategory[];
  className?: string;
}

const UlnarErgonomicGuide: React.FC<UlnarErgonomicGuideProps> = ({
  ergonomicCategories,
  className
}) => {
  const deviceInfo = useDeviceDetection();
  const [activeCategory, setActiveCategory] = useState<string>(ergonomicCategories[0]?.category || '');

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-error text-error-foreground';
      case 'medium': return 'bg-warning text-warning-foreground';
      case 'low': return 'bg-success text-success-foreground';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  const renderRecommendationCard = (recommendation: ErgonomicRecommendation, index: number) => (
    <Card key={index} className="medical-card">
      <CardHeader>
        <div className="flex items-start justify-between">
          <CardTitle className="text-enhanced-heading flex-1">
            {recommendation.title}
          </CardTitle>
          <Badge className={getPriorityColor(recommendation.priority)}>
            {recommendation.priority} priority
          </Badge>
        </div>
        <p className="text-enhanced-body text-sm">
          {recommendation.description}
        </p>
      </CardHeader>

      <CardContent>
        {/* Recommendation Image */}
        <div className="mb-6">
          <SafeImage
            src={recommendation.imageUrl}
            alt={`${recommendation.title} ergonomic setup`}
            className="w-full h-48 object-cover rounded-lg"
            fallbackSrc="https://images.pexels.com/photos/7688336/pexels-photo-7688336.jpeg?auto=compress&cs=tinysrgb&w=800"
          />
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          {/* Do's */}
          <div>
            <h4 className="text-enhanced-subheading mb-3 flex items-center gap-2">
              <CheckCircle className="w-4 h-4 text-success" />
              Recommended Actions
            </h4>
            <ul className="space-y-2">
              {recommendation.recommendations.map((rec, recIndex) => (
                <li key={recIndex} className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-success mt-0.5 flex-shrink-0" />
                  <span className="text-enhanced-body text-sm">{rec}</span>
                </li>
              ))}
            </ul>
          </div>

          {/* Don'ts */}
          <div>
            <h4 className="text-enhanced-subheading mb-3 flex items-center gap-2">
              <AlertTriangle className="w-4 h-4 text-foreground" />
              Actions to Avoid
            </h4>
            <ul className="space-y-2">
              {recommendation.avoidActions.map((avoid, avoidIndex) => (
                <li key={avoidIndex} className="flex items-start gap-2">
                  <AlertTriangle className="w-4 h-4 text-foreground mt-0.5 flex-shrink-0" />
                  <span className="text-enhanced-body text-sm">{avoid}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Equipment Recommendations */}
        {recommendation.equipment && recommendation.equipment.length > 0 && (
          <div className="mt-6 pt-6 border-t border-border">
            <h4 className="text-enhanced-subheading mb-3 flex items-center gap-2">
              <Settings className="w-4 h-4 text-info" />
              Recommended Equipment
            </h4>
            <ul className="space-y-2">
              {recommendation.equipment.map((equipment, equipIndex) => (
                <li key={equipIndex} className="flex items-start gap-2">
                  <Target className="w-4 h-4 text-info mt-0.5 flex-shrink-0" />
                  <span className="text-enhanced-body text-sm">{equipment}</span>
                </li>
              ))}
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );

  return (
    <section className={cn("section-background py-16", className)}>
      <div className="container mx-auto">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-enhanced-heading mb-4">
            Ergonomic Recommendations & Prevention
          </h2>
          <p className="text-enhanced-body max-w-3xl mx-auto leading-relaxed">
            Comprehensive ergonomic guidance to prevent ulnar nerve compression and reduce 
            symptoms through proper positioning and workplace modifications.
          </p>
        </div>

        {/* Quick Prevention Tips */}
        <Card className="medical-card border-l-4 border-l-success mb-12">
          <CardHeader>
            <CardTitle className="text-enhanced-heading flex items-center gap-3">
              <Lightbulb className="w-5 h-5 text-success" />
              Quick Prevention Tips
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-4 gap-4">
              <div className="text-center">
                <Clock className="w-8 h-8 text-primary mx-auto mb-2" />
                <h4 className="text-enhanced-subheading mb-1">Take Breaks</h4>
                <p className="text-enhanced-body text-sm">Every 30-60 minutes</p>
              </div>
              <div className="text-center">
                <Shield className="w-8 h-8 text-primary mx-auto mb-2" />
                <h4 className="text-enhanced-subheading mb-1">Avoid Pressure</h4>
                <p className="text-enhanced-body text-sm">On medial elbow</p>
              </div>
              <div className="text-center">
                <Target className="w-8 h-8 text-primary mx-auto mb-2" />
                <h4 className="text-enhanced-subheading mb-1">Neutral Position</h4>
                <p className="text-enhanced-body text-sm">Keep elbow straight</p>
              </div>
              <div className="text-center">
                <Award className="w-8 h-8 text-primary mx-auto mb-2" />
                <h4 className="text-enhanced-subheading mb-1">Strengthen</h4>
                <p className="text-enhanced-body text-sm">Regular exercises</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Ergonomic Categories */}
        <Tabs value={activeCategory} onValueChange={setActiveCategory} className="w-full">
          <TabsList className="grid w-full grid-cols-4 mb-8">
            {ergonomicCategories.map((category) => (
              <TabsTrigger key={category.category} value={category.category} className="flex items-center gap-2">
                <category.icon className="w-4 h-4" />
                {deviceInfo.isMobile ? category.category.split(' ')[0] : category.category}
              </TabsTrigger>
            ))}
          </TabsList>

          {ergonomicCategories.map((category) => (
            <TabsContent key={category.category} value={category.category}>
              <div className="mb-6">
                <h3 className="text-enhanced-subheading mb-2 flex items-center gap-3">
                  <category.icon className="w-5 h-5 text-primary" />
                  {category.category}
                </h3>
                <p className="text-enhanced-body">{category.description}</p>
              </div>
              <div className="grid lg:grid-cols-2 gap-6">
                {category.recommendations.map(renderRecommendationCard)}
              </div>
            </TabsContent>
          ))}
        </Tabs>

        {/* Daily Routine Checklist */}
        <Card className="medical-card border-l-4 border-l-info mt-12">
          <CardHeader>
            <CardTitle className="text-enhanced-heading flex items-center gap-3">
              <CheckCircle className="w-5 h-5 text-info" />
              Daily Ergonomic Checklist
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-6">
              <div>
                <h4 className="text-enhanced-subheading mb-3">Morning Setup</h4>
                <ul className="space-y-2">
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-success" />
                    <span className="text-enhanced-body text-sm">Adjust workstation height</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-success" />
                    <span className="text-enhanced-body text-sm">Position elbow pads</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-success" />
                    <span className="text-enhanced-body text-sm">Check chair armrest height</span>
                  </li>
                </ul>
              </div>
              <div>
                <h4 className="text-enhanced-subheading mb-3">During Work</h4>
                <ul className="space-y-2">
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-success" />
                    <span className="text-enhanced-body text-sm">Take hourly breaks</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-success" />
                    <span className="text-enhanced-body text-sm">Perform nerve glides</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-success" />
                    <span className="text-enhanced-body text-sm">Avoid leaning on elbows</span>
                  </li>
                </ul>
              </div>
              <div>
                <h4 className="text-enhanced-subheading mb-3">Evening Care</h4>
                <ul className="space-y-2">
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-success" />
                    <span className="text-enhanced-body text-sm">Gentle stretching</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-success" />
                    <span className="text-enhanced-body text-sm">Apply night splint if needed</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-success" />
                    <span className="text-enhanced-body text-sm">Review daily activities</span>
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default UlnarErgonomicGuide;
