import { Brain, Bone, Network, Zap } from 'lucide-react';
import React from 'react';

import SafeImage from '@/components/SafeImage';
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { TabsContent } from '@/components/ui/tabs';

interface AnatomicalStructure {
  id: string;
  name: string;
  description: string;
  function: string;
  pathophysiology: string;
  image: string;
  category: 'brain' | 'spine' | 'nerves';
}

interface AnatomicalStructuresSectionProps {
  structures: AnatomicalStructure[];
}

const AnatomicalStructuresSection: React.FC<AnatomicalStructuresSectionProps> = ({ structures }) => {
  const getIconForCategory = (category: string) => {
    switch (category) {
      case 'brain': return Brain;
      case 'spine': return Bone;
      case 'nerves': return Network;
      default: return Zap;
    }
  };

  const categorizedStructures = {
    brain: structures.filter(s => s.category === 'brain'),
    spine: structures.filter(s => s.category === 'spine'),
    nerves: structures.filter(s => s.category === 'nerves')
  };

  return (
    <TabsContent value="anatomy" className="mt-0 space-y-16">
      <div className="text-center space-y-6">
        <h3 className="text-3xl md:text-4xl font-bold text-foreground tracking-tight">
          Neuroanatomical Structures
        </h3>
        <p className="text-lg md:text-xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
          Understanding the anatomy of the nervous system is fundamental to comprehending
          neurological conditions and their treatments.
        </p>
        <div className="w-16 h-1 bg-gradient-to-r from-primary to-primary/50 mx-auto rounded-full"></div>
      </div>

      <div className="space-y-16">
        {Object.entries(categorizedStructures).map(([category, categoryStructures]) => (
        <div key={category} className="space-y-10">
          <div className="text-center space-y-4">
            <h4 className="text-2xl md:text-3xl font-bold text-primary capitalize">
              {category} Structures
            </h4>
            <div className="w-12 h-0.5 bg-primary/60 mx-auto rounded-full"></div>
          </div>
          <div className="grid gap-8 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
            {categoryStructures.map((structure) => {
              const Icon = getIconForCategory(structure.category);
              return (
                <Card key={structure.id} className="group h-full bg-card/90 backdrop-blur-sm border border-border/50 hover:border-primary/30 hover:shadow-2xl transition-all duration-500 hover:scale-[1.02] hover:-translate-y-2 overflow-hidden">
                  <div className="relative">
                    <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-bl from-primary/10 to-transparent rounded-bl-full"></div>
                    <CardHeader className="text-center pb-6">
                      <div className="flex items-center justify-center gap-4 mb-6">
                        <div className="p-4 rounded-full bg-gradient-to-br from-primary/15 to-primary/5 border border-primary/20">
                          <Icon className="h-7 w-7 text-primary" />
                        </div>
                        <CardTitle className="text-xl md:text-2xl font-bold text-primary leading-tight">
                          {structure.name}
                        </CardTitle>
                      </div>
                      <div className="relative overflow-hidden rounded-xl border border-border/30 shadow-lg group-hover:shadow-xl transition-all duration-500">
                        <SafeImage
                          src={structure.image}
                          alt={`${structure.name} anatomy`}
                          className="w-full h-52 object-cover group-hover:scale-105 transition-transform duration-500"
                          fallbackSrc="/images/anatomy/spine-default.jpg"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-8 p-8">
                      <div className="space-y-3">
                        <h4 className="font-bold text-base text-primary mb-4 flex items-center gap-2">
                          <div className="w-2 h-2 bg-primary rounded-full"></div>
                          Description
                        </h4>
                        <p className="text-muted-foreground leading-relaxed">{structure.description}</p>
                      </div>
                      <div className="space-y-3">
                        <h4 className="font-bold text-base text-primary mb-4 flex items-center gap-2">
                          <div className="w-2 h-2 bg-primary rounded-full"></div>
                          Function
                        </h4>
                        <p className="text-muted-foreground leading-relaxed">{structure.function}</p>
                      </div>
                      <div className="space-y-3">
                        <h4 className="font-bold text-base text-primary mb-4 flex items-center gap-2">
                          <div className="w-2 h-2 bg-primary rounded-full"></div>
                          Pathophysiology
                        </h4>
                        <p className="text-muted-foreground leading-relaxed">{structure.pathophysiology}</p>
                      </div>
                    </CardContent>
                  </div>
                </Card>
                );
              })}
            </div>
          </div>
        ))}
      </div>
    </TabsContent>
  );
};

export default AnatomicalStructuresSection;
