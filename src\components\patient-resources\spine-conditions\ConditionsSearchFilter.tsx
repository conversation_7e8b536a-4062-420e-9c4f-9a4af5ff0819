import { Search, Filter, Clock } from 'lucide-react';
import React from 'react';

import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface ConditionsSearchFilterProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  selectedRegion: string;
  setSelectedRegion: (region: string) => void;
  selectedSeverity: string;
  setSelectedSeverity: (severity: string) => void;
  totalConditions: number;
  filteredCount: number;
}

const ConditionsSearchFilter: React.FC<ConditionsSearchFilterProps> = ({
  searchTerm,
  setSearchTerm,
  selectedRegion,
  setSelectedRegion,
  selectedSeverity,
  setSelectedSeverity,
  totalConditions,
  filteredCount
}) => {
  const { isMobile } = useDeviceDetection();

  return (
    <section className="py-12 bg-muted/30">
      <div className="container max-w-4xl">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-enhanced-heading mb-4">
            Find Your Condition
          </h2>
          <p className="text-enhanced-body text-muted-foreground">
            Search by condition name, symptoms, or treatment options
          </p>
        </div>

        <div className={cn(
          "flex gap-4 mb-8",
          isMobile ? "flex-col" : "flex-row items-center"
        )}>
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search conditions, symptoms, or treatments..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          <div className="flex gap-2">
            <Select value={selectedRegion} onValueChange={setSelectedRegion}>
              <SelectTrigger className="w-[140px]">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Region" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Regions</SelectItem>
                <SelectItem value="cervical">Cervical</SelectItem>
                <SelectItem value="thoracic">Thoracic</SelectItem>
                <SelectItem value="lumbar">Lumbar</SelectItem>
                <SelectItem value="general">General</SelectItem>
              </SelectContent>
            </Select>

            <Select value={selectedSeverity} onValueChange={setSelectedSeverity}>
              <SelectTrigger className="w-[120px]">
                <Clock className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Severity" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Severity</SelectItem>
                <SelectItem value="mild">Mild</SelectItem>
                <SelectItem value="moderate">Moderate</SelectItem>
                <SelectItem value="severe">Severe</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="text-center text-sm text-muted-foreground">
          Showing {filteredCount} of {totalConditions} conditions
        </div>
      </div>
    </section>
  );
};

export default ConditionsSearchFilter;
