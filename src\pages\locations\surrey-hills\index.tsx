import React, { useEffect } from 'react';

import Footer from '@/components/Footer';
import {
  LocationHero,
  LocationContactInfo,
  LocationMap,
  LocationFacilities,
  LocationCTA,
  LocationAmenities,
  NearbyAmenities,
  NearbyHospitals,
  OtherConsultingLocations
} from '@/components/locations';
import Navbar from '@/components/Navbar';
import { surreyHillsLocationData } from '@/data/locations/surreyHillsData';

/**
 * Surrey Hills Location Page - Refactored
 * Complete refactoring preserving ALL content from the original 894-line file
 * Modular architecture with comprehensive data preservation
 */
const SurreyHillsLocation: React.FC = () => {
  useEffect(() => {
    // Scroll to top when component mounts
    window.scrollTo(0, 0);
  }, []);

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <div className="flex-1 pt-20">
        {/* Hero Section */}
        <LocationHero
          title={surreyHillsLocationData.hero.title}
          subtitle={surreyHillsLocationData.hero.subtitle}
          introduction1={surreyHillsLocationData.hero.introduction1}
          introduction2={surreyHillsLocationData.hero.introduction2}
          introduction3={surreyHillsLocationData.hero.introduction3}
          imageUrl={surreyHillsLocationData.hero.imageUrl}
        />

        {/* Location Details Section */}
        <section className="py-16">
          <div className="container">
            <div className="flex flex-col md:flex-row gap-8">
              <div className="md:w-1/2">
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-4">Location Details</h2>
                  <p className="text-muted-foreground">
                    Everything you need to know about our Surrey Hills consultation suites
                  </p>
                </div>

                <LocationContactInfo
                  address={surreyHillsLocationData.contact.address}
                  phone={surreyHillsLocationData.contact.phone}
                  email={surreyHillsLocationData.contact.email}
                  hours={surreyHillsLocationData.contact.hours}
                  consultingHours={surreyHillsLocationData.contact.consultingHours}
                  appointmentProcess={surreyHillsLocationData.contact.appointmentProcess}
                />
              </div>

              <div className="md:w-1/2">
                <LocationMap
                  embedUrl={surreyHillsLocationData.map.embedUrl}
                  title={surreyHillsLocationData.map.title}
                  transportOptions={surreyHillsLocationData.map.transportOptions}
                  gettingHereTitle={surreyHillsLocationData.map.gettingHereTitle}
                  publicTransportTitle={surreyHillsLocationData.map.publicTransportTitle}
                  carTitle={surreyHillsLocationData.map.carTitle}
                />
              </div>
            </div>
          </div>
        </section>

        {/* Facilities Section */}
        <LocationFacilities
          title={surreyHillsLocationData.facilities.title}
          subtitle={surreyHillsLocationData.facilities.subtitle}
          description={surreyHillsLocationData.facilities.description}
          facilities={surreyHillsLocationData.facilities.facilities}
          additionalFacilities={surreyHillsLocationData.facilities.additionalFacilities}
          gallery={surreyHillsLocationData.facilities.gallery}
        />

        {/* Location and Amenities Section */}
        <LocationAmenities
          title={surreyHillsLocationData.amenities.title}
          description={surreyHillsLocationData.amenities.description}
          locationDetails={surreyHillsLocationData.amenities.locationDetails}
          medicalFacilities={surreyHillsLocationData.amenities.medicalFacilities}
          surroundingAmenities={surreyHillsLocationData.amenities.surroundingAmenities}
          transportation={surreyHillsLocationData.amenities.transportation}
          parking={surreyHillsLocationData.amenities.parking}
          images={surreyHillsLocationData.amenities.images}
        />

        {/* Nearby Amenities Section */}
        <NearbyAmenities
          title={surreyHillsLocationData.nearbyAmenities.title}
          subtitle={surreyHillsLocationData.nearbyAmenities.subtitle}
          description={surreyHillsLocationData.nearbyAmenities.description}
          categories={surreyHillsLocationData.nearbyAmenities.categories}
        />

        {/* Nearby Hospitals Section */}
        <NearbyHospitals
          title={surreyHillsLocationData.nearbyHospitals.title}
          subtitle={surreyHillsLocationData.nearbyHospitals.subtitle}
          description={surreyHillsLocationData.nearbyHospitals.description}
          hospitals={surreyHillsLocationData.nearbyHospitals.hospitals}
        />

        {/* Other Consulting Locations Section */}
        <OtherConsultingLocations
          title={surreyHillsLocationData.otherLocations.title}
          subtitle={surreyHillsLocationData.otherLocations.subtitle}
          description={surreyHillsLocationData.otherLocations.description}
          locations={surreyHillsLocationData.otherLocations.locations}
        />



        {/* CTA Section */}
        <LocationCTA
          title={surreyHillsLocationData.cta.title}
          description={surreyHillsLocationData.cta.description}
          buttons={surreyHillsLocationData.cta.buttons}
        />
      </div>

      <Footer />
    </div>
  );
};

SurreyHillsLocation.displayName = 'SurreyHillsLocation';

export default SurreyHillsLocation;
