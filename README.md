# miNEURO Website

A comprehensive medical website for neurosurgical and spine care services, built with modern web technologies and designed for optimal user experience across all devices.

## 🏥 About

miNEURO is a professional medical website providing information about neurosurgical procedures, spine conditions, patient resources, and medical expertise. The platform serves patients, general practitioners, and medical professionals with comprehensive educational content and appointment booking capabilities.

## 🚀 Quick Start

### Prerequisites

- **Node.js** 18.0.0 or higher
- **npm** 9.0.0 or higher
- **Git** for version control

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd vas-41

# Install dependencies
npm install

# Start development server
npm run dev
```

The application will be available at `http://localhost:5173`

### Build for Production

```bash
# Type check and build
npm run build

# Preview production build
npm run preview
```

## 🏗️ Architecture

### Technology Stack

- **Frontend Framework**: React 18.3.1 with TypeScript
- **Build Tool**: Vite 6.3.5
- **Styling**: Tailwind CSS 3.4.11 with custom medical theme
- **UI Components**: Radix UI primitives with custom styling
- **Routing**: React Router DOM 7.6.3
- **State Management**: React Context + Custom Hooks
- **Testing**: Vitest + React Testing Library
- **Code Quality**: ESLint + Prettier + Husky

### Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── ui/             # Base UI components (buttons, cards, etc.)
│   ├── shared/         # Common shared components
│   ├── layout/         # Layout components and variants
│   ├── medical-conditions/  # Medical condition components
│   ├── patient-resources/   # Patient resource components
│   └── [feature]/      # Feature-specific components
├── pages/              # Page components
├── contexts/           # React contexts (Device, Language)
├── hooks/              # Custom React hooks
├── lib/                # Utility libraries and configurations
├── data/               # Static data and content
├── types/              # TypeScript type definitions
├── routes/             # Route definitions and configuration
└── tests/              # Test files and utilities
```

## 🎨 Design System

### Theme

The application uses a professional medical theme with:
- **Primary Color**: Medical Blue (#2563eb)
- **Typography**: System fonts optimized for readability
- **Spacing**: Consistent spacing scale with mobile-optimized variants
- **Accessibility**: WCAG AA+ compliant color contrasts

### Component Patterns

#### Base Component Props
```typescript
interface BaseComponentProps {
  className?: string;
  children?: ReactNode;
  'data-testid'?: string;
}
```

#### Responsive Design
- Mobile-first approach with touch-friendly targets
- Adaptive layouts using CSS Grid and Flexbox
- Device-aware components using `useDeviceDetection` hook

#### Accessibility
- Semantic HTML structure
- ARIA labels and descriptions
- Keyboard navigation support
- Screen reader optimization

## 📱 Features

### Core Functionality
- **Responsive Design**: Optimized for all devices and screen sizes
- **Accessibility**: WCAG AA+ compliant with full keyboard navigation
- **Performance**: Optimized loading with code splitting and lazy loading
- **SEO**: Comprehensive meta tags and structured data
- **Error Handling**: Robust error boundaries and fallback UI

### Medical Content
- **80+ Medical Conditions**: Comprehensive condition information
- **Exercise Library**: Safe exercises with detailed instructions
- **Patient Resources**: Educational materials and assessment tools
- **GP Resources**: Professional tools for healthcare providers
- **Location Information**: 11 clinic locations with detailed information

### User Experience
- **Professional Design**: Clean, medical-grade interface
- **Fast Navigation**: Optimized routing and state management
- **Search Functionality**: Find conditions and resources quickly
- **Appointment Booking**: Integrated appointment scheduling
- **Multi-language Ready**: Prepared for internationalization

## 🛠️ Development

### Available Scripts

```bash
# Development
npm run dev              # Start development server
npm run type-check       # TypeScript type checking
npm run type-check:watch # Watch mode type checking

# Code Quality
npm run lint             # Run ESLint
npm run lint:fix         # Fix ESLint issues
npm run format           # Format code with Prettier
npm run format:check     # Check code formatting

# Testing
npm run test             # Run tests
npm run test:ui          # Run tests with UI
npm run test:coverage    # Run tests with coverage
npm run test:watch       # Watch mode testing

# Build & Deploy
npm run build            # Production build
npm run build:dev        # Development build
npm run preview          # Preview production build
npm run production-check # Full production validation
```

### Code Standards

#### TypeScript
- Strict TypeScript configuration
- Comprehensive type definitions
- No `any` types allowed in production code

#### Component Guidelines
- Functional components with hooks
- Props interfaces for all components
- Consistent naming conventions
- Comprehensive error handling

#### Styling
- Tailwind CSS utility classes
- Custom CSS variables for theming
- Responsive design patterns
- Accessibility-first approach

### Testing Strategy

- **Unit Tests**: Component and utility function testing
- **Integration Tests**: Feature and workflow testing
- **Accessibility Tests**: WCAG compliance validation
- **Performance Tests**: Bundle size and loading optimization

## 📚 Documentation

### Component Documentation
- [Component Library](./docs/pages/README.md) - Complete component reference
- [UI Components](./src/components/ui/) - Base UI component documentation
- [Layout System](./src/components/layout/) - Layout component patterns

### Development Guides
- [Architecture Overview](./docs/current-architecture-2025.md) - System architecture
- [Development Guidelines](./docs/foundation-phase-summary.md) - Coding standards
- [Testing Guide](./src/tests/comprehensive-test-plan.md) - Testing strategies

### Content Management
- [Medical Conditions](./docs/pages/patient-resources/) - Condition page documentation
- [Patient Resources](./src/data/patient-resources/) - Educational content structure
- [Location Data](./src/data/locations/) - Clinic information management

## 🚀 Deployment

### Production Build
```bash
# Full production validation
npm run pre-deploy

# Build for production
npm run build:production
```

### Environment Configuration
- Development: `npm run dev`
- Production: Optimized build with minification and compression
- Preview: Local production preview with `npm run preview`

## 🤝 Contributing

### Development Workflow
1. Create feature branch from `main`
2. Implement changes with tests
3. Run quality checks: `npm run production-check`
4. Submit pull request with comprehensive description

### Code Review Checklist
- [ ] TypeScript types are comprehensive
- [ ] Components follow established patterns
- [ ] Accessibility requirements met
- [ ] Tests cover new functionality
- [ ] Documentation updated
- [ ] Performance impact considered

## 📄 License

This project is proprietary software for miNEURO medical practice.

## 📞 Support

For technical support or questions about the codebase, please contact the development team.

---

**Built with ❤️ for better patient care and medical education**
