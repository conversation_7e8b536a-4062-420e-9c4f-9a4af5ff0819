#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

console.log('🔧 REMAINING ANY TYPES CLEANUP');
console.log('===============================');

// More specific replacements for the remaining any types
const specificReplacements = [
  // Medical condition data patterns
  {
    pattern: /exercises: any/g,
    replacement: 'exercises: Array<{ id: string; name: string; description: string; instructions: string[]; difficulty: string; duration: string; repetitions?: string; }>',
    description: 'Fix exercises: any types'
  },
  {
    pattern: /ergonomicTips: any/g,
    replacement: 'ergonomicTips: Array<{ id: string; category: string; title: string; description: string; importance: string; }>',
    description: 'Fix ergonomicTips: any types'
  },
  {
    pattern: /workplaceModifications: any/g,
    replacement: 'workplaceModifications: Array<{ id: string; area: string; modification: string; benefit: string; cost: string; }>',
    description: 'Fix workplaceModifications: any types'
  },
  {
    pattern: /treatments: any/g,
    replacement: 'treatments: Array<{ name: string; type: string; description: string; effectiveness: string; invasiveness: string; recoveryTime: string; risks: string[]; benefits: string[]; }>',
    description: 'Fix treatments: any types'
  },
  {
    pattern: /symptoms: any/g,
    replacement: 'symptoms: Array<{ name: string; description: string; severity: string; frequency: string; }>',
    description: 'Fix symptoms: any types'
  },
  {
    pattern: /assessmentQuestions: any/g,
    replacement: 'assessmentQuestions: Array<{ id: string; question: string; type: string; options?: Array<{ value: string; label: string; score?: number; }>; }>',
    description: 'Fix assessmentQuestions: any types'
  },
  {
    pattern: /riskFactors: any/g,
    replacement: 'riskFactors: Array<{ factor: string; description: string; impact: string; modifiable: boolean; }>',
    description: 'Fix riskFactors: any types'
  },
  {
    pattern: /treatmentOptions: any/g,
    replacement: 'treatmentOptions: Array<{ name: string; description: string; effectiveness: string; sideEffects: string[]; contraindications: string[]; }>',
    description: 'Fix treatmentOptions: any types'
  },
  
  // Function parameter patterns
  {
    pattern: /\(error: any\)/g,
    replacement: '(error: Error | unknown)',
    description: 'Fix error parameter types'
  },
  {
    pattern: /\(event: any\)/g,
    replacement: '(event: Event)',
    description: 'Fix event parameter types'
  },
  {
    pattern: /\(data: any\)/g,
    replacement: '(data: Record<string, unknown>)',
    description: 'Fix data parameter types'
  },
  
  // Generic object patterns
  {
    pattern: /: any = \{/g,
    replacement: ': Record<string, unknown> = {',
    description: 'Fix object initialization types'
  },
  {
    pattern: /: any = \[/g,
    replacement: ': unknown[] = [',
    description: 'Fix array initialization types'
  },
  
  // Specific complex patterns
  {
    pattern: /comparisonData: any/g,
    replacement: 'comparisonData: { categories: string[]; treatments: Array<{ name: string; [key: string]: string | number | string[]; }>; }',
    description: 'Fix comparisonData types'
  }
];

// Get files that still have any types
function getFilesWithRemainingAnyTypes() {
  const files = [];
  
  function scanDirectory(dir) {
    if (!fs.existsSync(dir)) return;
    
    const items = fs.readdirSync(dir);
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        scanDirectory(fullPath);
      } else if (item.endsWith('.ts') || item.endsWith('.tsx')) {
        try {
          const content = fs.readFileSync(fullPath, 'utf8');
          if (content.includes(': any')) {
            files.push(fullPath);
          }
        } catch (error) {
          // Skip files that can't be read
        }
      }
    }
  }
  
  scanDirectory('src');
  return files;
}

const filesWithAnyTypes = getFilesWithRemainingAnyTypes();
console.log(`📁 Found ${filesWithAnyTypes.length} files still with 'any' types`);

let totalChanges = 0;
let filesModified = 0;

// Apply specific replacements
for (const replacement of specificReplacements) {
  console.log(`\n🔍 ${replacement.description}...`);
  let patternCount = 0;
  
  for (const filePath of filesWithAnyTypes) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const newContent = content.replace(replacement.pattern, replacement.replacement);
      
      if (content !== newContent) {
        fs.writeFileSync(filePath, newContent, 'utf8');
        patternCount++;
        console.log(`   ✅ ${path.relative(process.cwd(), filePath)}`);
      }
    } catch (error) {
      console.log(`   ❌ Error processing ${filePath}: ${error.message}`);
    }
  }
  
  if (patternCount > 0) {
    console.log(`   📊 Applied to ${patternCount} files`);
    totalChanges += patternCount;
  } else {
    console.log(`   ℹ️  No matches found`);
  }
}

// Count files modified
const modifiedFiles = new Set();
for (const filePath of filesWithAnyTypes) {
  try {
    const originalContent = fs.readFileSync(filePath, 'utf8');
    let testContent = originalContent;
    
    for (const replacement of specificReplacements) {
      testContent = testContent.replace(replacement.pattern, replacement.replacement);
    }
    
    if (testContent !== originalContent) {
      modifiedFiles.add(filePath);
    }
  } catch (error) {
    // Skip
  }
}

filesModified = modifiedFiles.size;

console.log(`\n✨ SUMMARY`);
console.log(`==========`);
console.log(`Files scanned: ${filesWithAnyTypes.length}`);
console.log(`Files modified: ${filesModified}`);
console.log(`Total pattern applications: ${totalChanges}`);

if (totalChanges > 0) {
  console.log(`\n🧪 Running type check...`);
  try {
    execSync('npm run type-check', { stdio: 'inherit' });
    console.log(`✅ Type check passed!`);
  } catch (error) {
    console.log(`⚠️  Type check had issues. Manual review needed for complex types.`);
  }
  
  console.log(`\n📊 Checking ESLint progress...`);
  try {
    const lintResult = execSync('npm run lint 2>&1 | grep -E "error|warning" | wc -l', { encoding: 'utf8' });
    const remainingIssues = parseInt(lintResult.trim());
    console.log(`Remaining ESLint issues: ${remainingIssues}`);
    
    if (remainingIssues < 100) {
      console.log(`🎉 More progress! Reduced from 100 to ${remainingIssues} issues.`);
    }
  } catch (error) {
    console.log(`Could not check ESLint progress automatically`);
  }
}

console.log(`\n🎯 REMAINING ISSUES ANALYSIS`);
console.log(`============================`);
console.log(`Most remaining issues are likely:`);
console.log(`• React refresh warnings (development-only)`);
console.log(`• Complex any types requiring manual review`);
console.log(`• React hooks dependency warnings`);

console.log(`\n🎉 Specific any types cleanup completed!`);
