import React, { useEffect } from 'react';
import { Helmet } from 'react-helmet-async';

import {
  ConditionHero,
  ConditionOverviewSection,
  ConditionQuickFacts
} from '@/components/medical-conditions/shared';
import {
  TrigeminalAnatomySection,
  MedicationComparison,
  PainTriggerManagement,
  SurgicalOptionsComparison,
  PainAssessmentTool
} from '@/components/medical-conditions/trigeminal-neuralgia';
import StandardPageLayout from '@/components/StandardPageLayout';
import { trigeminalNeuralgiaData } from '@/data/conditions/trigeminalNeuralgia';
import { useScrollToTop } from '@/hooks/useScrollToTop';

const TrigeminalNeuralgia: React.FC = () => {
  useScrollToTop();

  useEffect(() => {
    // Track page view for analytics
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('config', 'GA_MEASUREMENT_ID', {
        pageTitle: 'Trigeminal Neuralgia Guide',
        pageLocation: window.location.href
      });
    }
  }, []);

  return (
    <>
      <Helmet>
        <title>Trigeminal Neuralgia: Comprehensive Patient Guide | miNEURO</title>
        <meta 
          name="description" 
          content="Complete guide to trigeminal neuralgia: causes, symptoms, diagnosis, and treatment options. Expert neurosurgical care with advanced surgical techniques and comprehensive pain management." 
        />
        <meta 
          name="keywords" 
          content="trigeminal neuralgia, facial pain, neuralgia, microvascular decompression, gamma knife, carbamazepine, facial nerve pain, Melbourne neurosurgeon" 
        />
        <meta name="author" content="Dr. Ales Aliashkevich" />
        <meta property="og:title" content="Trigeminal Neuralgia: Comprehensive Patient Guide | miNEURO" />
        <meta 
          property="og:description" 
          content="Expert guide to trigeminal neuralgia covering causes, symptoms, diagnosis, and advanced treatment options including surgical interventions and pain management strategies." 
        />
        <meta property="og:type" content="article" />
        <meta property="og:url" content="https://mineuro.com.au/patient-resources/conditions/trigeminal-neuralgia" />
        <meta property="og:image" content="https://mineuro.com.au/images/neurological-conditions/trigeminal-neuralgia-guide-og.jpg" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="Trigeminal Neuralgia: Comprehensive Patient Guide" />
        <meta name="twitter:description" content="Complete guide to trigeminal neuralgia with expert neurosurgical insights and treatment options." />
        <link rel="canonical" href="https://mineuro.com.au/patient-resources/conditions/trigeminal-neuralgia" />
        
        {/* Structured Data for Medical Content */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "MedicalWebPage",
            "name": "Trigeminal Neuralgia: Comprehensive Patient Guide",
            "description": "Complete guide to trigeminal neuralgia: causes, symptoms, diagnosis, and treatment options",
            "url": "https://mineuro.com.au/patient-resources/conditions/trigeminal-neuralgia",
            "mainEntity": {
              "@type": "MedicalCondition",
              "name": "Trigeminal Neuralgia",
              "alternateName": ["Tic Douloureux", "Facial Neuralgia"],
              "description": "Chronic pain disorder affecting the trigeminal nerve, causing severe facial pain",
              "symptom": [
                "Electric shock-like facial pain",
                "Unilateral facial pain",
                "Trigger zone sensitivity",
                "Brief pain episodes"
              ],
              "riskFactor": [
                "Age over 50",
                "Female gender",
                "Multiple sclerosis",
                "Vascular compression"
              ]
            },
            "author": {
              "@type": "Person",
              "name": "Dr. Ales Aliashkevich",
              "jobTitle": "Neurosurgeon",
              "affiliation": {
                "@type": "Organization",
                "name": "miNEURO Brain and Spine Surgery"
              }
            },
            "datePublished": "2024-01-01",
            "dateModified": new Date().toISOString().split('T')[0],
            "publisher": {
              "@type": "Organization",
              "name": "miNEURO Brain and Spine Surgery",
              "url": "https://mineuro.com.au"
            }
          })}
        </script>
      </Helmet>

      <StandardPageLayout 
        title="Trigeminal Neuralgia - Comprehensive Guide" 
        showHeader={false}
      >
        <main className="flex-1 pt-20">
          {/* Hero Section */}
          <ConditionHero
            title={trigeminalNeuralgiaData.hero.title}
            subtitle={trigeminalNeuralgiaData.hero.subtitle}
            backgroundImage={trigeminalNeuralgiaData.hero.backgroundImage}
            badge={trigeminalNeuralgiaData.hero.badge}
            showAssessment={true}
            showBooking={true}
            assessmentLink="#pain-assessment"
            bookingLink="/appointments"
          />

          {/* Quick Facts */}
          <ConditionQuickFacts facts={trigeminalNeuralgiaData.quickFacts} />

          {/* Overview Section */}
          <ConditionOverviewSection
            title={trigeminalNeuralgiaData.overview.title}
            description={trigeminalNeuralgiaData.overview.description}
            keyPoints={trigeminalNeuralgiaData.overview.keyPoints}
            imageSrc={trigeminalNeuralgiaData.overview.imageSrc}
            imageAlt={trigeminalNeuralgiaData.overview.imageAlt}
            imageCaption={trigeminalNeuralgiaData.overview.imageCaption}
          />

          {/* Pain Assessment Tool */}
          <div id="pain-assessment">
            <PainAssessmentTool />
          </div>

          {/* Trigeminal Nerve Anatomy */}
          <TrigeminalAnatomySection
            title={trigeminalNeuralgiaData.anatomy.title}
            description={trigeminalNeuralgiaData.anatomy.description}
            nerveDistribution={trigeminalNeuralgiaData.anatomy.nerveDistribution}
          />

          {/* Types of Trigeminal Neuralgia */}
          <section className="section-spacing section-background">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{trigeminalNeuralgiaData.types.title}</h2>
                <p className="text-lg text-enhanced-body max-w-3xl mx-auto">
                  {trigeminalNeuralgiaData.types.description}
                </p>
              </div>
              
              <div className="grid gap-8 md:grid-cols-3">
                {trigeminalNeuralgiaData.types.classifications.map((type, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="font-semibold text-xl mb-3">{type.type}</h3>
                    <p className="text-muted-foreground mb-4">{type.description}</p>
                    
                    <div className="space-y-3">
                      <div>
                        <h4 className="font-medium text-sm mb-2">Characteristics:</h4>
                        <ul className="space-y-1">
                          {type.characteristics.map((char, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                              {char}
                            </li>
                          ))}
                        </ul>
                      </div>
                      
                      <div className="flex justify-between items-center pt-2 border-t border-border">
                        <span className="text-enhanced-muted text-xs">Prevalence:</span>
                        <span className="text-xs font-medium">{type.prevalence}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Symptoms Section */}
          <section className="section-spacing section-background-alt">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">Symptoms and Characteristics</h2>
                <p className="text-lg text-enhanced-body max-w-3xl mx-auto">
                  Trigeminal neuralgia has distinctive pain characteristics that help with diagnosis and treatment planning.
                </p>
              </div>
              
              <div className="grid gap-8 md:grid-cols-2">
                {trigeminalNeuralgiaData.symptoms.map((category, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="flex items-center gap-2 font-semibold text-xl mb-4">
                      <category.icon className="h-5 w-5 text-primary" />
                      {category.category}
                    </h3>
                    <div className="content-spacing-sm">
                      {category.symptoms.map((symptom, idx) => (
                        <div key={idx} className="border-l-4 border-primary/20 pl-4">
                          <div className="flex items-center justify-between mb-1">
                            <h4 className="text-enhanced-strong font-medium">{symptom.name}</h4>
                            <span className={`text-xs px-2 py-1 rounded ${
                              symptom.severity === 'severe' ? 'badge-emergency' :
                              symptom.severity === 'moderate' ? 'badge-routine' :
                              'badge-routine'
                            }`}>
                              {symptom.severity}
                            </span>
                          </div>
                          <p className="text-sm text-muted-foreground mb-1">{symptom.description}</p>
                          <p className="text-enhanced-muted text-xs">Frequency: {symptom.frequency}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Pain Trigger Management */}
          <div id="trigger-management">
            <PainTriggerManagement
              title={trigeminalNeuralgiaData.triggers.title}
              description={trigeminalNeuralgiaData.triggers.description}
              commonTriggers={trigeminalNeuralgiaData.triggers.commonTriggers}
            />
          </div>

          {/* Diagnosis Section */}
          <section className="section-spacing section-background">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{trigeminalNeuralgiaData.diagnosis.title}</h2>
                <p className="text-lg text-enhanced-body max-w-3xl mx-auto">
                  {trigeminalNeuralgiaData.diagnosis.description}
                </p>
              </div>
              
              <div className="grid gap-8 lg:grid-cols-2">
                {/* Diagnostic Criteria */}
                <div className="medical-card p-6">
                  <h3 className="font-semibold text-xl mb-4">Diagnostic Criteria</h3>
                  <div className="space-y-3">
                    {trigeminalNeuralgiaData.diagnosis.criteria.map((criterion, index) => (
                      <div key={index} className="flex items-start gap-3">
                        <div className={`w-3 h-3 rounded-full mt-1.5 flex-shrink-0 ${
                          criterion.importance === 'essential' ? 'bg-muted' : 'bg-primary'
                        }`} />
                        <div>
                          <h4 className="text-enhanced-strong font-medium">{criterion.criterion}</h4>
                          <p className="text-enhanced-muted text-sm">{criterion.description}</p>
                          <span className={`text-xs px-2 py-1 rounded mt-1 inline-block ${
                            criterion.importance === 'essential' ? 'badge-emergency' : 'badge-info'
                          }`}>
                            {criterion.importance}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Differential Diagnosis */}
                <div className="medical-card p-6">
                  <h3 className="font-semibold text-xl mb-4">Differential Diagnosis</h3>
                  <div className="content-spacing-sm">
                    {trigeminalNeuralgiaData.diagnosis.differentialDiagnosis.map((condition, index) => (
                      <div key={index} className="border-l-4 border-info pl-4">
                        <h4 className="font-medium mb-2">{condition.condition}</h4>
                        <ul className="space-y-1">
                          {condition.distinguishingFeatures.map((feature, idx) => (
                            <li key={idx} className="text-sm text-muted-foreground flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-info rounded-full mt-1.5 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Medication Comparison */}
          <MedicationComparison
            title={trigeminalNeuralgiaData.medications.title}
            description={trigeminalNeuralgiaData.medications.description}
            firstLine={trigeminalNeuralgiaData.medications.firstLine}
            secondLine={trigeminalNeuralgiaData.medications.secondLine}
          />

          {/* Surgical Options */}
          <SurgicalOptionsComparison
            title={trigeminalNeuralgiaData.surgicalOptions.title}
            description={trigeminalNeuralgiaData.surgicalOptions.description}
            procedures={trigeminalNeuralgiaData.surgicalOptions.procedures}
          />

          {/* Living with Trigeminal Neuralgia */}
          <section className="py-16 bg-info">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{trigeminalNeuralgiaData.livingWithTN.title}</h2>
                <p className="text-lg text-enhanced-body max-w-3xl mx-auto">
                  {trigeminalNeuralgiaData.livingWithTN.description}
                </p>
              </div>
              
              <div className="grid gap-8 md:grid-cols-3">
                {trigeminalNeuralgiaData.livingWithTN.sections.map((section, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="font-semibold text-xl mb-4">{section.title}</h3>
                    <div className="space-y-3 mb-4">
                      {section.content.map((paragraph, idx) => (
                        <p key={idx} className="text-enhanced-muted text-sm">{paragraph}</p>
                      ))}
                    </div>
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm">Practical Tips:</h4>
                      <ul className="space-y-1">
                        {section.tips.map((tip, idx) => (
                          <li key={idx} className="text-xs text-muted-foreground flex items-start gap-2">
                            <div className="w-1.5 h-1.5 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                            {tip}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Emergency Guidance */}
          <section className="py-16 bg-muted">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-3xl font-bold mb-4 text-foreground">{trigeminalNeuralgiaData.emergencyGuidance.title}</h2>
                <p className="text-lg text-foreground max-w-3xl mx-auto">
                  {trigeminalNeuralgiaData.emergencyGuidance.description}
                </p>
              </div>
              
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {trigeminalNeuralgiaData.emergencyGuidance.emergencySigns.map((sign, index) => (
                  <div key={index} className="bg-background rounded-lg p-6 shadow-md border border-border">
                    <h3 className="font-semibold text-lg mb-2 text-foreground">{sign.sign}</h3>
                    <p className="text-sm text-foreground mb-3">{sign.description}</p>
                    <div className="bg-muted border border-border rounded p-3">
                      <p className="text-sm font-medium text-foreground">{sign.action}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Support Resources */}
          <section className="section-spacing section-background">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{trigeminalNeuralgiaData.supportResources.title}</h2>
                <p className="text-lg text-enhanced-body max-w-3xl mx-auto">
                  {trigeminalNeuralgiaData.supportResources.description}
                </p>
              </div>
              
              <div className="grid gap-8 md:grid-cols-3">
                {trigeminalNeuralgiaData.supportResources.resources.map((resourceCategory, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="font-semibold text-xl mb-4">{resourceCategory.category}</h3>
                    <div className="content-spacing-sm">
                      {resourceCategory.items.map((item, idx) => (
                        <div key={idx} className="border-b border-border pb-3 last:border-b-0">
                          <h4 className="font-medium mb-1">{item.name}</h4>
                          <p className="text-sm text-muted-foreground mb-1">{item.description}</p>
                          {item.contact && (
                            <p className="text-xs text-primary">{item.contact}</p>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>
        </main>
      </StandardPageLayout>
    </>
  );
};

export default TrigeminalNeuralgia;
