const fs = require('fs');
const path = require('path');

// Function to recursively find all TypeScript and TSX files
function findTsFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      if (!['node_modules', '.git', 'dist', 'build'].includes(file)) {
        findTsFiles(filePath, fileList);
      }
    } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// Function to systematically fix missing closing braces
function fixMissingClosingBraces(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Split content into lines for analysis
    const lines = content.split('\n');
    const fixedLines = [];
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const nextLine = i < lines.length - 1 ? lines[i + 1] : '';
      const lineAfterNext = i < lines.length - 2 ? lines[i + 2] : '';
      
      fixedLines.push(line);
      
      // Pattern 1: Missing closing brace before method declaration
      // Look for: "    }\n\n  /**" followed by method declaration
      if (line.trim() === '}' && 
          nextLine.trim() === '' && 
          lineAfterNext.trim() === '' &&
          i + 3 < lines.length) {
        
        const commentLine = lines[i + 3];
        if (commentLine.trim().startsWith('/**')) {
          // Find the end of the comment block
          let commentEndIndex = i + 3;
          while (commentEndIndex < lines.length && !lines[commentEndIndex].trim().endsWith('*/')) {
            commentEndIndex++;
          }
          
          if (commentEndIndex < lines.length) {
            const methodLineIndex = commentEndIndex + 1;
            if (methodLineIndex < lines.length) {
              const methodLine = lines[methodLineIndex];
              
              // Check if it's a method declaration
              if (/^\s*(private|public|protected)?\s*\w+\s*\([^)]*\)\s*:\s*\w+/.test(methodLine)) {
                // Add missing closing brace
                fixedLines.push('  }');
                modified = true;
                console.log(`Fixed missing closing brace before method in: ${filePath} at line ${i + 1}`);
              }
            }
          }
        }
      }
      
      // Pattern 2: Missing closing brace before function declaration
      // Look for similar pattern but with function keyword
      if (line.trim() === '}' && 
          nextLine.trim() === '' && 
          lineAfterNext.trim() === '' &&
          i + 3 < lines.length) {
        
        const commentLine = lines[i + 3];
        if (commentLine.trim().startsWith('/**')) {
          // Find the end of the comment block
          let commentEndIndex = i + 3;
          while (commentEndIndex < lines.length && !lines[commentEndIndex].trim().endsWith('*/')) {
            commentEndIndex++;
          }
          
          if (commentEndIndex < lines.length) {
            const functionLineIndex = commentEndIndex + 1;
            if (functionLineIndex < lines.length) {
              const functionLine = lines[functionLineIndex];
              
              // Check if it's a function declaration
              if (/^\s*(export\s+)?function\s+\w+/.test(functionLine)) {
                // Add missing closing brace
                fixedLines.push('  }');
                modified = true;
                console.log(`Fixed missing closing brace before function in: ${filePath} at line ${i + 1}`);
              }
            }
          }
        }
      }
    }
    
    if (modified) {
      const newContent = fixedLines.join('\n');
      fs.writeFileSync(filePath, newContent, 'utf8');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Main execution
console.log('🔧 Systematic missing closing brace fix...');

// Focus on the problematic files first
const problematicFiles = [
  'src/lib/performance.ts',
  'src/lib/security.ts',
  'src/lib/mobile-optimization.ts',
  'src/lib/env-validation.ts'
];

let fixedCount = 0;

problematicFiles.forEach(filePath => {
  const fullPath = path.join(process.cwd(), filePath);
  if (fs.existsSync(fullPath)) {
    if (fixMissingClosingBraces(fullPath)) {
      fixedCount++;
    }
  }
});

console.log(`\n✅ Processed ${problematicFiles.length} problematic files`);
console.log(`🔧 Fixed missing braces in ${fixedCount} files`);

if (fixedCount > 0) {
  console.log('\n🎯 Next steps:');
  console.log('1. Run npm run build to test the build process');
  console.log('2. Continue fixing any remaining issues');
}
