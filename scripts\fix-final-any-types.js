#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

console.log('🔧 FINAL ANY TYPES CLEANUP');
console.log('===========================');

// Final specific replacements for the remaining files
const finalReplacements = [
  // Icon component types
  {
    pattern: /icon: React\.ComponentType<any>/g,
    replacement: 'icon: React.ComponentType<{ className?: string; size?: number | string; }>',
    description: 'Fix React.ComponentType<any> to proper icon props'
  },
  
  // Function parameter types
  {
    pattern: /\(error: any\) =>/g,
    replacement: '(error: Error | unknown) =>',
    description: 'Fix error parameter in arrow functions'
  },
  {
    pattern: /\(event: any\) =>/g,
    replacement: '(event: Event) =>',
    description: 'Fix event parameter in arrow functions'
  },
  {
    pattern: /\(data: any\) =>/g,
    replacement: '(data: Record<string, unknown>) =>',
    description: 'Fix data parameter in arrow functions'
  },
  
  // Object and array types
  {
    pattern: /: any = \{/g,
    replacement: ': Record<string, unknown> = {',
    description: 'Fix object initialization'
  },
  {
    pattern: /: any = \[/g,
    replacement: ': unknown[] = [',
    description: 'Fix array initialization'
  },
  
  // Generic any types at end of lines
  {
    pattern: /: any$/gm,
    replacement: ': unknown',
    description: 'Fix remaining any types at end of lines'
  },
  {
    pattern: /: any;$/gm,
    replacement: ': unknown;',
    description: 'Fix remaining any types with semicolons'
  },
  {
    pattern: /: any,$/gm,
    replacement: ': unknown,',
    description: 'Fix remaining any types with commas'
  },
  
  // Specific error handling patterns
  {
    pattern: /catch \(error: any\)/g,
    replacement: 'catch (error: Error | unknown)',
    description: 'Fix catch block error types'
  },
  
  // Function return types
  {
    pattern: /\): any \{/g,
    replacement: '): unknown {',
    description: 'Fix function return types'
  },
  {
    pattern: /\): any =>/g,
    replacement: '): unknown =>',
    description: 'Fix arrow function return types'
  }
];

// Specific files that still have any types
const targetFiles = [
  'src/components/medical-conditions/peripheral-nerve-tumors/NerveTumorErgonomicsSection.tsx',
  'src/components/medical-conditions/peripheral-nerve-tumors/NerveTumorExerciseSection.tsx',
  'src/components/medical-conditions/peripheral-nerve-tumors/NerveTumorTreatmentComparison.tsx',
  'src/components/medical-conditions/peroneal-nerve-palsy/PeronealErgonomicsSection.tsx',
  'src/components/medical-conditions/peroneal-nerve-palsy/PeronealExerciseSection.tsx',
  'src/components/medical-conditions/peroneal-nerve-palsy/PeronealTreatmentComparison.tsx',
  'src/components/medical-conditions/tarsal-tunnel-syndrome/TarsalErgonomicsSection.tsx',
  'src/components/medical-conditions/tarsal-tunnel-syndrome/TarsalExerciseSection.tsx',
  'src/components/medical-conditions/tarsal-tunnel-syndrome/TarsalTreatmentComparison.tsx',
  'src/lib/enhanced-error-utils.ts'
];

console.log(`📁 Processing ${targetFiles.length} specific files with remaining any types`);

let totalChanges = 0;
let filesModified = 0;

// Apply final replacements
for (const replacement of finalReplacements) {
  console.log(`\n🔍 ${replacement.description}...`);
  let patternCount = 0;
  
  for (const filePath of targetFiles) {
    try {
      if (!fs.existsSync(filePath)) {
        console.log(`   ⚠️  File not found: ${filePath}`);
        continue;
      }
      
      const content = fs.readFileSync(filePath, 'utf8');
      const newContent = content.replace(replacement.pattern, replacement.replacement);
      
      if (content !== newContent) {
        fs.writeFileSync(filePath, newContent, 'utf8');
        patternCount++;
        console.log(`   ✅ ${path.relative(process.cwd(), filePath)}`);
      }
    } catch (error) {
      console.log(`   ❌ Error processing ${filePath}: ${error.message}`);
    }
  }
  
  if (patternCount > 0) {
    console.log(`   📊 Applied to ${patternCount} files`);
    totalChanges += patternCount;
  } else {
    console.log(`   ℹ️  No matches found`);
  }
}

// Count files modified
const modifiedFiles = new Set();
for (const filePath of targetFiles) {
  try {
    if (!fs.existsSync(filePath)) continue;
    
    const originalContent = fs.readFileSync(filePath, 'utf8');
    let testContent = originalContent;
    
    for (const replacement of finalReplacements) {
      testContent = testContent.replace(replacement.pattern, replacement.replacement);
    }
    
    if (testContent !== originalContent) {
      modifiedFiles.add(filePath);
    }
  } catch (error) {
    // Skip
  }
}

filesModified = modifiedFiles.size;

console.log(`\n✨ SUMMARY`);
console.log(`==========`);
console.log(`Files processed: ${targetFiles.length}`);
console.log(`Files modified: ${filesModified}`);
console.log(`Total pattern applications: ${totalChanges}`);

if (totalChanges > 0) {
  console.log(`\n🧪 Running type check...`);
  try {
    execSync('npm run type-check', { stdio: 'inherit' });
    console.log(`✅ Type check passed!`);
  } catch (error) {
    console.log(`⚠️  Type check had issues. This is expected when replacing complex any types.`);
  }
  
  console.log(`\n📊 Checking final ESLint progress...`);
  try {
    const lintResult = execSync('npm run lint 2>&1 | grep -E "error|warning" | wc -l', { encoding: 'utf8' });
    const remainingIssues = parseInt(lintResult.trim());
    console.log(`Remaining ESLint issues: ${remainingIssues}`);
    
    if (remainingIssues < 97) {
      console.log(`🎉 Final progress! Reduced from 97 to ${remainingIssues} issues.`);
    }
  } catch (error) {
    console.log(`Could not check ESLint progress automatically`);
  }
}

console.log(`\n🏁 FINAL ANALYSIS`);
console.log(`=================`);
console.log(`After this cleanup, remaining issues are likely:`);
console.log(`• React refresh warnings (development-only, not critical)`);
console.log(`• React hooks dependency warnings (specific cases)`);
console.log(`• Complex any types that require manual type definitions`);

console.log(`\n🎉 Final any types cleanup completed!`);
