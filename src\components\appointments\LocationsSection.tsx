
// TODO: Extract inline object types to interfaces
// Consider creating interfaces for complex object types
import React from 'react';
import { Link } from 'react-router-dom';

import SafeImage from '@/components/SafeImage';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { MainLocation } from '@/data/appointments/locationsData';

interface LocationsSectionProps {
  title: string;
  subtitle1: string;
  subtitle2: string;
  mainLocation: MainLocation;
  labels: {
    phone: string;
    fax: string;
    email: string;
    viewDetails: string;
    allLocations: string;
    viewAllLocations: string;
  };
}

const LocationsSection: React.FC<LocationsSectionProps> = ({ 
  title, 
  subtitle1, 
  subtitle2, 
  mainLocation, 
  labels 
}) => {
  return (
    <section className="py-16 bg-muted/30">
      <div className="container max-w-7xl">
        <div className="text-center mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4 text-foreground">{title}</h2>
          <p className="text-enhanced-body mb-2">{subtitle1}</p>
          <p className="text-enhanced-body">{subtitle2}</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Main Location Card */}
          <Card className="medical-card backdrop-blur-sm border border-border/50 shadow-md hover:shadow-lg transition-all duration-300">
            <CardHeader className="pb-4">
              <CardTitle className="text-xl text-enhanced-subheading">{mainLocation.title}</CardTitle>
            </CardHeader>
            <CardContent className="content-spacing-sm">
              <div>
                <p className="text-enhanced-strong mb-2">Address:</p>
                <p className="text-enhanced-body whitespace-pre-line">{mainLocation.address}</p>
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <p className="text-foreground font-medium">{labels.phone}</p>
                  <p className="text-muted-foreground">{mainLocation.phone}</p>
                </div>
                <div>
                  <p className="text-foreground font-medium">{labels.fax}</p>
                  <p className="text-muted-foreground">{mainLocation.fax}</p>
                </div>
              </div>
              
              <div>
                <p className="text-foreground font-medium">{labels.email}</p>
                <p className="text-muted-foreground">{mainLocation.email}</p>
              </div>
              
              <div className="pt-4">
                <Button asChild variant="outline" className="w-full">
                  <Link to="/locations/surrey-hills" className="text-primary hover:underline">{labels.viewDetails}</Link>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Location Image */}
          <div className="relative rounded-lg overflow-hidden shadow-lg">
            <SafeImage
              src={mainLocation.imageSrc}
              alt={mainLocation.imageAlt}
              className="w-full h-auto"
              fallbackSrc="/images/medical-consulting.jpg"
            />
          </div>
        </div>

        {/* All Locations Link */}
        <div className="text-center mt-12">
          <h3 className="text-xl font-semibold mb-4 text-foreground">{labels.allLocations}</h3>
          <Button asChild size="lg" >
            <Link to="/locations" >{labels.viewAllLocations}</Link>
          </Button>
        </div>
      </div>
    </section>
  );
};

LocationsSection.displayName = 'LocationsSection';

export default LocationsSection;
