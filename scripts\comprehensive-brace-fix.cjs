const fs = require('fs');
const path = require('path');

// Function to recursively find all TypeScript and TSX files
function findTsFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      if (!['node_modules', '.git', 'dist', 'build'].includes(file)) {
        findTsFiles(filePath, fileList);
      }
    } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// Function to analyze and fix brace issues in a file
function fixBraceIssues(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Split content into lines for analysis
    const lines = content.split('\n');
    const fixedLines = [];
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const nextLine = i < lines.length - 1 ? lines[i + 1] : '';
      const prevLine = i > 0 ? lines[i - 1] : '';
      
      // Check for missing closing brace before method/function declaration
      if (line.trim().endsWith('}') && 
          nextLine.trim() === '' && 
          i + 2 < lines.length) {
        
        const lineAfterEmpty = lines[i + 2];
        
        // Check if the line after empty is a comment block start
        if (lineAfterEmpty.trim().startsWith('/**')) {
          // Look for method/function declaration after comment block
          let commentEndIndex = i + 2;
          while (commentEndIndex < lines.length && !lines[commentEndIndex].trim().endsWith('*/')) {
            commentEndIndex++;
          }
          
          if (commentEndIndex < lines.length) {
            const methodLineIndex = commentEndIndex + 1;
            if (methodLineIndex < lines.length) {
              const methodLine = lines[methodLineIndex];
              
              // Check if it's a method/function declaration
              if (/^\s*(private|public|protected)?\s*\w+\s*\([^)]*\)\s*:\s*\w+/.test(methodLine) ||
                  /^\s*(export\s+)?function\s+\w+/.test(methodLine)) {
                
                // Add missing closing brace
                fixedLines.push(line);
                fixedLines.push('  }');
                fixedLines.push('');
                modified = true;
                console.log(`Fixed missing closing brace before method/function in: ${filePath} at line ${i + 1}`);
                continue;
              }
            }
          }
        }
      }
      
      fixedLines.push(line);
    }
    
    if (modified) {
      const newContent = fixedLines.join('\n');
      fs.writeFileSync(filePath, newContent, 'utf8');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Main execution
console.log('🔧 Comprehensive brace fixing...');

const srcDir = path.join(process.cwd(), 'src');
const tsFiles = findTsFiles(srcDir);

let fixedCount = 0;
let totalFiles = tsFiles.length;

tsFiles.forEach(filePath => {
  if (fixBraceIssues(filePath)) {
    fixedCount++;
  }
});

console.log(`\n✅ Processed ${totalFiles} files`);
console.log(`🔧 Fixed brace issues in ${fixedCount} files`);

if (fixedCount > 0) {
  console.log('\n🎯 Next steps:');
  console.log('1. Run npm run build to test the build process');
  console.log('2. Run npm run type-check to verify TypeScript compilation');
}
