#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// CRITICAL INVISIBLE TEXT PATTERNS TO FIX
const INVISIBLE_TEXT_FIXES = [
  // bg-info-light with text-info (INVISIBLE)
  {
    pattern: /bg-info-light text-info/g,
    replacement: 'bg-info-light text-foreground',
    desc: 'CRITICAL: bg-info-light text-info → text-foreground'
  },
  
  // bg-success-light with text-success (INVISIBLE)
  {
    pattern: /bg-success-light text-success/g,
    replacement: 'bg-success-light text-foreground',
    desc: 'CRITICAL: bg-success-light text-success → text-foreground'
  },
  
  // bg-error-light with text-error (INVISIBLE)
  {
    pattern: /bg-error-light text-error/g,
    replacement: 'bg-error-light text-foreground',
    desc: 'CRITICAL: bg-error-light text-error → text-foreground'
  },
  
  // bg-warning-light with text-warning (INVISIBLE)
  {
    pattern: /bg-warning-light text-warning/g,
    replacement: 'bg-warning-light text-foreground',
    desc: 'CRITICAL: bg-warning-light text-warning → text-foreground'
  },
  
  // Dark theme patterns
  {
    pattern: /dark:text-info(?=.*dark:bg-info)/g,
    replacement: 'dark:text-foreground',
    desc: 'CRITICAL: dark theme text-info on bg-info → text-foreground'
  },
  
  {
    pattern: /dark:text-success(?=.*dark:bg-success)/g,
    replacement: 'dark:text-foreground',
    desc: 'CRITICAL: dark theme text-success on bg-success → text-foreground'
  },
  
  // Specific patterns from search results
  {
    pattern: /'bg-success\/10 text-success dark:text-success'/g,
    replacement: "'bg-success-light/30 text-foreground dark:text-foreground'",
    desc: 'CRITICAL: success patterns → readable'
  },
  
  {
    pattern: /'bg-info\/10 text-info dark:text-info'/g,
    replacement: "'bg-info-light/30 text-foreground dark:text-foreground'",
    desc: 'CRITICAL: info patterns → readable'
  },
  
  {
    pattern: /'bg-info\/80 text-foreground dark:bg-info\/50 dark:text-info'/g,
    replacement: "'bg-info-light/30 text-foreground dark:bg-info-light/20 dark:text-foreground'",
    desc: 'CRITICAL: complex info patterns → readable'
  },
  
  {
    pattern: /'bg-success\/80 text-foreground dark:bg-success\/50 dark:text-success'/g,
    replacement: "'bg-success-light/30 text-foreground dark:bg-success-light/20 dark:text-foreground'",
    desc: 'CRITICAL: complex success patterns → readable'
  }
];

function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    let fixCount = 0;
    
    INVISIBLE_TEXT_FIXES.forEach(fix => {
      const matches = content.match(fix.pattern);
      if (matches) {
        content = content.replace(fix.pattern, fix.replacement);
        fixCount += matches.length;
        modified = true;
      }
    });
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`🔧 FIXED: ${fixCount} invisible text issues in ${filePath}`);
      return fixCount;
    }
    
    return 0;
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return 0;
  }
}

function findFiles(dir, extensions = ['.tsx', '.jsx']) {
  let files = [];
  
  try {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        files = files.concat(findFiles(fullPath, extensions));
      } else if (extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    }
  } catch (error) {
    console.error(`❌ Error reading directory ${dir}:`, error.message);
  }
  
  return files;
}

function main() {
  console.log('🚨 FIXING REMAINING INVISIBLE TEXT PATTERNS...\n');
  
  const srcDir = path.join(process.cwd(), 'src');
  const files = findFiles(srcDir);
  
  console.log(`📁 Found ${files.length} files to process\n`);
  
  let totalFixes = 0;
  let filesFixed = 0;
  
  files.forEach(file => {
    const fixes = processFile(file);
    if (fixes > 0) {
      totalFixes += fixes;
      filesFixed++;
    }
  });
  
  console.log('\n🚨 INVISIBLE TEXT FIX COMPLETE!');
  console.log(`📊 Total files updated: ${filesFixed}`);
  console.log(`📊 Total invisible text issues fixed: ${totalFixes}`);
  console.log('\n✅ ALL invisible text issues should now be resolved!');
  console.log('🔍 All text should now be readable with proper contrast.');
}

if (require.main === module) {
  main();
}

module.exports = { processFile, findFiles, INVISIBLE_TEXT_FIXES };
