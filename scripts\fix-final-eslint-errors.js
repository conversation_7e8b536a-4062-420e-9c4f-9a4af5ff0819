#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

console.log('🔧 FINAL ESLINT ERRORS CLEANUP');
console.log('===============================');

// Step 1: Fix unused imports (critical errors)
const unusedImportFixes = [
  {
    file: 'src/pages/PatientResources.tsx',
    fixes: [
      { pattern: /,\s*BookOpen/, replacement: '' },
      { pattern: /,\s*Users/, replacement: '' },
      { pattern: /,\s*Clock/, replacement: '' }
    ]
  },
  {
    file: 'src/pages/patient-resources/BrainAndSpineHealth.tsx',
    fixes: [
      { pattern: /,\s*Layers/, replacement: '' },
      { pattern: /setShowLoginRequired/, replacement: '_setShowLoginRequired' }
    ]
  },
  {
    file: 'src/pages/patient-resources/BrainConditions.tsx',
    fixes: [
      { pattern: /,\s*SectionHeader/, replacement: '' }
    ]
  },
  {
    file: 'src/pages/patient-resources/PeripheralNerveConditions.tsx',
    fixes: [
      { pattern: /,\s*Users/, replacement: '' },
      { pattern: /,\s*TrendingUp/, replacement: '' },
      { pattern: /,\s*AlertTriangle/, replacement: '' },
      { pattern: /,\s*SectionHeader/, replacement: '' }
    ]
  },
  {
    file: 'src/pages/patient-resources/SpineConditions.tsx',
    fixes: [
      { pattern: /,\s*Users/, replacement: '' },
      { pattern: /,\s*TrendingUp/, replacement: '' },
      { pattern: /,\s*SectionHeader/, replacement: '' }
    ]
  }
];

// Step 2: Fix the remaining any type
const anyTypeFixes = [
  {
    file: 'src/lib/enhanced-error-utils.ts',
    fixes: [
      { pattern: /: any\)/g, replacement: ': Error | unknown)' }
    ]
  }
];

// Step 3: Remove unused eslint-disable directives
const eslintDisableFixes = [
  {
    file: 'src/components/medical-conditions/brain-tumour/AccessibilityEnhanced.tsx',
    fixes: [
      { pattern: /\/\* eslint-disable react-refresh\/only-export-components \*\/\n?/, replacement: '' }
    ]
  },
  {
    file: 'src/components/medical-conditions/brain-tumour/SymptomAssessment.tsx',
    fixes: [
      { pattern: /\/\* eslint-disable react-refresh\/only-export-components \*\/\n?/, replacement: '' }
    ]
  }
];

// Step 4: Fix React hooks dependency warning
const reactHooksFixes = [
  {
    file: 'src/hooks/useTimeout.ts',
    fixes: [
      { 
        pattern: /return \(\) => \{\s*timersRef\.current\.forEach\(clearTimeout\);\s*\};/,
        replacement: 'return () => {\n        const currentTimers = timersRef.current;\n        currentTimers.forEach(clearTimeout);\n      };'
      }
    ]
  }
];

let totalChanges = 0;
let filesModified = 0;

// Apply all fixes
const allFixes = [
  ...unusedImportFixes,
  ...anyTypeFixes,
  ...eslintDisableFixes,
  ...reactHooksFixes
];

console.log(`📁 Processing ${allFixes.length} files with specific fixes`);

for (const fileFix of allFixes) {
  try {
    if (!fs.existsSync(fileFix.file)) {
      console.log(`   ⚠️  File not found: ${fileFix.file}`);
      continue;
    }
    
    let content = fs.readFileSync(fileFix.file, 'utf8');
    let fileChanged = false;
    
    for (const fix of fileFix.fixes) {
      const newContent = content.replace(fix.pattern, fix.replacement);
      if (newContent !== content) {
        content = newContent;
        fileChanged = true;
        totalChanges++;
      }
    }
    
    if (fileChanged) {
      fs.writeFileSync(fileFix.file, content, 'utf8');
      console.log(`   ✅ ${path.relative(process.cwd(), fileFix.file)}`);
      filesModified++;
    }
  } catch (error) {
    console.log(`   ❌ Error processing ${fileFix.file}: ${error.message}`);
  }
}

console.log(`\n✨ SUMMARY`);
console.log(`==========`);
console.log(`Files processed: ${allFixes.length}`);
console.log(`Files modified: ${filesModified}`);
console.log(`Total changes applied: ${totalChanges}`);

if (totalChanges > 0) {
  console.log(`\n🧪 Running type check...`);
  try {
    execSync('npm run type-check', { stdio: 'inherit' });
    console.log(`✅ Type check passed!`);
  } catch (error) {
    console.log(`⚠️  Type check had issues.`);
  }
  
  console.log(`\n📊 Checking ESLint progress...`);
  try {
    const lintResult = execSync('npm run lint 2>&1 | grep -E "error|warning" | wc -l', { encoding: 'utf8' });
    const remainingIssues = parseInt(lintResult.trim());
    console.log(`Remaining ESLint issues: ${remainingIssues}`);
    
    if (remainingIssues < 90) {
      console.log(`🎉 Significant progress! Reduced from 90 to ${remainingIssues} issues.`);
    }
  } catch (error) {
    console.log(`Could not check ESLint progress automatically`);
  }
}

console.log(`\n🎯 NEXT PHASE`);
console.log(`=============`);
console.log(`After critical errors are fixed, we'll address:`);
console.log(`• React refresh warnings (development-only)`);
console.log(`• Remaining import cleanup`);
console.log(`• Final optimization`);

console.log(`\n🎉 Critical ESLint errors cleanup completed!`);
