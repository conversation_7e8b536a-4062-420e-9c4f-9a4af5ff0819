import React, { useEffect } from 'react';

import {
  IntroductionSection,
  DoctorCredentialsSection,
  AssessmentsSection,
  ComprehensiveServicesSection,
  AMAAccreditationSection,
  IndependentExaminationsSection,
  DetailedProcessSection,
  PersonalInjurySection,
  CertificationSection,
  ServicesSection,
  AssessmentProcessSection,
  LocationsSection,
  ReportingStandardsSection,
  ContactSection,
  FAQSection
} from '@/components/medicolegal';
import PageHeader from '@/components/PageHeader';
import StandardPageLayout from '@/components/StandardPageLayout';
import { useLanguage } from '@/contexts/LanguageContext';
import { medicolegalPageData } from '@/data/medicolegal/medicolegalPageData';
import { en } from '@/locales/en';



const Medicolegal: React.FC = () => {
  const { t } = useLanguage();

  // Safe fallback for translations
  const safeT = t || en;
  const finalT = safeT || {
    // Add minimal fallback structure based on component needs
    nav: { home: "Home", expertise: "Expertise", appointments: "Appointments", contact: "Contact" },
    hero: { title: "Welcome", subtitle: "Professional Care", description: "Expert medical services" },
    footer: { description: "Professional medical practice", quickLinks: "Quick Links", contact: "Contact" }
  };

  useEffect(() => {
    // Scroll to top when component mounts
    window.scrollTo(0, 0);
  }, []);

  return (
    <StandardPageLayout showHeader={false}>
      <PageHeader
        title={finalT.medicolegal?.title || 'Medicolegal Services'}
        subtitle={finalT.medicolegal?.subtitle || 'Expert medicolegal assessments and reports for legal and insurance purposes.'}
        backgroundImage="/images/themis-statue-holding-a-scale-Q8JZGEY-min.jpg"
        enableParallax={true}
      />

      <div className="flex-1">

        {/* Introduction Section */}
        <IntroductionSection introduction={medicolegalPageData.introduction} />

        {/* Doctor Credentials Section - New content from medicolegal.md */}
        <DoctorCredentialsSection doctorCredentials={medicolegalPageData.doctorCredentials} />

        {/* Assessments Section */}
        <AssessmentsSection translations={finalT.medicolegal || {}} />

        {/* Comprehensive Services Section - New detailed services from medicolegal.md */}
        <ComprehensiveServicesSection comprehensiveServices={medicolegalPageData.comprehensiveServices} />

        {/* AMA Accreditation Section - New accreditation content from medicolegal.md */}
        <AMAAccreditationSection />

        {/* Independent Examinations */}
        <IndependentExaminationsSection translations={finalT.medicolegal || {}} />

        {/* Detailed Process Section - New detailed process from medicolegal.md */}
        <DetailedProcessSection />

        {/* Personal Injury Section - New content from medicolegal.md */}
        <PersonalInjurySection />

        {/* Certification Section */}
        <CertificationSection translations={finalT.medicolegal || {}} />
        {/* Services Section */}
        <ServicesSection translations={finalT.medicolegal || {}} />
        {/* Assessment Process Section */}
        <AssessmentProcessSection translations={finalT.medicolegal || {}} />
        {/* Locations Section */}
        <LocationsSection translations={finalT.medicolegal || {}} />

        {/* Reporting Standards Section - New content from medicolegal.md */}
        <ReportingStandardsSection reportingStandards={medicolegalPageData.reportingStandards} />

        {/* Contact Section */}
        <ContactSection translations={finalT.medicolegal || {}} />
        {/* FAQ Section */}
        <FAQSection faq={medicolegalPageData.faq} />
      </div>
    </StandardPageLayout>
  );
};

Medicolegal.displayName = 'Medicolegal';

export default Medicolegal;

