import React from 'react';
import { Helmet } from 'react-helmet-async';

import { StandardPageLayout } from '@/components/layout';
import {
  LocationHero,
  LocationContactInfo,
  LocationMap,
  LocationFacilities,
  LocationAmenities,
  TherapeuticInterventions,
  NearbyAmenities,
  NearbyHospitals,
  OtherConsultingLocations,
  InsuranceAndFunding,
  LocationCTA
} from '@/components/locations';
import { langwarrinLocationData } from '@/data/locations/langwarrinData';

/**
 * Langwarrin Location Page - Refactored
 * Complete refactoring preserving ALL content from the original 614-line file
 * Modular architecture with comprehensive data preservation
 */
const LangwarrinLocation: React.FC = () => {
  return (
    <>
      <Helmet>
        <title>Langwarrin Neurosurgeon | Peninsula Consulting Suites | Dr <PERSON><PERSON></title>
        <meta
          name="description"
          content="Expert neurosurgery and spine care at Peninsula Consulting Suites, Langwarrin. Dr <PERSON><PERSON> provides comprehensive neurosurgical consultations and treatments. Book your appointment today."
        />
        <meta name="keywords" content="neurosurgeon langwarrin, spine surgeon langwarrin, peninsula consulting suites, neurosurgery langwarrin, spine surgery langwarrin" />
        <link rel="canonical" href="https://mineuro.com.au/locations/langwarrin" />
      </Helmet>

      <StandardPageLayout>
        <LocationHero
          title={langwarrinLocationData.hero.title}
          subtitle={langwarrinLocationData.hero.subtitle}
          introduction1={langwarrinLocationData.hero.introduction1}
          introduction2={langwarrinLocationData.hero.introduction2}
          imageUrl={langwarrinLocationData.hero.imageUrl}
        />

        <div className="py-16 bg-muted/30">
          <div className="container">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <LocationContactInfo
                address={langwarrinLocationData.contact.address}
                phone={langwarrinLocationData.contact.phone}
                email={langwarrinLocationData.contact.email}
                hours={langwarrinLocationData.contact.hours}
                consultingHours={langwarrinLocationData.contact.consultingHours}
                appointmentProcess={langwarrinLocationData.contact.appointmentProcess}
              />

              <LocationMap
                embedUrl={langwarrinLocationData.map.embedUrl}
                title={langwarrinLocationData.map.title}
                transportOptions={langwarrinLocationData.map.transportOptions}
                gettingHereTitle={langwarrinLocationData.map.gettingHereTitle}
                publicTransportTitle={langwarrinLocationData.map.publicTransportTitle}
                carTitle={langwarrinLocationData.map.carTitle}
              />
            </div>
          </div>
        </div>

        {langwarrinLocationData.therapeuticInterventions && (
          <TherapeuticInterventions
            title={langwarrinLocationData.therapeuticInterventions.title}
            subtitle={langwarrinLocationData.therapeuticInterventions.subtitle}
            description={langwarrinLocationData.therapeuticInterventions.description}
            interventions={langwarrinLocationData.therapeuticInterventions.interventions}
          />
        )}

        <LocationFacilities
          title={langwarrinLocationData.facilities.title}
          subtitle={langwarrinLocationData.facilities.subtitle}
          description={langwarrinLocationData.facilities.description}
          facilities={langwarrinLocationData.facilities.facilities}
          gallery={langwarrinLocationData.facilities.gallery}
        />

        <LocationAmenities
          title={langwarrinLocationData.amenities.title}
          description={langwarrinLocationData.amenities.description}
          locationDetails={langwarrinLocationData.amenities.locationDetails}
          medicalFacilities={langwarrinLocationData.amenities.medicalFacilities}
          surroundingAmenities={langwarrinLocationData.amenities.surroundingAmenities}
          transportation={langwarrinLocationData.amenities.transportation}
          parking={langwarrinLocationData.amenities.parking}
          images={langwarrinLocationData.amenities.images}
        />

        <NearbyAmenities
          title={langwarrinLocationData.nearbyAmenities.title}
          subtitle={langwarrinLocationData.nearbyAmenities.subtitle}
          description={langwarrinLocationData.nearbyAmenities.description}
          categories={langwarrinLocationData.nearbyAmenities.categories}
        />

        <OtherConsultingLocations
          title={langwarrinLocationData.otherLocations.title}
          subtitle={langwarrinLocationData.otherLocations.subtitle}
          description={langwarrinLocationData.otherLocations.description}
          locations={langwarrinLocationData.otherLocations.locations}
        />

        <NearbyHospitals
          title={langwarrinLocationData.nearbyHospitals.title}
          subtitle={langwarrinLocationData.nearbyHospitals.subtitle}
          description={langwarrinLocationData.nearbyHospitals.description}
          hospitals={langwarrinLocationData.nearbyHospitals.hospitals}
        />

        {langwarrinLocationData.insuranceAndFunding && (
          <InsuranceAndFunding
            title={langwarrinLocationData.insuranceAndFunding.title}
            subtitle={langwarrinLocationData.insuranceAndFunding.subtitle}
            categories={langwarrinLocationData.insuranceAndFunding.categories}
          />
        )}

        <LocationCTA
          title={langwarrinLocationData.cta.title}
          description={langwarrinLocationData.cta.description}
          buttons={langwarrinLocationData.cta.buttons}
        />
      </StandardPageLayout>
    </>
  );
};

LangwarrinLocation.displayName = 'LangwarrinLocation';

export default LangwarrinLocation;
