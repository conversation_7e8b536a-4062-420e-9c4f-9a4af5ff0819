import React, { useEffect } from 'react';
import { Helmet } from 'react-helmet-async';

import {
  AVMAnatomySection,
  AVMGradingSystem,
  AVMTreatmentComparison,
  AVMSymptomAssessment
} from '@/components/medical-conditions/cerebral-avm';
import {
  ConditionHero,
  ConditionOverviewSection,
  ConditionQuickFacts
} from '@/components/medical-conditions/shared';
import { StandardPageLayout } from '@/components/StandardPageLayout';
import { cerebralAVMData } from '@/data/conditions/cerebralAVM';
import { useScrollToTop } from '@/hooks/useScrollToTop';

const CerebralAVM: React.FC = () => {
  useScrollToTop();

  useEffect(() => {
    // Track page view for analytics
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('config', 'GA_MEASUREMENT_ID', {
        pageTitle: 'Cerebral AVM Guide',
        pageLocation: window.location.href
      });
    }
  }, []);

  return (
    <>
      <Helmet>
        <title>Cerebral AVM: Comprehensive Patient Guide | miNEURO</title>
        <meta 
          name="description" 
          content="Complete guide to cerebral arteriovenous malformations (AVMs): causes, symptoms, diagnosis, and treatment options. Expert neurosurgical care with advanced surgical, endovascular, and radiation therapy techniques." 
        />
        <meta 
          name="keywords" 
          content="cerebral AVM, arteriovenous malformation, brain AVM, AVM surgery, endovascular treatment, radiosurgery, brain bleeding, seizures, neurosurgery, Melbourne neurosurgeon" 
        />
        <meta name="author" content="Dr. Ales Aliashkevich" />
        <meta property="og:title" content="Cerebral AVM: Comprehensive Patient Guide | miNEURO" />
        <meta 
          property="og:description" 
          content="Expert guide to cerebral AVMs covering causes, symptoms, diagnosis, and advanced treatment options including surgical, endovascular, and radiation therapy approaches." 
        />
        <meta property="og:type" content="article" />
        <meta property="og:url" content="https://mineuro.com.au/patient-resources/conditions/cerebral-avm" />
        <meta property="og:image" content="https://mineuro.com.au/images/neurological-conditions/cerebral-avm-guide-og.jpg" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="Cerebral AVM: Comprehensive Patient Guide" />
        <meta name="twitter:description" content="Complete guide to cerebral AVMs with expert neurosurgical insights and treatment options." />
        <link rel="canonical" href="https://mineuro.com.au/patient-resources/conditions/cerebral-avm" />
        
        {/* Structured Data for Medical Content */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "MedicalWebPage",
            "name": "Cerebral AVM: Comprehensive Patient Guide",
            "description": "Complete guide to cerebral arteriovenous malformations: causes, symptoms, diagnosis, and treatment options",
            "url": "https://mineuro.com.au/patient-resources/conditions/cerebral-avm",
            "mainEntity": {
              "@type": "MedicalCondition",
              "name": "Cerebral Arteriovenous Malformation",
              "alternateName": ["Cerebral AVM", "Brain AVM", "Arteriovenous Malformation"],
              "description": "Abnormal tangle of blood vessels in the brain where arteries connect directly to veins without normal capillary bed",
              "symptom": [
                "Sudden severe headache",
                "Seizures",
                "Neurological deficits",
                "Bleeding symptoms",
                "Progressive symptoms"
              ],
              "riskFactor": [
                "Congenital development",
                "Family history",
                "Genetic factors",
                "Vascular malformation syndromes"
              ]
            },
            "author": {
              "@type": "Person",
              "name": "Dr. Ales Aliashkevich",
              "jobTitle": "Neurosurgeon",
              "affiliation": {
                "@type": "Organization",
                "name": "miNEURO Brain and Spine Surgery"
              }
            },
            "datePublished": "2024-01-01",
            "dateModified": new Date().toISOString().split('T')[0],
            "publisher": {
              "@type": "Organization",
              "name": "miNEURO Brain and Spine Surgery",
              "url": "https://mineuro.com.au"
            }
          })}
        </script>
      </Helmet>

      <StandardPageLayout 
        title="Cerebral AVM - Comprehensive Guide" 
        showHeader={false}
      >
        <main className="flex-1 pt-20">
          {/* Hero Section */}
          <ConditionHero
            title={cerebralAVMData.hero.title}
            subtitle={cerebralAVMData.hero.subtitle}
            backgroundImage={cerebralAVMData.hero.backgroundImage}
            badge={cerebralAVMData.hero.badge}
            showAssessment={true}
            showBooking={true}
            assessmentLink="#symptom-assessment"
            bookingLink="/appointments"
          />

          {/* Quick Facts */}
          <ConditionQuickFacts facts={cerebralAVMData.quickFacts} />

          {/* Overview Section */}
          <ConditionOverviewSection
            title={cerebralAVMData.overview.title}
            description={cerebralAVMData.overview.description}
            keyPoints={cerebralAVMData.overview.keyPoints}
            imageSrc={cerebralAVMData.overview.imageSrc}
            imageAlt={cerebralAVMData.overview.imageAlt}
            imageCaption={cerebralAVMData.overview.imageCaption}
          />

          {/* Symptom Assessment Tool */}
          <div id="symptom-assessment">
            <AVMSymptomAssessment />
          </div>

          {/* Vascular Anatomy */}
          <AVMAnatomySection
            title={cerebralAVMData.anatomy.title}
            description={cerebralAVMData.anatomy.description}
            vascularComponents={cerebralAVMData.anatomy.vascularComponents}
          />

          {/* Types and Classifications */}
          <section className="section-spacing section-background">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{cerebralAVMData.types.title}</h2>
                <p className="text-lg text-enhanced-body max-w-3xl mx-auto">
                  {cerebralAVMData.types.description}
                </p>
              </div>
              
              <div className="grid gap-8 md:grid-cols-3">
                {cerebralAVMData.types.classifications.map((type, index) => (
                  <div key={index} className="medical-card p-6">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="text-enhanced-heading font-semibold text-xl">{type.type}</h3>
                      <span className={`text-xs px-2 py-1 rounded ${
                        type.type.includes('Superficial') ? 'badge-info' :
                        type.type.includes('Deep') ? 'badge-medical' :
                        'badge-info'
                      }`}>
                        {type.prevalence.split(' ')[0]}
                      </span>
                    </div>
                    <p className="text-muted-foreground mb-4">{type.description}</p>
                    
                    <div className="space-y-3">
                      <div>
                        <h4 className="font-medium text-sm mb-2">Characteristics:</h4>
                        <ul className="space-y-1">
                          {type.characteristics.slice(0, 3).map((char, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                              {char}
                            </li>
                          ))}
                        </ul>
                      </div>
                      
                      <div className="space-y-2 pt-2 border-t border-border">
                        <div className="flex justify-between items-center">
                          <span className="text-enhanced-muted text-xs">Risk Level:</span>
                          <span className="text-xs font-medium">{type.riskLevel}</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-enhanced-muted text-xs">Treatment:</span>
                          <span className="text-xs font-medium">{type.treatmentApproach.split(' ').slice(0, 2).join(' ')}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Spetzler-Martin Grading System */}
          <AVMGradingSystem
            title={cerebralAVMData.grading.title}
            description={cerebralAVMData.grading.description}
            spetzlerMartin={cerebralAVMData.grading.spetzlerMartin}
          />

          {/* Common Locations */}
          <section className="section-spacing section-background-alt">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{cerebralAVMData.locations.title}</h2>
                <p className="text-lg text-enhanced-body max-w-3xl mx-auto">
                  {cerebralAVMData.locations.description}
                </p>
              </div>
              
              <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
                {cerebralAVMData.locations.commonSites.map((site, index) => (
                  <div key={index} className="medical-card p-6">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="text-enhanced-heading font-semibold text-lg">{site.location}</h3>
                      <span className="text-xs px-2 py-1 rounded badge-info">
                        {site.frequency}
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground mb-4">{site.description}</p>
                    
                    <div className="space-y-3">
                      <div>
                        <h4 className="font-medium text-sm mb-2">Common Symptoms:</h4>
                        <ul className="space-y-1">
                          {site.symptoms.slice(0, 2).map((symptom, idx) => (
                            <li key={idx} className="text-xs flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-muted rounded-full mt-1.5 flex-shrink-0" />
                              {symptom}
                            </li>
                          ))}
                        </ul>
                      </div>
                      
                      <div>
                        <h4 className="font-medium text-sm mb-2">Surgical Considerations:</h4>
                        <ul className="space-y-1">
                          {site.surgicalConsiderations.slice(0, 2).map((consideration, idx) => (
                            <li key={idx} className="text-xs flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-info rounded-full mt-1.5 flex-shrink-0" />
                              {consideration}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Symptoms Section */}
          <section className="section-spacing section-background">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">Symptoms and Clinical Presentation</h2>
                <p className="text-lg text-enhanced-body max-w-3xl mx-auto">
                  AVM symptoms depend on location, size, and bleeding history. Many are discovered incidentally before symptoms develop.
                </p>
              </div>
              
              <div className="grid gap-8 md:grid-cols-3">
                {cerebralAVMData.symptoms.map((category, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="flex items-center gap-2 font-semibold text-xl mb-4">
                      <category.icon className="h-5 w-5 text-primary" />
                      {category.category}
                    </h3>
                    <div className="content-spacing-sm">
                      {category.symptoms.map((symptom, idx) => (
                        <div key={idx} className="border-l-4 border-primary/20 pl-4">
                          <div className="flex items-center justify-between mb-1">
                            <h4 className="text-enhanced-strong font-medium">{symptom.name}</h4>
                            <span className={`text-xs px-2 py-1 rounded ${
                              symptom.severity === 'severe' ? 'badge-emergency' :
                              symptom.severity === 'moderate' ? 'badge-routine' :
                              'badge-routine'
                            }`}>
                              {symptom.severity}
                            </span>
                          </div>
                          <p className="text-sm text-muted-foreground mb-1">{symptom.description}</p>
                          <p className="text-enhanced-muted text-xs">Frequency: {symptom.frequency}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Warning Signs Section */}
          <section className="py-16 bg-muted">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-3xl font-bold mb-4 text-foreground">{cerebralAVMData.warningSigns.title}</h2>
                <p className="text-lg text-foreground max-w-3xl mx-auto">
                  {cerebralAVMData.warningSigns.description}
                </p>
              </div>
              
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {cerebralAVMData.warningSigns.emergencySigns.map((sign, index) => (
                  <div key={index} className="bg-background rounded-lg p-6 shadow-md border border-border">
                    <h3 className="font-semibold text-lg mb-2 text-foreground">{sign.sign}</h3>
                    <p className="text-sm text-foreground mb-3">{sign.description}</p>
                    <div className="bg-muted border border-border rounded p-3">
                      <p className="text-sm font-medium text-foreground">{sign.action}</p>
                    </div>
                    <div className="mt-2">
                      <span className={`text-xs px-2 py-1 rounded ${
                        sign.urgency === 'immediate' ? 'bg-muted text-primary-foreground' :
                        sign.urgency === 'urgent' ? 'bg-primary text-primary-foreground' :
                        'bg-primary text-primary-foreground'
                      }`}>
                        {sign.urgency.toUpperCase()}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Diagnosis Section */}
          <section className="section-spacing section-background">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{cerebralAVMData.diagnosis.title}</h2>
                <p className="text-lg text-enhanced-body max-w-3xl mx-auto">
                  {cerebralAVMData.diagnosis.description}
                </p>
              </div>

              <div className="grid gap-8 lg:grid-cols-3">
                {cerebralAVMData.diagnosis.procedures.map((procedure, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="font-semibold text-xl mb-3">{procedure.name}</h3>
                    <p className="text-muted-foreground mb-4">{procedure.description}</p>

                    <div className="space-y-3">
                      <div className="bg-success-light border border-success/30 rounded p-3">
                        <h4 className="font-medium text-sm text-success mb-1">Accuracy</h4>
                        <p className="text-sm text-success">{procedure.accuracy}</p>
                      </div>

                      <div>
                        <h4 className="font-medium text-sm mb-2">Advantages:</h4>
                        <ul className="space-y-1">
                          {procedure.advantages.slice(0, 3).map((advantage, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-success rounded-full mt-1.5 flex-shrink-0" />
                              {advantage}
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="font-medium text-sm mb-2">Limitations:</h4>
                        <ul className="space-y-1">
                          {procedure.limitations.slice(0, 2).map((limitation, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-info rounded-full mt-1.5 flex-shrink-0" />
                              {limitation}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Treatment Comparison */}
          <AVMTreatmentComparison
            title={cerebralAVMData.treatmentModalities.title}
            description={cerebralAVMData.treatmentModalities.description}
            treatments={cerebralAVMData.treatmentModalities.treatments}
          />

          {/* Surgical Options */}
          <section className="py-16 bg-info">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{cerebralAVMData.surgicalOptions.title}</h2>
                <p className="text-lg text-enhanced-body max-w-3xl mx-auto">
                  {cerebralAVMData.surgicalOptions.description}
                </p>
              </div>

              <div className="grid gap-8 lg:grid-cols-3">
                {cerebralAVMData.surgicalOptions.procedures.map((procedure, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="font-semibold text-xl mb-3">{procedure.name}</h3>
                    <p className="text-muted-foreground mb-4">{procedure.description}</p>

                    <div className="content-spacing-sm">
                      <div className="bg-info-light border border-info/30 rounded p-3">
                        <h4 className="font-medium text-sm text-info mb-1">Technique</h4>
                        <p className="text-sm text-foreground">{procedure.technique}</p>
                      </div>

                      <div>
                        <h4 className="font-medium text-sm mb-2">Advantages:</h4>
                        <ul className="space-y-1">
                          {procedure.advantages.slice(0, 3).map((advantage, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-success rounded-full mt-1.5 flex-shrink-0" />
                              {advantage}
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="font-medium text-sm mb-2">Risks:</h4>
                        <ul className="space-y-1">
                          {procedure.risks.slice(0, 2).map((risk, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-muted rounded-full mt-1.5 flex-shrink-0" />
                              {risk}
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div className="flex justify-between items-center pt-2 border-t border-border">
                        <span className="text-enhanced-muted text-xs">Success Rate:</span>
                        <span className="text-xs font-medium">{procedure.successRate}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Endovascular Treatment */}
          <section className="section-spacing section-background">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{cerebralAVMData.endovascularTreatment.title}</h2>
                <p className="text-lg text-enhanced-body max-w-3xl mx-auto">
                  {cerebralAVMData.endovascularTreatment.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-1 max-w-4xl mx-auto">
                {cerebralAVMData.endovascularTreatment.techniques.map((technique, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="font-semibold text-xl mb-4">{technique.name}</h3>
                    <p className="text-muted-foreground mb-4">{technique.description}</p>

                    <div className="grid gap-4 md:grid-cols-2">
                      <div>
                        <h4 className="font-medium text-sm mb-2">Indications:</h4>
                        <ul className="space-y-1">
                          {technique.indications.map((indication, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                              {indication}
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="font-medium text-sm mb-2">Advantages:</h4>
                        <ul className="space-y-1">
                          {technique.advantages.slice(0, 3).map((advantage, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-success rounded-full mt-1.5 flex-shrink-0" />
                              {advantage}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>

                    <div className="mt-4 grid gap-4 md:grid-cols-2">
                      <div>
                        <h4 className="font-medium text-sm mb-2">Limitations:</h4>
                        <ul className="space-y-1">
                          {technique.limitations.slice(0, 3).map((limitation, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-info rounded-full mt-1.5 flex-shrink-0" />
                              {limitation}
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="font-medium text-sm mb-2">Side Effects:</h4>
                        <ul className="space-y-1">
                          {technique.sideEffects.slice(0, 3).map((effect, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-muted rounded-full mt-1.5 flex-shrink-0" />
                              {effect}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Radiation Therapy */}
          <section className="py-16 bg-medical-blue">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{cerebralAVMData.radiationTherapy.title}</h2>
                <p className="text-lg text-enhanced-body max-w-3xl mx-auto">
                  {cerebralAVMData.radiationTherapy.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-2">
                {cerebralAVMData.radiationTherapy.techniques.map((technique, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="font-semibold text-xl mb-4">{technique.name}</h3>
                    <p className="text-muted-foreground mb-4">{technique.description}</p>

                    <div className="content-spacing-sm">
                      <div>
                        <h4 className="font-medium text-sm mb-2">Indications:</h4>
                        <ul className="space-y-1">
                          {technique.indications.map((indication, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                              {indication}
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="font-medium text-sm mb-2">Advantages:</h4>
                        <ul className="space-y-1">
                          {technique.advantages.slice(0, 3).map((advantage, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-success rounded-full mt-1.5 flex-shrink-0" />
                              {advantage}
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="font-medium text-sm mb-2">Limitations:</h4>
                        <ul className="space-y-1">
                          {technique.limitations.slice(0, 3).map((limitation, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-info rounded-full mt-1.5 flex-shrink-0" />
                              {limitation}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Follow-up Care */}
          <section className="section-spacing section-background-alt">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{cerebralAVMData.followUpCare.title}</h2>
                <p className="text-lg text-enhanced-body max-w-3xl mx-auto">
                  {cerebralAVMData.followUpCare.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-3">
                {cerebralAVMData.followUpCare.monitoring.map((timeframe, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="font-semibold text-xl mb-3">{timeframe.timeframe}</h3>
                    <p className="text-sm text-muted-foreground mb-4">{timeframe.purpose}</p>
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm">Required Procedures:</h4>
                      <ul className="space-y-1">
                        {timeframe.procedures.map((procedure, idx) => (
                          <li key={idx} className="text-sm flex items-start gap-2">
                            <div className="w-1.5 h-1.5 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                            {procedure}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Living with AVM */}
          <section className="section-spacing section-background">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{cerebralAVMData.livingWithAVM.title}</h2>
                <p className="text-lg text-enhanced-body max-w-3xl mx-auto">
                  {cerebralAVMData.livingWithAVM.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-3">
                {cerebralAVMData.livingWithAVM.sections.map((section, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="font-semibold text-xl mb-4">{section.title}</h3>
                    <div className="space-y-3 mb-4">
                      {section.content.map((paragraph, idx) => (
                        <p key={idx} className="text-enhanced-muted text-sm">{paragraph}</p>
                      ))}
                    </div>
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm">Practical Tips:</h4>
                      <ul className="space-y-1">
                        {section.tips.map((tip, idx) => (
                          <li key={idx} className="text-xs text-muted-foreground flex items-start gap-2">
                            <div className="w-1.5 h-1.5 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                            {tip}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Support Resources */}
          <section className="py-16 bg-info">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{cerebralAVMData.supportResources.title}</h2>
                <p className="text-lg text-enhanced-body max-w-3xl mx-auto">
                  {cerebralAVMData.supportResources.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-3">
                {cerebralAVMData.supportResources.resources.map((resourceCategory, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="font-semibold text-xl mb-4">{resourceCategory.category}</h3>
                    <div className="content-spacing-sm">
                      {resourceCategory.items.map((item, idx) => (
                        <div key={idx} className="border-b border-border pb-3 last:border-b-0">
                          <h4 className="font-medium mb-1">{item.name}</h4>
                          <p className="text-sm text-muted-foreground mb-1">{item.description}</p>
                          {item.contact && (
                            <p className="text-xs text-primary">{item.contact}</p>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>
        </main>
      </StandardPageLayout>
    </>
  );
};

export default CerebralAVM;
