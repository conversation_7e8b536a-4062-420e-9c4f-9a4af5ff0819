import React from 'react';
import { Helmet } from 'react-helmet-async';

import {
  PeronealAnatomySection,
  PeronealTreatmentComparison,
  PeronealExerciseSection,
  PeronealErgonomicsSection,
  PeronealWarningSignsSection
} from '@/components/medical-conditions/peroneal-nerve-palsy';
import {
  ConditionHero,
  ConditionQuickFacts,
  ConditionOverviewSection,
  ConditionCauses,
  ConditionSymptoms,
  ConditionTreatment
} from '@/components/medical-conditions/shared';
import StandardPageLayout from '@/components/StandardPageLayout';
import { peronealNervePalsyData } from '@/data/conditions/peroneal-nerve-palsy';
import { useScrollToTop } from '@/hooks/useScrollToTop';

/**
 * Peroneal Nerve Palsy Component
 * Comprehensive guide to peroneal nerve palsy and foot drop
 * Provides unparalleled educational value with specialised components
 */
const PeronealNervePalsy: React.FC = () => {
  useScrollToTop();

  const { info, quickFacts, causes, symptoms, diagnostics, treatments, exercises: _exercises, prevention: _prevention, prognosis } = peronealNervePalsyData;

  // Transform treatments for shared component compatibility
  const transformedTreatments = {
    conservative: treatments.filter(t => t.title !== 'Surgical Intervention'),
    surgical: treatments.filter(t => t.title === 'Surgical Intervention')
  };

  return (
    <StandardPageLayout 
      title="Peroneal Nerve Palsy - Comprehensive Guide" 
      showHeader={false}
    >
      <Helmet>
        <title>Peroneal Nerve Palsy (Foot Drop) - Complete Guide | miNEURO</title>
        <meta 
          name="description" 
          content="Comprehensive guide to peroneal nerve palsy and foot drop. Learn about causes, symptoms, diagnosis, treatment options, exercises, and recovery strategies from leading neurological experts." 
        />
        <meta 
          name="keywords" 
          content="peroneal nerve palsy, foot drop, common peroneal nerve, nerve injury, ankle dorsiflexion weakness, AFO, nerve recovery, physiotherapy, nerve compression" 
        />
        <link rel="canonical" href="https://mineuro.com.au/patient-resources/conditions/peroneal-nerve-palsy" />
        
        {/* Open Graph tags */}
        <meta property="og:title" content="Peroneal Nerve Palsy (Foot Drop) - Complete Guide | miNEURO" />
        <meta property="og:description" content="Expert guide to understanding and treating peroneal nerve palsy. Comprehensive information on foot drop, causes, treatments, and recovery strategies." />
        <meta property="og:type" content="article" />
        <meta property="og:url" content="https://mineuro.com.au/patient-resources/conditions/peroneal-nerve-palsy" />
        <meta property="og:image" content="https://mineuro.com.au/images/peripheral-nerve-conditions/peroneal-nerve-palsy-hero.jpg" />
        
        {/* Twitter Card tags */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="Peroneal Nerve Palsy (Foot Drop) - Complete Guide" />
        <meta name="twitter:description" content="Comprehensive guide to peroneal nerve palsy and foot drop treatment options." />
        <meta name="twitter:image" content="https://mineuro.com.au/images/peripheral-nerve-conditions/peroneal-nerve-palsy-hero.jpg" />
        
        {/* Medical/Health specific meta tags */}
        <meta name="health-condition" content="Peroneal Nerve Palsy" />
        <meta name="medical-specialty" content="Neurology, Neurosurgery" />
        <meta name="treatment-type" content="Conservative, Surgical, Rehabilitation" />
      </Helmet>

      <main className="flex-1 pt-20">
        {/* Hero Section */}
        <ConditionHero
          title={info.title}
          subtitle={info.subtitle}
          description={info.description}
          backgroundImage={info.heroImage.src}
          badge="Peripheral Nerve Conditions Library"
        />

        {/* Quick Facts */}
        <ConditionQuickFacts facts={quickFacts} />

        {/* Overview Section */}
        <ConditionOverviewSection
          title="Understanding Peroneal Nerve Palsy"
          description={[
            "Peroneal nerve palsy is a common peripheral nerve injury that results in foot drop - the inability to lift the foot properly during walking. This condition affects the common peroneal nerve as it wraps around the fibular head at the knee, making it vulnerable to compression and injury.",
            "The condition can range from mild weakness to complete paralysis of foot dorsiflexion, significantly impacting mobility and quality of life. Understanding the anatomy, causes, and treatment options is crucial for optimal management and recovery.",
            "With appropriate treatment including assistive devices, rehabilitation, and sometimes surgery, most individuals with peroneal nerve palsy can maintain good functional mobility and independence."
          ]}
          keyPoints={[
            { text: "Most common cause of foot drop in adults" },
            { text: "Often results from nerve compression at the fibular head" },
            { text: "Can significantly impact walking safety and efficiency" },
            { text: "Early treatment optimises recovery potential" },
            { text: "AFO devices provide immediate functional improvement" },
            { text: "Recovery varies from weeks to permanent depending on severity" }
          ]}
          imageSrc={info.heroImage.src}
          imageAlt={info.heroImage.alt}
          imageCaption="Common peroneal nerve anatomy showing vulnerability at the fibular head"
        />

        {/* Causes and Risk Factors */}
        <ConditionCauses
          causes={causes}
        />

        {/* Symptoms */}
        <ConditionSymptoms
          symptomCategories={symptoms}
        />

        {/* Specialised Nerve Anatomy Section */}
        <PeronealAnatomySection />

        {/* Diagnostic Methods */}
        <section className="section-background border-y border-border/50 py-16 md:py-24">
          <div className="container">
            <div className="text-center mb-16">
              <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-6">
                Diagnostic Evaluation
              </h2>
              <p className="text-lg text-foreground/80 max-w-3xl mx-auto">
                Comprehensive assessment to confirm diagnosis, identify underlying causes, and guide treatment planning
              </p>
            </div>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {diagnostics.map((diagnostic, index) => {
                const IconComponent = diagnostic.icon;
                return (
                  <div key={index} className="medical-card p-6">
                    <div className="p-4 rounded-xl bg-primary/10 border border-primary/20 mb-4 w-fit">
                      <IconComponent className="w-8 h-8 text-primary" />
                    </div>
                    <h3 className="text-enhanced-subheading font-semibold mb-3">{diagnostic.title}</h3>
                    <p className="text-enhanced-body text-sm mb-4">{diagnostic.description}</p>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-enhanced-caption">Accuracy:</span>
                        <span className="text-enhanced-body text-sm font-medium">{diagnostic.accuracy}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-enhanced-caption">Duration:</span>
                        <span className="text-enhanced-body text-sm font-medium">{diagnostic.timeRequired}</span>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </section>

        {/* Treatment Options */}
        <ConditionTreatment
          title="Treatment Approaches"
          subtitle="Comprehensive treatment options from immediate functional support to advanced interventions"
          conservativeOptions={transformedTreatments.conservative}
          surgicalOptions={transformedTreatments.surgical}
          treatmentPhases={[
            {
              phase: "Phase 1",
              title: "Immediate Function",
              duration: "Immediate",
              goals: ["Restore safe walking", "Prevent falls", "Maintain mobility"],
              treatments: ["AFO fitting", "Safety assessment", "Gait training", "Fall prevention"]
            },
            {
              phase: "Phase 2", 
              title: "Rehabilitation",
              duration: "3-6 months",
              goals: ["Maximise recovery", "Strengthen muscles", "Improve function"],
              treatments: ["Physical therapy", "Strengthening exercises", "Balance training", "Functional activities"]
            },
            {
              phase: "Phase 3",
              title: "Advanced Options",
              duration: "Variable",
              goals: ["Restore natural function", "Long-term solutions", "Optimise outcomes"],
              treatments: ["Surgical consultation", "Nerve procedures", "Tendon transfers", "Advanced rehabilitation"]
            }
          ]}
        />

        {/* Specialised Treatment Comparison */}
        <PeronealTreatmentComparison />

        {/* Exercise and Physical Therapy Guide */}
        <PeronealExerciseSection />

        {/* Ergonomic and Lifestyle Recommendations */}
        <PeronealErgonomicsSection />

        {/* Warning Signs and When to Seek Help */}
        <PeronealWarningSignsSection />

        {/* Prognosis and Recovery */}
        <section className="section-background-alt border-y border-border/50 py-16 md:py-24">
          <div className="container">
            <div className="text-center mb-16">
              <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-6">
                Prognosis and Recovery Expectations
              </h2>
              <p className="text-lg text-foreground/80 max-w-3xl mx-auto">
                Understanding recovery potential and factors that influence long-term outcomes
              </p>
            </div>
            
            <div className="grid md:grid-cols-3 gap-8">
              {prognosis.map((item, index) => {
                const IconComponent = item.icon;
                return (
                  <div key={index} className="medical-card p-6">
                    <div className="p-4 rounded-xl bg-info/10 border border-info/20 mb-4 w-fit">
                      <IconComponent className="w-8 h-8 text-info" />
                    </div>
                    <h3 className="text-enhanced-subheading font-semibold mb-3">{item.title}</h3>
                    <p className="text-enhanced-body text-sm mb-4">{item.description}</p>
                    <ul className="space-y-2">
                      {item.details.map((detail, detailIndex) => (
                        <li key={detailIndex} className="flex items-start gap-2">
                          <div className="w-1.5 h-1.5 rounded-full bg-info mt-2 flex-shrink-0" />
                          <span className="text-enhanced-body text-sm">{detail}</span>
                        </li>
                      ))}
                    </ul>
                    <div className="mt-4 pt-4 border-t border-border/50">
                      <span className="text-enhanced-caption font-medium">Timeline: </span>
                      <span className="text-enhanced-body text-sm">{item.timeframe}</span>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </section>
      </main>
    </StandardPageLayout>
  );
};

PeronealNervePalsy.displayName = 'PeronealNervePalsy';

export default PeronealNervePalsy;
