#!/usr/bin/env node

import fs from 'fs';
import path from 'path';

console.log('🔍 CHECKING FOR MISSING IMPORTS');
console.log('================================');

// Common lucide-react icons that might be missing
const commonIcons = [
  'AlertTriangle', 'Users', 'TrendingUp', 'BookOpen', 'Clock', 'Target',
  'CheckCircle', 'XCircle', 'Info', 'Warning', 'Error', 'Success',
  'Calendar', 'Phone', 'Mail', 'MapPin', 'ExternalLink', 'ArrowRight',
  'ArrowLeft', 'ChevronRight', 'ChevronLeft', 'Plus', 'Minus', 'X',
  'Search', 'Filter', 'Settings', 'Menu', 'Home', 'User', 'Heart',
  'Star', 'Share', 'Download', 'Upload', 'Edit', 'Delete', 'Save'
];

function scanFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const issues = [];
    
    // Get imports from lucide-react
    const lucideImportMatch = content.match(/import\s*\{([^}]+)\}\s*from\s*['"]lucide-react['"]/);
    const importedIcons = lucideImportMatch 
      ? lucideImportMatch[1].split(',').map(icon => icon.trim())
      : [];
    
    // Check for usage of common icons that might not be imported
    for (const icon of commonIcons) {
      const iconRegex = new RegExp(`<${icon}\\s|${icon}\\.`, 'g');
      if (iconRegex.test(content) && !importedIcons.includes(icon)) {
        issues.push({
          icon,
          file: filePath,
          type: 'missing-import'
        });
      }
    }
    
    return issues;
  } catch (error) {
    return [];
  }
}

function scanDirectory(dir) {
  const issues = [];
  
  try {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        issues.push(...scanDirectory(fullPath));
      } else if (item.endsWith('.tsx') || item.endsWith('.ts')) {
        issues.push(...scanFile(fullPath));
      }
    }
  } catch (error) {
    // Skip directories that can't be read
  }
  
  return issues;
}

console.log('📁 Scanning src directory for missing imports...');

const allIssues = scanDirectory('src');

if (allIssues.length === 0) {
  console.log('✅ No missing imports detected!');
} else {
  console.log(`⚠️  Found ${allIssues.length} potential missing imports:`);
  
  const groupedIssues = {};
  for (const issue of allIssues) {
    if (!groupedIssues[issue.file]) {
      groupedIssues[issue.file] = [];
    }
    groupedIssues[issue.file].push(issue.icon);
  }
  
  for (const [file, icons] of Object.entries(groupedIssues)) {
    console.log(`\n📄 ${path.relative(process.cwd(), file)}`);
    console.log(`   Missing: ${icons.join(', ')}`);
  }
  
  console.log(`\n🔧 RECOMMENDED ACTIONS`);
  console.log(`======================`);
  console.log(`1. Review each file manually to confirm the icons are actually used`);
  console.log(`2. Add missing imports to the lucide-react import statement`);
  console.log(`3. Run 'npm run lint' to verify no new issues are introduced`);
}

console.log(`\n🎉 Missing imports check completed!`);
