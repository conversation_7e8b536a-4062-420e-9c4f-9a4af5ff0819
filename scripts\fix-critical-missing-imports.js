#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

console.log('🔧 FIXING CRITICAL MISSING IMPORTS');
console.log('===================================');

// Critical missing imports that are likely to cause runtime errors
const criticalFixes = [
  {
    file: 'src/components/NetworkStatus.tsx',
    missingIcons: ['AlertTriangle'],
    description: 'Network status component'
  },
  {
    file: 'src/components/medical-conditions/cervical-myelopathy/SpinalAnatomySection.tsx',
    missingIcons: ['AlertTriangle'],
    description: 'Spinal anatomy warnings'
  },
  {
    file: 'src/components/medical-conditions/ulnar-neuropathy/UlnarNerveAnatomySection.tsx',
    missingIcons: ['AlertTriangle'],
    description: 'Ulnar nerve warnings'
  },
  {
    file: 'src/components/patient-resources/spine-conditions/DetailedAnatomySection.tsx',
    missingIcons: ['AlertTriangle'],
    description: 'Spine anatomy warnings'
  },
  {
    file: 'src/components/ui/calendar.tsx',
    missingIcons: ['Calendar'],
    description: 'Calendar UI component'
  },
  {
    file: 'src/components/ui/navigation-menu.tsx',
    missingIcons: ['Menu'],
    description: 'Navigation menu component'
  }
];

let totalChanges = 0;
let filesModified = 0;

console.log(`📁 Processing ${criticalFixes.length} files with critical missing imports...`);

for (const fix of criticalFixes) {
  try {
    if (!fs.existsSync(fix.file)) {
      console.log(`   ⚠️  File not found: ${fix.file}`);
      continue;
    }
    
    const content = fs.readFileSync(fix.file, 'utf8');
    
    // Check if the icons are actually used in the file
    const usedIcons = [];
    for (const icon of fix.missingIcons) {
      const iconRegex = new RegExp(`<${icon}\\s|${icon}\\.`, 'g');
      if (iconRegex.test(content)) {
        usedIcons.push(icon);
      }
    }
    
    if (usedIcons.length === 0) {
      console.log(`   ℹ️  No missing icons found in use: ${path.relative(process.cwd(), fix.file)}`);
      continue;
    }
    
    // Get current lucide-react imports
    const lucideImportMatch = content.match(/import\s*\{([^}]+)\}\s*from\s*['"]lucide-react['"]/);
    
    if (!lucideImportMatch) {
      console.log(`   ⚠️  No lucide-react imports found: ${path.relative(process.cwd(), fix.file)}`);
      continue;
    }
    
    const currentImports = lucideImportMatch[1]
      .split(',')
      .map(imp => imp.trim())
      .filter(imp => imp.length > 0);
    
    // Add missing icons that are actually used
    const iconsToAdd = usedIcons.filter(icon => !currentImports.includes(icon));
    
    if (iconsToAdd.length === 0) {
      console.log(`   ✅ All icons already imported: ${path.relative(process.cwd(), fix.file)}`);
      continue;
    }
    
    // Create new import list
    const newImports = [...currentImports, ...iconsToAdd].sort();
    const newImportString = `import { ${newImports.join(', ')} } from 'lucide-react'`;
    
    // Replace the import
    const newContent = content.replace(
      /import\s*\{[^}]+\}\s*from\s*['"]lucide-react['"]/,
      newImportString
    );
    
    if (newContent !== content) {
      fs.writeFileSync(fix.file, newContent, 'utf8');
      console.log(`   ✅ Added ${iconsToAdd.join(', ')}: ${path.relative(process.cwd(), fix.file)}`);
      totalChanges++;
      filesModified++;
    }
    
  } catch (error) {
    console.log(`   ❌ Error processing ${fix.file}: ${error.message}`);
  }
}

console.log(`\n✨ SUMMARY`);
console.log(`==========`);
console.log(`Files processed: ${criticalFixes.length}`);
console.log(`Files modified: ${filesModified}`);
console.log(`Total changes applied: ${totalChanges}`);

if (totalChanges > 0) {
  console.log(`\n🧪 Running type check...`);
  try {
    execSync('npm run type-check', { stdio: 'inherit' });
    console.log(`✅ Type check passed!`);
  } catch (error) {
    console.log(`⚠️  Type check had issues.`);
  }
  
  console.log(`\n📊 Checking ESLint status...`);
  try {
    execSync('npm run lint', { stdio: 'inherit' });
    console.log(`✅ ESLint passed!`);
  } catch (error) {
    console.log(`⚠️  ESLint found issues.`);
  }
}

console.log(`\n🎯 NEXT STEPS`);
console.log(`=============`);
console.log(`1. Test the application to ensure no runtime errors`);
console.log(`2. Check browser console for any remaining missing import errors`);
console.log(`3. Most other "missing imports" from the scan are likely false positives`);

console.log(`\n🎉 Critical missing imports fix completed!`);
