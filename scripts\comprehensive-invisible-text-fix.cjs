#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// COMPREHENSIVE INVISIBLE TEXT PATTERNS TO FIX
const CRITICAL_INVISIBLE_TEXT_FIXES = [
  // Direct bg-color with text-color matches (MOST CRITICAL)
  {
    pattern: /bg-info(?!-light)([^"]*?)text-info/g,
    replacement: 'bg-info-light/30$1text-foreground',
    desc: 'CRITICAL: bg-info with text-info → readable'
  },
  {
    pattern: /bg-success(?!-light)([^"]*?)text-success/g,
    replacement: 'bg-success-light/30$1text-foreground',
    desc: 'CRITICAL: bg-success with text-success → readable'
  },
  {
    pattern: /bg-error(?!-light)([^"]*?)text-error/g,
    replacement: 'bg-error-light/30$1text-foreground',
    desc: 'CRITICAL: bg-error with text-error → readable'
  },
  {
    pattern: /bg-warning(?!-light)([^"]*?)text-warning/g,
    replacement: 'bg-warning-light/30$1text-foreground',
    desc: 'CRITICAL: bg-warning with text-warning → readable'
  },
  
  // Dark theme patterns
  {
    pattern: /dark:bg-info(?!-light)([^"]*?)dark:text-info/g,
    replacement: 'dark:bg-info-light/20$1dark:text-foreground',
    desc: 'CRITICAL: dark theme bg-info with text-info → readable'
  },
  {
    pattern: /dark:bg-success(?!-light)([^"]*?)dark:text-success/g,
    replacement: 'dark:bg-success-light/20$1dark:text-foreground',
    desc: 'CRITICAL: dark theme bg-success with text-success → readable'
  },
  
  // Reverse patterns (text before bg)
  {
    pattern: /text-info([^"]*?)bg-info(?!-light)/g,
    replacement: 'text-foreground$1bg-info-light/30',
    desc: 'CRITICAL: text-info with bg-info → readable'
  },
  {
    pattern: /text-success([^"]*?)bg-success(?!-light)/g,
    replacement: 'text-foreground$1bg-success-light/30',
    desc: 'CRITICAL: text-success with bg-success → readable'
  },
  
  // Complex border patterns that might indicate invisible text
  {
    pattern: /border-info\/30\/30\/30\/30/g,
    replacement: 'border-info/30',
    desc: 'FIX: Malformed border classes'
  },
  
  // Specific problematic patterns found in search
  {
    pattern: /className="bg-info border-info"/g,
    replacement: 'className="bg-info-light/30 border-info/30"',
    desc: 'CRITICAL: Solid info background → readable'
  },
  
  // Badge patterns that might be invisible
  {
    pattern: /className="bg-info">/g,
    replacement: 'className="bg-info-light/30 text-foreground">',
    desc: 'CRITICAL: Badge bg-info → readable'
  },
  
  // Emergency contact patterns
  {
    pattern: /bg-info\/10 dark:bg-info\/5/g,
    replacement: 'bg-info-light/30 dark:bg-info-light/20',
    desc: 'CRITICAL: Emergency contact backgrounds → readable'
  }
];

function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    let fixCount = 0;
    let fixDetails = [];
    
    CRITICAL_INVISIBLE_TEXT_FIXES.forEach(fix => {
      const matches = content.match(fix.pattern);
      if (matches) {
        content = content.replace(fix.pattern, fix.replacement);
        fixCount += matches.length;
        fixDetails.push(`${fix.desc} (${matches.length} fixes)`);
        modified = true;
      }
    });
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`🔧 FIXED: ${fixCount} invisible text issues in ${filePath}`);
      fixDetails.forEach(detail => console.log(`   🚨 ${detail}`));
      return fixCount;
    }
    
    return 0;
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return 0;
  }
}

function findFiles(dir, extensions = ['.tsx', '.jsx']) {
  let files = [];
  
  try {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        files = files.concat(findFiles(fullPath, extensions));
      } else if (extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    }
  } catch (error) {
    console.error(`❌ Error reading directory ${dir}:`, error.message);
  }
  
  return files;
}

function main() {
  console.log('🚨 COMPREHENSIVE INVISIBLE TEXT FIX - FINAL RESOLUTION...\n');
  
  const srcDir = path.join(process.cwd(), 'src');
  const files = findFiles(srcDir);
  
  console.log(`📁 Found ${files.length} files to process\n`);
  
  let totalFixes = 0;
  let filesFixed = 0;
  
  files.forEach(file => {
    const fixes = processFile(file);
    if (fixes > 0) {
      totalFixes += fixes;
      filesFixed++;
    }
  });
  
  console.log('\n🚨 COMPREHENSIVE INVISIBLE TEXT FIX COMPLETE!');
  console.log(`📊 Total files updated: ${filesFixed}`);
  console.log(`📊 Total invisible text issues fixed: ${totalFixes}`);
  console.log('\n✅ ALL invisible text issues should now be completely resolved!');
  console.log('🔍 All text should now be readable with proper contrast in all themes.');
}

if (require.main === module) {
  main();
}

module.exports = { processFile, findFiles, CRITICAL_INVISIBLE_TEXT_FIXES };
