# Deployment Guide

This document provides comprehensive information about build processes, deployment procedures, and production considerations for the miNEURO application.

## 📋 Table of Contents

- [Pre-Deployment Checklist](#pre-deployment-checklist)
- [Build Process](#build-process)
- [Production Optimization](#production-optimization)
- [Deployment Strategies](#deployment-strategies)
- [Environment Configuration](#environment-configuration)
- [Performance Monitoring](#performance-monitoring)
- [Troubleshooting](#troubleshooting)

## ✅ Pre-Deployment Checklist

### Code Quality Validation

```bash
# Complete pre-deployment validation
npm run production-check

# Individual validation steps
npm run type-check      # TypeScript validation
npm run lint           # Code quality check
npm run test           # Test execution
npm run format:check   # Code formatting validation
npm run build          # Production build test
```

### Content Validation

- [ ] **Medical Content Review**: All medical content professionally reviewed
- [ ] **SEO Metadata**: Complete meta tags and structured data
- [ ] **Accessibility**: WCAG AA+ compliance verified
- [ ] **Images**: All images optimized and have alt text
- [ ] **Links**: All internal and external links tested
- [ ] **Forms**: All forms tested and validated
- [ ] **Error Pages**: 404 and error pages tested

### Performance Validation

- [ ] **Bundle Size**: Analyzed and optimized
- [ ] **Loading Speed**: Tested on various network conditions
- [ ] **Mobile Performance**: Tested on mobile devices
- [ ] **Accessibility**: Screen reader and keyboard navigation tested
- [ ] **Cross-Browser**: Tested on major browsers
- [ ] **SEO**: Search engine optimization verified

## 🔧 Build Process

### Development Build

```bash
# Development build with source maps
npm run build:dev

# Output analysis
ls -la dist/
du -sh dist/
```

### Production Build

```bash
# Full production build
npm run build

# Build with analysis
npm run build -- --analyze

# Verify build output
npm run preview
```

### Build Output Structure

```
dist/
├── assets/
│   ├── index-[hash].js      # Main application bundle (~150KB gzipped)
│   ├── vendor-[hash].js     # React and dependencies (~120KB gzipped)
│   ├── router-[hash].js     # React Router (~25KB gzipped)
│   ├── ui-[hash].js         # UI components (~40KB gzipped)
│   └── index-[hash].css     # Compiled styles (~15KB gzipped)
├── images/
│   ├── conditions/          # Medical condition images
│   ├── locations/           # Clinic location images
│   └── general/             # General site images
├── icons/
│   ├── favicon.ico
│   ├── apple-touch-icon.png
│   └── manifest-icons/
├── index.html               # Main HTML file
├── manifest.json            # Web app manifest
├── robots.txt               # Search engine directives
└── sitemap.xml              # Site structure for search engines
```

### Bundle Analysis

```bash
# Analyze bundle composition
npx vite-bundle-analyzer dist

# Check bundle sizes
npm run build -- --analyze

# Performance budget check
npm run build:check-size
```

**Target Bundle Sizes**:
- **Main Bundle**: < 200KB gzipped
- **Vendor Bundle**: < 150KB gzipped
- **Total Initial Load**: < 400KB gzipped
- **Individual Chunks**: < 50KB gzipped

## ⚡ Production Optimization

### Code Optimization

```typescript
// Vite production configuration
export default defineConfig({
  build: {
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom'],
          ui: ['@radix-ui/react-slot', '@radix-ui/react-tabs'],
          utils: ['clsx', 'tailwind-merge'],
        },
      },
    },
    chunkSizeWarningLimit: 1000,
  },
})
```

### Asset Optimization

1. **Image Optimization**:
   ```bash
   # Optimize images before deployment
   npx imagemin src/assets/images/* --out-dir=dist/images
   
   # WebP conversion for modern browsers
   npx imagemin src/assets/images/* --out-dir=dist/images --plugin=webp
   ```

2. **CSS Optimization**:
   - Tailwind CSS purging removes unused styles
   - Critical CSS inlined in HTML
   - Non-critical CSS loaded asynchronously

3. **JavaScript Optimization**:
   - Tree shaking removes unused code
   - Code splitting reduces initial bundle size
   - Dynamic imports for route-based loading

### Performance Features

```typescript
// Lazy loading implementation
const MedicalConditionsPage = lazy(() => 
  import('@/pages/medical-conditions/MedicalConditionsPage')
)

// Preloading critical routes
const preloadRoute = (routeComponent: () => Promise<any>) => {
  const componentImport = routeComponent()
  return componentImport
}

// Image lazy loading
<img 
  src="/images/condition.jpg"
  alt="Medical condition illustration"
  loading="lazy"
  decoding="async"
  width={400}
  height={300}
/>
```

## 🚀 Deployment Strategies

### Static Site Deployment

**Recommended Platforms**:
1. **Netlify** (Recommended for medical sites)
2. **Vercel** (Excellent performance)
3. **AWS S3 + CloudFront** (Enterprise solution)
4. **GitHub Pages** (Simple deployment)

### Netlify Deployment

**netlify.toml**:
```toml
[build]
  publish = "dist"
  command = "npm run build"

[build.environment]
  NODE_VERSION = "18"
  NPM_VERSION = "9"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/*.html"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
```

### Vercel Deployment

**vercel.json**:
```json
{
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "framework": "vite",
  "rewrites": [
    {
      "source": "/(.*)",
      "destination": "/index.html"
    }
  ],
  "headers": [
    {
      "source": "/assets/(.*)",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "public, max-age=31536000, immutable"
        }
      ]
    }
  ]
}
```

### AWS S3 + CloudFront

**Deployment Script**:
```bash
#!/bin/bash
# AWS deployment script

# Build the application
npm run build

# Sync to S3 bucket
aws s3 sync dist/ s3://mineuro-website --delete

# Invalidate CloudFront cache
aws cloudfront create-invalidation \
  --distribution-id E1234567890123 \
  --paths "/*"

echo "Deployment completed successfully"
```

## 🌍 Environment Configuration

### Production Environment Variables

```bash
# .env.production
VITE_APP_TITLE="miNEURO - Neurosurgical Excellence"
VITE_APP_URL="https://mineuro.com.au"
VITE_API_BASE_URL="https://api.mineuro.com.au"
VITE_GOOGLE_ANALYTICS_ID="G-XXXXXXXXXX"
VITE_GOOGLE_TAG_MANAGER_ID="GTM-XXXXXXX"
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_ERROR_REPORTING=true
VITE_DEV_MODE=false
```

### Security Headers

```nginx
# Nginx configuration for security headers
add_header X-Frame-Options "DENY" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://www.googletagmanager.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:;" always;
```

### SSL/TLS Configuration

```nginx
# SSL configuration
ssl_certificate /path/to/certificate.crt;
ssl_certificate_key /path/to/private.key;
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
ssl_prefer_server_ciphers off;
ssl_session_cache shared:SSL:10m;
ssl_session_timeout 10m;
```

## 📊 Performance Monitoring

### Core Web Vitals Targets

- **Largest Contentful Paint (LCP)**: < 2.5 seconds
- **First Input Delay (FID)**: < 100 milliseconds
- **Cumulative Layout Shift (CLS)**: < 0.1
- **First Contentful Paint (FCP)**: < 1.8 seconds
- **Time to Interactive (TTI)**: < 3.8 seconds

### Monitoring Setup

```typescript
// Performance monitoring
const reportWebVitals = (metric: any) => {
  if (import.meta.env.PROD) {
    // Send to analytics service
    gtag('event', metric.name, {
      event_category: 'Web Vitals',
      value: Math.round(metric.value),
      event_label: metric.id,
      non_interaction: true,
    })
  }
}

// Usage in main.tsx
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals'

getCLS(reportWebVitals)
getFID(reportWebVitals)
getFCP(reportWebVitals)
getLCP(reportWebVitals)
getTTFB(reportWebVitals)
```

### Error Monitoring

```typescript
// Error boundary with reporting
class ErrorBoundary extends Component {
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    if (import.meta.env.PROD) {
      // Report to error tracking service
      console.error('Application error:', error, errorInfo)
      
      // Send to analytics
      gtag('event', 'exception', {
        description: error.message,
        fatal: false,
      })
    }
  }
}
```

## 🔍 Troubleshooting

### Common Deployment Issues

1. **Build Failures**:
   ```bash
   # Clear cache and rebuild
   rm -rf node_modules/.vite dist
   npm ci
   npm run build
   ```

2. **Routing Issues (404 on refresh)**:
   - Ensure SPA fallback is configured
   - Check server rewrite rules
   - Verify `index.html` fallback

3. **Asset Loading Issues**:
   ```bash
   # Check asset paths
   grep -r "src=" dist/index.html
   
   # Verify base URL configuration
   grep -r "base:" vite.config.ts
   ```

4. **Performance Issues**:
   ```bash
   # Analyze bundle size
   npm run build -- --analyze
   
   # Check for large dependencies
   npx webpack-bundle-analyzer dist/assets
   ```

### Health Checks

```bash
# Deployment health check script
#!/bin/bash

echo "🔍 Running deployment health checks..."

# Check if site is accessible
curl -f https://mineuro.com.au || exit 1

# Check critical pages
curl -f https://mineuro.com.au/medical-conditions || exit 1
curl -f https://mineuro.com.au/patient-resources || exit 1
curl -f https://mineuro.com.au/locations || exit 1

# Check performance
lighthouse https://mineuro.com.au --only-categories=performance --chrome-flags="--headless"

echo "✅ All health checks passed!"
```

### Rollback Procedure

```bash
# Emergency rollback script
#!/bin/bash

echo "🚨 Initiating rollback..."

# Restore previous version from backup
aws s3 sync s3://mineuro-website-backup/previous/ s3://mineuro-website/ --delete

# Invalidate CDN cache
aws cloudfront create-invalidation --distribution-id E1234567890123 --paths "/*"

echo "✅ Rollback completed"
```

## 📈 Post-Deployment Validation

### Automated Testing

```bash
# Post-deployment test suite
npm run test:e2e:production

# Performance testing
npm run test:lighthouse

# Accessibility testing
npm run test:a11y:production

# SEO validation
npm run test:seo
```

### Manual Validation Checklist

- [ ] **Homepage loads correctly**
- [ ] **Navigation works on all devices**
- [ ] **Medical condition pages accessible**
- [ ] **Patient resources functional**
- [ ] **Location pages display correctly**
- [ ] **Contact forms working**
- [ ] **Search functionality operational**
- [ ] **Mobile experience optimized**
- [ ] **Accessibility features working**
- [ ] **Analytics tracking active**

## 📚 Additional Resources

- [Vite Deployment Guide](https://vitejs.dev/guide/static-deploy.html) - Static deployment options
- [Netlify Documentation](https://docs.netlify.com/) - Netlify-specific deployment
- [Vercel Documentation](https://vercel.com/docs) - Vercel deployment guide
- [AWS S3 Static Hosting](https://docs.aws.amazon.com/s3/latest/userguide/WebsiteHosting.html) - AWS deployment
- [Web Vitals](https://web.dev/vitals/) - Performance monitoring guide
- [Lighthouse CI](https://github.com/GoogleChrome/lighthouse-ci) - Automated performance testing
