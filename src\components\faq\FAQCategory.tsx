import { HelpCir<PERSON>, Clock, Users } from 'lucide-react';
import React from 'react';

import SafeImage from '@/components/SafeImage';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from "@/components/ui/accordion";
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { FAQCategory as FAQCategoryType, FAQItem } from '@/data/faq/faqData';
import { cn } from '@/lib/utils';

interface FAQCategoryProps {
  category: FAQCategoryType;
  categoryIndex: number;
  isHighlighted?: boolean;
}

const FAQCategory: React.FC<FAQCategoryProps> = ({
  category,
  categoryIndex,
  isHighlighted = false
}) => {
  const deviceInfo = useDeviceDetection();

  // Category icons mapping
  const getCategoryIcon = (title: string) => {
    if (title.includes('General')) return <HelpCircle className="h-5 w-5" />;
    if (title.includes('Cervical') || title.includes('Lumbar')) return <Users className="h-5 w-5" />;
    if (title.includes('Advanced') || title.includes('Robotic')) return <Clock className="h-5 w-5" />;
    if (title.includes('Pain') || title.includes('Recovery')) return <Clock className="h-5 w-5" />;
    if (title.includes('Insurance') || title.includes('Costs')) return <Users className="h-5 w-5" />;
    return <HelpCircle className="h-5 w-5" />;
  };

  return (
    <section
      id={`category-${categoryIndex}`}
      aria-labelledby={`category-${categoryIndex}-heading`}
      className={cn(
        "scroll-mt-24 transition-all duration-300 animate-fade-in-up",
        isHighlighted && "ring-2 ring-primary ring-opacity-50 rounded-xl"
      )}
      style={{ animationDelay: `${categoryIndex * 0.1}s` }}
    >
      {/* Premium Category Header */}
      <div className={cn(
        "bg-card/90 backdrop-blur-sm border border-border/50 shadow-xl",
        "rounded-2xl relative overflow-hidden",
        deviceInfo.isMobile ? "p-6 mb-8" : "p-10 mb-12"
      )}>
        {/* Decorative background elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-primary/5 pointer-events-none" />

        <div className="flex items-start gap-6 relative z-10">
          <div className="flex-shrink-0 p-4 bg-gradient-to-br from-primary via-info-light to-info-light rounded-xl shadow-lg shadow-primary/25">
            {getCategoryIcon(category.title)}
          </div>
          <div className="flex-1">
            <div className="flex items-center gap-6 mb-4">
              <h3
                id={`category-${categoryIndex}-heading`}
                className={cn(
                  "font-bold text-foreground",
                  deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
                )}
              >
                {category.title}
              </h3>
              <Badge
                variant="secondary"
                className="bg-primary/10 text-primary border-primary/20 font-bold px-4 py-2 shadow-md text-base"
              >
                {category.questions.length} questions
              </Badge>
            </div>

            {category.description && (
              <p className="text-foreground/80 leading-relaxed font-medium text-lg">
                {category.description}
              </p>
            )}
          </div>
        </div>

        {/* Category-specific image */}
        {category.imageSrc && (
          <div className="relative rounded-lg overflow-hidden mt-4 shadow-md">
            <SafeImage
              src={category.imageSrc}
              alt={category.imageAlt || `${category.title} illustration`}
              className="w-full h-auto max-h-[250px] object-cover"
              fallbackSrc="/images/medical-consulting.jpg"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
          </div>
        )}
      </div>

      {/* Premium FAQ Accordion */}
      <Accordion
        type="single"
        collapsible
        className="space-y-4"
        aria-label={`${category.title} frequently asked questions`}
      >
        {category.questions.map((faq: FAQItem, faqIndex: number) => (
          <AccordionItem
            key={faqIndex}
            value={`item-${categoryIndex}-${faqIndex}`}
            className={cn(
              "group border-0 relative overflow-hidden",
              "bg-card/90 backdrop-blur-sm",
              "rounded-2xl shadow-xl border border-border/50",
              "hover:shadow-2xl hover:border-primary/30 hover:scale-[1.02] transform-gpu transition-all duration-300"
            )}
          >
            {/* Decorative background for questions */}
            <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-primary/5 pointer-events-none" />

            <AccordionTrigger
              className={cn(
                "text-left font-bold hover:no-underline relative z-10",
                "text-foreground group-hover:text-primary transition-colors duration-300",
                "[&[data-state=open]]:text-primary [&[data-state=open]]:border-b [&[data-state=open]]:border-border/50",
                deviceInfo.isMobile ? "px-6 py-5" : "px-8 py-6"
              )}
              aria-describedby={`answer-${categoryIndex}-${faqIndex}`}
            >
              <div className="flex items-start gap-4 w-full">
                <div className="flex-shrink-0 mt-1">
                  <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-primary via-info-light to-info-light flex items-center justify-center shadow-lg shadow-primary/25">
                    <span className="text-sm font-bold">
                      {faqIndex + 1}
                    </span>
                  </div>
                </div>
                <span className="flex-1 pr-4 leading-relaxed text-base">
                  {faq.question}
                </span>
              </div>
            </AccordionTrigger>
            <AccordionContent
              id={`answer-${categoryIndex}-${faqIndex}`}
              className={cn(
                "relative z-10",
                deviceInfo.isMobile ? "px-6 pb-8 pt-3" : "px-8 pb-8 pt-4"
              )}
            >
              <div className="pl-12 space-y-6">
                <div className="bg-background/70 dark:bg-muted/70 backdrop-blur-sm rounded-xl p-6 border border-border/30 shadow-md">
                  {faq.answer.split('\n').map((paragraph: string, i: number) => (
                    <p
                      key={i}
                      className="text-foreground/80 leading-relaxed font-medium mb-4 last:mb-0 text-base"
                    >
                      {paragraph}
                    </p>
                  ))}
                </div>

                {/* Enhanced helpful actions */}
                <div className="flex items-center gap-4 pt-6 border-t border-border/30">
                  <span className="text-base text-foreground/70 font-semibold">
                    Was this helpful?
                  </span>
                  <Button variant="ghost" className="">
                    👍 Yes
                  </Button>
                  <Button variant="ghost" className="">
                    👎 No
                  </Button>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </section>
  );
};

FAQCategory.displayName = 'FAQCategory';

export default FAQCategory;
