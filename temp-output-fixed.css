@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600&display=swap');

*, ::before, ::after{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box;
  /* 1 */
  border-width: 0;
  /* 2 */
  border-style: solid;
  /* 2 */
  border-color: #e5e7eb;
  /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
  -moz-tab-size: 4;
  /* 3 */
  -o-tab-size: 4;
     tab-size: 4;
  /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  /* 4 */
  font-feature-settings: normal;
  /* 5 */
  font-variation-settings: normal;
  /* 6 */
  -webkit-tap-highlight-color: transparent;
  /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0;
  /* 1 */
  line-height: inherit;
  /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0;
  /* 1 */
  color: inherit;
  /* 2 */
  border-top-width: 1px;
  /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  /* 1 */
  font-feature-settings: normal;
  /* 2 */
  font-variation-settings: normal;
  /* 3 */
  font-size: 1em;
  /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0;
  /* 1 */
  border-color: inherit;
  /* 2 */
  border-collapse: collapse;
  /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-feature-settings: inherit;
  /* 1 */
  font-variation-settings: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  font-weight: inherit;
  /* 1 */
  line-height: inherit;
  /* 1 */
  letter-spacing: inherit;
  /* 1 */
  color: inherit;
  /* 1 */
  margin: 0;
  /* 2 */
  padding: 0;
  /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button;
  /* 1 */
  background-color: transparent;
  /* 2 */
  background-image: none;
  /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  /* 1 */
  vertical-align: middle;
  /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

:root {
  /* Enhanced Professional Medical Theme - WCAG AA Compliant */
  --background: 0 0% 100%;
  --foreground: 215 25% 12%;
  --card: 0 0% 100%;
  --card-foreground: 215 25% 12%;
  --popover: 0 0% 100%;
  --popover-foreground: 215 25% 12%;
  --primary: 210 85% 40%;
  --primary-foreground: 0 0% 100%;
  --secondary: 210 30% 96%;
  --secondary-foreground: 215 25% 18%;
  --muted: 210 25% 95%;
  --muted-foreground: 215 20% 32%;
  --accent: 210 25% 96%;
  --accent-foreground: 215 25% 18%;
  --destructive: 0 65% 45%;
  --destructive-foreground: 0 0% 100%;
  --border: 210 15% 85%;
  --input: 210 15% 85%;
  --ring: 210 85% 40%;
  --radius: 0.5rem;
  /* Enhanced Professional Medical Color Palette */
  --medical-blue: 210 85% 40%;
  --medical-blue-foreground: 0 0% 100%;
  --medical-blue-light: 210 85% 97%;
  --medical-blue-dark: 210 85% 30%;
  --success: 142 65% 32%;
  --success-foreground: 0 0% 100%;
  --success-light: 142 65% 96%;
  --warning: 38 85% 48%;
  --warning-foreground: 0 0% 100%;
  --warning-light: 38 85% 96%;
  --error: 0 70% 47%;
  --error-foreground: 0 0% 100%;
  --error-light: 0 70% 96%;
  --info: 199 80% 40%;
  --info-foreground: 0 0% 100%;
  --info-light: 199 80% 96%;
  /* Professional Trust Colors */
  --trust-navy: 215 85% 22%;
  --trust-navy-light: 215 85% 95%;
  --trust-teal: 180 65% 32%;
  --trust-teal-light: 180 65% 95%;
}

.dark {
  /* Enhanced Professional Dark Theme - Improved Medical Readability */
  --background: 220 20% 8%;
  --foreground: 220 10% 98%;
  --card: 220 15% 12%;
  --card-foreground: 220 10% 98%;
  --popover: 220 15% 12%;
  --popover-foreground: 220 10% 98%;
  --primary: 210 80% 70%;
  --primary-foreground: 220 20% 8%;
  --secondary: 220 12% 18%;
  --secondary-foreground: 220 10% 92%;
  --muted: 220 12% 18%;
  --muted-foreground: 220 10% 78%;
  --accent: 220 12% 18%;
  --accent-foreground: 220 10% 92%;
  --destructive: 0 60% 65%;
  --destructive-foreground: 220 10% 98%;
  --border: 220 12% 25%;
  --input: 220 12% 25%;
  --ring: 210 80% 70%;
  /* Enhanced Professional Dark Medical Colors - Better Contrast */
  --medical-blue: 210 80% 72%;
  --medical-blue-foreground: 220 20% 8%;
  --medical-blue-light: 210 80% 15%;
  --medical-blue-dark: 210 80% 78%;
  --success: 142 60% 68%;
  --success-foreground: 220 20% 8%;
  --success-light: 142 60% 15%;
  --warning: 38 80% 70%;
  --warning-foreground: 220 20% 8%;
  --warning-light: 38 80% 15%;
  --error: 0 65% 75%;
  --error-foreground: 220 20% 8%;
  --error-light: 0 65% 15%;
  --info: 199 75% 70%;
  --info-foreground: 220 20% 8%;
  --info-light: 199 75% 15%;
  /* Professional Dark Trust Colors - Enhanced Visibility */
  --trust-navy: 215 80% 55%;
  --trust-navy-light: 215 80% 15%;
  --trust-teal: 180 60% 65%;
  --trust-teal-light: 180 60% 15%;
}

* {
  border-color: hsl(var(--border));
}

html, body {
  margin: 0;
  padding: 0;
  border: none;
  outline: none;
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: 'Inter', sans-serif;
  line-height: 1.6;
  font-weight: 400;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Playfair Display', serif;
  font-weight: 600;
  line-height: 1.2;
  color: hsl(var(--foreground));
}

h1{
  font-size: 2.25rem;
  line-height: 2.5rem;
  font-weight: 700;
  line-height: 1.25;
  letter-spacing: -0.025em;
}

@media (min-width: 768px){
  h1{
    font-size: 3rem;
    line-height: 1;
  }
}

@media (min-width: 1024px){
  h1{
    font-size: 3.75rem;
    line-height: 1;
  }
}

h1 {
  margin-bottom: 1.5rem;
  color: hsl(var(--foreground));
}

h2{
  font-size: 1.875rem;
  line-height: 2.25rem;
  font-weight: 600;
  line-height: 1.25;
  letter-spacing: -0.025em;
}

@media (min-width: 768px){
  h2{
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
}

@media (min-width: 1024px){
  h2{
    font-size: 3rem;
    line-height: 1;
  }
}

h2 {
  margin-bottom: 1.25rem;
  color: hsl(var(--foreground));
}

h3{
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 600;
  line-height: 1.375;
  letter-spacing: -0.025em;
}

@media (min-width: 768px){
  h3{
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
}

@media (min-width: 1024px){
  h3{
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
}

h3 {
  margin-bottom: 1rem;
  color: hsl(var(--foreground));
}

h4{
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-weight: 500;
  line-height: 1.375;
}

@media (min-width: 768px){
  h4{
    font-size: 1.5rem;
    line-height: 2rem;
  }
}

@media (min-width: 1024px){
  h4{
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
}

h4 {
  margin-bottom: 0.875rem;
  color: hsl(var(--foreground));
}

h5{
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 500;
  line-height: 1.5;
}

@media (min-width: 768px){
  h5{
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1024px){
  h5{
    font-size: 1.5rem;
    line-height: 2rem;
  }
}

h5 {
  margin-bottom: 0.75rem;
  color: hsl(var(--foreground));
}

h6{
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 500;
  line-height: 1.5;
}

@media (min-width: 768px){
  h6{
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1024px){
  h6{
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

h6 {
  margin-bottom: 0.625rem;
  color: hsl(var(--foreground));
}

p{
  font-size: 1rem;
  line-height: 1.5rem;
  line-height: 1.625;
  color: hsl(var(--foreground) / 0.9);
}

@media (min-width: 768px){
  p{
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

p {
  margin-bottom: 1rem;
  line-height: 1.7;
}

.lead{
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 400;
  line-height: 1.625;
  color: hsl(var(--foreground) / 0.95);
}

@media (min-width: 768px){
  .lead{
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

.lead {
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

strong, b {
  font-weight: 600;
  color: hsl(var(--foreground));
}

em, i{
  font-style: italic;
  color: hsl(var(--foreground) / 0.95);
}

/* Enhanced Link Styling for Dark Theme */

a{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
  animation-duration: 200ms;
}

/* Broken CSS rules removed */

/* Enhanced text hierarchy classes */

.text-hero{
  font-size: 3rem;
  font-weight: 700;
  line-height: 1.25;
  letter-spacing: -0.025em;
}

@media (min-width: 768px){
  .text-hero{
    font-size: 3.75rem;
    line-height: 1;
  }
}

@media (min-width: 1024px){
  .text-hero{
    font-size: 4.5rem;
    line-height: 1;
  }
}

.text-hero {
  color: hsl(var(--foreground));
  margin-bottom: 2rem;
}

.text-headline{
  font-size: 1.875rem;
  line-height: 2.25rem;
  font-weight: 600;
  line-height: 1.25;
  letter-spacing: -0.025em;
}

@media (min-width: 768px){
  .text-headline{
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
}

@media (min-width: 1024px){
  .text-headline{
    font-size: 3rem;
    line-height: 1;
  }
}

.text-headline {
  color: hsl(var(--foreground));
  margin-bottom: 1.25rem;
}

.text-title{
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 600;
  line-height: 1.375;
  letter-spacing: -0.025em;
}

@media (min-width: 768px){
  .text-title{
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
}

@media (min-width: 1024px){
  .text-title{
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
}

.text-title {
  color: hsl(var(--foreground));
  margin-bottom: 1rem;
}

.text-subtitle{
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-weight: 500;
  line-height: 1.375;
}

@media (min-width: 768px){
  .text-subtitle{
    font-size: 1.5rem;
    line-height: 2rem;
  }
}

@media (min-width: 1024px){
  .text-subtitle{
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
}

.text-subtitle {
  color: hsl(var(--foreground));
  margin-bottom: 0.875rem;
}

/* Medical Content Typography */

.medical-heading{
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 700;
  line-height: 1.25;
}

@media (min-width: 768px){
  .medical-heading{
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
}

.medical-heading {
  color: hsl(var(--foreground));
  margin-bottom: 1.5rem;
  border-bottom: 2px solid hsl(var(--primary) / 0.3);
  padding-bottom: 0.5rem;
}

.medical-subheading{
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 600;
  line-height: 1.375;
}

@media (min-width: 768px){
  .medical-subheading{
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

.medical-subheading {
  color: hsl(var(--foreground));
  margin-bottom: 1rem;
  margin-top: 2rem;
}

.medical-body{
  font-size: 1rem;
  line-height: 1.5rem;
  color: hsl(var(--foreground) / 0.9);
  margin-bottom: 1.25rem;
  line-height: 1.7;
}

.medical-caption{
  font-size: 0.875rem;
  line-height: 1.25rem;
  line-height: 1.5;
  color: hsl(var(--muted-foreground));
  margin-top: 0.5rem;
  font-style: italic;
}

/* Remove any browser default progress bars or loading indicators */

::-webkit-progress-bar,
  ::-webkit-progress-value,
  progress {
  display: none !important;
}

.container{
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 1rem;
  padding-left: 1rem;
}

@media (min-width: 640px){
  .container{
    max-width: 640px;
    padding-right: 1.5rem;
    padding-left: 1.5rem;
  }
}

@media (min-width: 768px){
  .container{
    max-width: 768px;
    padding-right: 2rem;
    padding-left: 2rem;
  }
}

@media (min-width: 1024px){
  .container{
    max-width: 1024px;
    padding-right: 2.5rem;
    padding-left: 2.5rem;
  }
}

@media (min-width: 1280px){
  .container{
    max-width: 1280px;
    padding-right: 3rem;
    padding-left: 3rem;
  }
}

@media (min-width: 1400px){
  .container{
    max-width: 1400px;
    padding-right: 4rem;
    padding-left: 4rem;
  }
}

/* ========================================
   * UNIFIED BUTTON SYSTEM
   * ========================================
   * ALL buttons use the buttonVariants system from /lib/button-variants.ts
   * NO additional CSS overrides should be added here
   * This ensures consistent styling across the entire application
   * ======================================== */

.container{
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 768px){
  .container{
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px){
  .container{
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

/* UNIFIED BUTTON SYSTEM - All buttons use buttonVariants */

.dark .medical-card-interactive:focus-within {
  outline-color: hsl(var(--primary) / 0.7);
  box-shadow: 0 0 0 4px hsl(var(--primary) / 0.2);
}

.section{
  padding-top: 3rem;
  padding-bottom: 3rem;
}

@media (min-width: 768px){
  .section{
    padding-top: 4rem;
    padding-bottom: 4rem;
  }
}

@media (min-width: 1024px){
  .section{
    padding-top: 6rem;
    padding-bottom: 6rem;
  }
}

/* Enhanced Glass Card with Better Contrast */

.glass-card{
  border-radius: 0.75rem;
  border-width: 1px;
  border-color: hsl(var(--border) / 0.5);
  background-color: hsl(var(--card) / 0.95);
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  --tw-backdrop-blur: blur(16px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

/* Enhanced Professional Card Styling with Better Spacing */

.medical-card{
  border-radius: 0.75rem;
  border-width: 1px;
  border-color: hsl(var(--border) / 0.4);
  background-color: hsl(var(--card));
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  animation-duration: 300ms;
}

.medical-card:hover{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.medical-card {
  padding: 2.5rem;
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
}

.medical-card-content > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}

.medical-card-content{
  color: hsl(var(--card-foreground));
}

.medical-card-inner{
  border-radius: var(--radius);
  border-width: 1px;
  border-color: hsl(var(--border) / 0.2);
  background-color: hsl(var(--card) / 0.8);
  padding: 2rem;
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
}

/* Enhanced Card Variants */

.medical-card-elevated{
  border-radius: 0.75rem;
  border-width: 1px;
  border-color: hsl(var(--border) / 0.4);
  background-color: hsl(var(--card));
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  animation-duration: 300ms;
}

.medical-card-elevated:hover{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.medical-card-elevated {
  padding: 2.5rem;
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
}

.dark .medical-card-elevated {
  background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--card) / 0.98) 100%);
  border-color: hsl(var(--border) / 0.5);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.dark .medical-card-elevated:hover {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border-color: hsl(var(--border) / 0.7);
}

@media (prefers-contrast: high) {
  .medical-card-elevated {
    border-width: 2px;
    border-color: hsl(var(--border));
  }
}

@supports (-webkit-backdrop-filter: blur(10px)) {
  .medical-card-elevated {
    -webkit-backdrop-filter: blur(8px);
  }
}

@-moz-document url-prefix() {
  .medical-card-elevated {
    background-color: hsl(var(--card) / 0.95);
  }
}

@supports not ((-webkit-backdrop-filter: blur(8px)) or (backdrop-filter: blur(8px))) {
  .medical-card-elevated {
    background-color: hsl(var(--card) / 0.98);
  }
}

@media (min-resolution: 192dpi) {
  .medical-card-elevated {
    border-width: 0.5px;
  }
}

@media print {
  .medical-card-elevated {
    background: white;
    border: 1px solid #000;
    box-shadow: none;
    -webkit-backdrop-filter: none;
            backdrop-filter: none;
  }
}

.medical-card-elevated {
  contain: layout style paint;
  isolation: isolate;
}

@media (prefers-reduced-data: reduce) {
  .medical-card-elevated {
    -webkit-backdrop-filter: none;
            backdrop-filter: none;
    background-color: hsl(var(--card));
  }
}

.medical-card-elevated{
  border-color: hsl(var(--border) / 0.5);
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

@media (hover: none) and (pointer: coarse) {
  .medical-card-elevated:hover {
    box-shadow: none;
  }
}

@media (prefers-contrast: high) {
  .medical-card-elevated {
    border-width: 2px;
  }
}

.medical-card-elevated {
  background-color: white;
  /* Fallback */
  background: hsl(var(--card));
  border-color: #e0e0e0;
  /* Fallback */
  border-color: hsl(var(--border) / 0.4);
}

@supports (-webkit-backdrop-filter: blur(8px)) {
  .medical-card-elevated {
    -webkit-backdrop-filter: blur(8px);
  }
}

@-moz-document url-prefix() {
  .medical-card-elevated {
    background-color: hsl(var(--card) / 0.95);
  }

  .dark .medical-card-elevated {
    background-color: hsl(var(--card) / 0.95);
  }
}

@supports (-ms-ime-align: auto) {
  .medical-card-elevated {
    background: hsl(var(--card));
    border: 1px solid hsl(var(--border));
  }

  .dark .medical-card-elevated {
    background: hsl(var(--card));
    border: 1px solid hsl(var(--border));
  }
}

.medical-card-elevated:hover{
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.medical-card-elevated {
  transform: translateY(0);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.medical-card-elevated:hover {
  transform: translateY(-2px);
}

.medical-card-interactive{
  border-radius: 0.75rem;
  border-width: 1px;
  border-color: hsl(var(--border) / 0.4);
  background-color: hsl(var(--card));
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  animation-duration: 300ms;
}

.medical-card-interactive:hover{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.medical-card-interactive {
  padding: 2.5rem;
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
}

.dark .medical-card-interactive {
  background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--card) / 0.98) 100%);
  border-color: hsl(var(--border) / 0.5);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.dark .medical-card-interactive:hover {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border-color: hsl(var(--border) / 0.7);
}

@media (prefers-contrast: high) {
  .medical-card-interactive {
    border-width: 2px;
    border-color: hsl(var(--border));
  }
}

@supports (-webkit-backdrop-filter: blur(10px)) {
  .medical-card-interactive {
    -webkit-backdrop-filter: blur(8px);
  }
}

@-moz-document url-prefix() {
  .medical-card-interactive {
    background-color: hsl(var(--card) / 0.95);
  }
}

@supports not ((-webkit-backdrop-filter: blur(8px)) or (backdrop-filter: blur(8px))) {
  .medical-card-interactive {
    background-color: hsl(var(--card) / 0.98);
  }
}

@media (min-resolution: 192dpi) {
  .medical-card-interactive {
    border-width: 0.5px;
  }
}

@media print {
  .medical-card-interactive {
    background: white;
    border: 1px solid #000;
    box-shadow: none;
    -webkit-backdrop-filter: none;
            backdrop-filter: none;
  }
}

.medical-card-interactive {
  contain: layout style paint;
  isolation: isolate;
}

@media (prefers-reduced-data: reduce) {
  .medical-card-interactive {
    -webkit-backdrop-filter: none;
            backdrop-filter: none;
    background-color: hsl(var(--card));
  }
}

.medical-card-interactive{
  cursor: pointer;
}

@media (prefers-contrast: high) {
  .medical-card-interactive {
    border-width: 2px;
  }
}

.medical-card-interactive {
  background-color: white;
  /* Fallback */
  background: hsl(var(--card));
  border-color: #e0e0e0;
  /* Fallback */
  border-color: hsl(var(--border) / 0.4);
}

@supports (-webkit-backdrop-filter: blur(8px)) {
  .medical-card-interactive {
    -webkit-backdrop-filter: blur(8px);
  }
}

@-moz-document url-prefix() {
  .medical-card-interactive {
    background-color: hsl(var(--card) / 0.95);
  }

  .dark .medical-card-interactive {
    background-color: hsl(var(--card) / 0.95);
  }
}

@supports (-ms-ime-align: auto) {
  .medical-card-interactive {
    background: hsl(var(--card));
    border: 1px solid hsl(var(--border));
  }

  .dark .medical-card-interactive {
    background: hsl(var(--card));
    border: 1px solid hsl(var(--border));
  }
}

.medical-card-interactive:hover{
  border-color: hsl(var(--primary) / 0.4);
}

.medical-card-interactive {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.medical-card-interactive:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.medical-card-feature{
  border-radius: 0.75rem;
  border-width: 1px;
  border-color: hsl(var(--border) / 0.4);
  background-color: hsl(var(--card));
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  animation-duration: 300ms;
}

.medical-card-feature:hover{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.medical-card-feature {
  padding: 2.5rem;
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
}

.dark .medical-card-feature {
  background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--card) / 0.98) 100%);
  border-color: hsl(var(--border) / 0.5);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.dark .medical-card-feature:hover {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border-color: hsl(var(--border) / 0.7);
}

@media (prefers-contrast: high) {
  .medical-card-feature {
    border-width: 2px;
    border-color: hsl(var(--border));
  }
}

@supports (-webkit-backdrop-filter: blur(10px)) {
  .medical-card-feature {
    -webkit-backdrop-filter: blur(8px);
  }
}

@-moz-document url-prefix() {
  .medical-card-feature {
    background-color: hsl(var(--card) / 0.95);
  }
}

@supports not ((-webkit-backdrop-filter: blur(8px)) or (backdrop-filter: blur(8px))) {
  .medical-card-feature {
    background-color: hsl(var(--card) / 0.98);
  }
}

@media (min-resolution: 192dpi) {
  .medical-card-feature {
    border-width: 0.5px;
  }
}

@media print {
  .medical-card-feature {
    background: white;
    border: 1px solid #000;
    box-shadow: none;
    -webkit-backdrop-filter: none;
            backdrop-filter: none;
  }
}

.medical-card-feature {
  contain: layout style paint;
  isolation: isolate;
}

@media (prefers-reduced-data: reduce) {
  .medical-card-feature {
    -webkit-backdrop-filter: none;
            backdrop-filter: none;
    background-color: hsl(var(--card));
  }
}

.medical-card-feature{
  border-color: hsl(var(--primary) / 0.15);
}

@media (prefers-contrast: high) {
  .medical-card-feature {
    border-width: 2px;
  }
}

.medical-card-feature {
  background-color: white;
  /* Fallback */
  background: hsl(var(--card));
  border-color: #e0e0e0;
  /* Fallback */
  border-color: hsl(var(--border) / 0.4);
}

@supports (-webkit-backdrop-filter: blur(8px)) {
  .medical-card-feature {
    -webkit-backdrop-filter: blur(8px);
  }
}

@-moz-document url-prefix() {
  .medical-card-feature {
    background-color: hsl(var(--card) / 0.95);
  }

  .dark .medical-card-feature {
    background-color: hsl(var(--card) / 0.95);
  }
}

@supports (-ms-ime-align: auto) {
  .medical-card-feature {
    background: hsl(var(--card));
    border: 1px solid hsl(var(--border));
  }

  .dark .medical-card-feature {
    background: hsl(var(--card));
    border: 1px solid hsl(var(--border));
  }
}

.medical-card-feature:hover{
  background-color: hsl(var(--primary) / 0.1);
}

.medical-card-feature {
  background-color: hsl(var(--primary) / 0.03);
  position: relative;
  overflow: hidden;
}

.medical-card-feature:hover {
  background-color: hsl(var(--primary) / 0.08);
}

.medical-card-feature::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, hsl(var(--primary)), hsl(var(--primary) / 0.7));
}

/* Professional Section Backgrounds - Enhanced */

.section-background{
  background-color: hsl(var(--background));
  border-bottom: 1px solid hsl(var(--border) / 0.1);
}

.section-background-muted{
  background-color: hsl(var(--muted) / 0.2);
  border-bottom: 1px solid hsl(var(--border) / 0.1);
}

.section-background-alt{
  background-color: hsl(var(--accent) / 0.2);
  border-bottom: 1px solid hsl(var(--border) / 0.1);
}

.section-background-primary{
  background-color: hsl(var(--primary) / 0.05);
  border-bottom: 1px solid hsl(var(--primary) / 0.1);
}

/* Enhanced Professional Section Spacing */

.section-spacing{
  padding-top: 4rem;
  padding-bottom: 4rem;
}

@media (min-width: 768px){
  .section-spacing{
    padding-top: 5rem;
    padding-bottom: 5rem;
  }
}

@media (min-width: 1024px){
  .section-spacing{
    padding-top: 6rem;
    padding-bottom: 6rem;
  }
}

/* Content spacing within sections */

.section-content-spacing > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}

@media (min-width: 768px){
  .section-content-spacing > :not([hidden]) ~ :not([hidden]){
    --tw-space-y-reverse: 0;
    margin-top: calc(3rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(3rem * var(--tw-space-y-reverse));
  }
}

@media (min-width: 1024px){
  .section-content-spacing > :not([hidden]) ~ :not([hidden]){
    --tw-space-y-reverse: 0;
    margin-top: calc(4rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(4rem * var(--tw-space-y-reverse));
  }
}

.section-header-spacing{
  margin-bottom: 3rem;
}

@media (min-width: 768px){
  .section-header-spacing{
    margin-bottom: 4rem;
  }
}

@media (min-width: 1024px){
  .section-header-spacing{
    margin-bottom: 5rem;
  }
}

/* Enhanced Medical Content Layout with Better Spacing */

.medical-content-layout > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}

@media (min-width: 768px){
  .medical-content-layout > :not([hidden]) ~ :not([hidden]){
    --tw-space-y-reverse: 0;
    margin-top: calc(3rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(3rem * var(--tw-space-y-reverse));
  }
}

@media (min-width: 1024px){
  .medical-content-layout > :not([hidden]) ~ :not([hidden]){
    --tw-space-y-reverse: 0;
    margin-top: calc(4rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(4rem * var(--tw-space-y-reverse));
  }
}

.medical-section-divider {
  margin-top: 3rem;
  margin-bottom: 3rem;
  border-top-width: 1px;
  border-color: hsl(var(--border) / 0.3);
}

@media (min-width: 768px){
  .medical-section-divider{
    margin-top: 4rem;
    margin-bottom: 4rem;
  }
}

@media (min-width: 1024px){
  .medical-section-divider{
    margin-top: 5rem;
    margin-bottom: 5rem;
  }
}

.medical-content-grid{
  display: grid;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  gap: 2rem;
}

@media (min-width: 768px){
  .medical-content-grid{
    gap: 3rem;
  }
}

@media (min-width: 1024px){
  .medical-content-grid{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

.medical-info-panel{
  border-radius: var(--radius);
  border-width: 1px;
  border-color: hsl(var(--primary) / 0.2);
  background-color: hsl(var(--primary) / 0.05);
  padding: 1.5rem;
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
}

.dark .medical-info-panel {
  background: linear-gradient(135deg, hsl(var(--primary) / 0.1) 0%, hsl(var(--primary) / 0.05) 100%);
  border-color: hsl(var(--primary) / 0.3);
  box-shadow: 0 4px 16px hsl(var(--primary) / 0.1);
}

/* Enhanced Dark Mode Medical Cards */

.dark .medical-card {
  background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--card) / 0.98) 100%);
  border-color: hsl(var(--border) / 0.5);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.dark .medical-card:hover {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border-color: hsl(var(--border) / 0.7);
}

/* Content Width Management */

.content-width-standard{
  margin-left: auto;
  margin-right: auto;
  max-width: 56rem;
}

/* Container Standardization */

.section-container{
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 1rem;
  padding-left: 1rem;
}

@media (min-width: 640px){
  .section-container{
    max-width: 640px;
    padding-right: 1.5rem;
    padding-left: 1.5rem;
  }
}

@media (min-width: 768px){
  .section-container{
    max-width: 768px;
    padding-right: 2rem;
    padding-left: 2rem;
  }
}

@media (min-width: 1024px){
  .section-container{
    max-width: 1024px;
    padding-right: 2.5rem;
    padding-left: 2.5rem;
  }
}

@media (min-width: 1280px){
  .section-container{
    max-width: 1280px;
    padding-right: 3rem;
    padding-left: 3rem;
  }
}

@media (min-width: 1400px){
  .section-container{
    max-width: 1400px;
    padding-right: 4rem;
    padding-left: 4rem;
  }
}

.section-container{
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 768px){
  .section-container{
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px){
  .section-container{
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

.section-container{
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 768px){
  .section-container{
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px){
  .section-container{
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

/* Enhanced Mobile-optimized components */

.mobile-container{
  margin-left: auto;
  margin-right: auto;
  max-width: 80rem;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px){
  .mobile-container{
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 768px){
  .mobile-container{
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1024px){
  .mobile-container{
    padding-left: 3rem;
    padding-right: 3rem;
  }
}

.mobile-section{
  padding-top: 3rem;
  padding-bottom: 3rem;
}

@media (min-width: 640px){
  .mobile-section{
    padding-top: 4rem;
    padding-bottom: 4rem;
  }
}

@media (min-width: 768px){
  .mobile-section{
    padding-top: 5rem;
    padding-bottom: 5rem;
  }
}

@media (min-width: 1024px){
  .mobile-section{
    padding-top: 6rem;
    padding-bottom: 6rem;
  }
}

.mobile-section-sm{
  padding-top: 2rem;
  padding-bottom: 2rem;
}

@media (min-width: 640px){
  .mobile-section-sm{
    padding-top: 3rem;
    padding-bottom: 3rem;
  }
}

@media (min-width: 768px){
  .mobile-section-sm{
    padding-top: 4rem;
    padding-bottom: 4rem;
  }
}

@media (min-width: 1024px){
  .mobile-section-sm{
    padding-top: 5rem;
    padding-bottom: 5rem;
  }
}

.mobile-section-lg{
  padding-top: 4rem;
  padding-bottom: 4rem;
}

@media (min-width: 640px){
  .mobile-section-lg{
    padding-top: 5rem;
    padding-bottom: 5rem;
  }
}

@media (min-width: 768px){
  .mobile-section-lg{
    padding-top: 6rem;
    padding-bottom: 6rem;
  }
}

@media (min-width: 1024px){
  .mobile-section-lg{
    padding-top: 8rem;
    padding-bottom: 8rem;
  }
}

/* Professional Badge Styles with Enhanced Contrast */

.badge-emergency {
  background-color: hsl(var(--muted));
  border-width: 1px;
  border-color: hsl(var(--border) / 0.7);
  font-weight: 600;
  color: hsl(var(--foreground));
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.badge-info {
  border-width: 1px;
  border-color: hsl(var(--info) / 0.3);
  background-color: hsl(var(--info-light));
  font-weight: 600;
  color: hsl(var(--info));
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.badge-routine {
  border-width: 1px;
  border-color: hsl(var(--success) / 0.3);
  background-color: hsl(var(--success-light));
  font-weight: 600;
  color: hsl(var(--success));
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.badge-medical {
  border-width: 1px;
  border-color: hsl(var(--medical-blue) / 0.3);
  background-color: hsl(var(--medical-blue-light));
  font-weight: 600;
  color: hsl(var(--medical-blue));
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

/* Color Utility Classes */

.bg-info-light {
  background-color: hsl(var(--info-light));
}

.bg-success-light {
  background-color: hsl(var(--success-light));
}

.bg-muted-light {
  background-color: hsl(var(--muted));
}

.bg-medical-blue-light {
  background-color: hsl(var(--medical-blue-light));
}

.text-info {
  color: hsl(var(--info));
}

.text-success {
  color: hsl(var(--success));
}

.text-foreground {
  color: hsl(var(--foreground));
}

.text-medical-blue {
  color: hsl(var(--medical-blue));
}

.text-medical-blue-foreground {
  color: hsl(var(--medical-blue-foreground));
}

.border-info\/30 {
  border-color: hsl(var(--info) / 0.3);
}

.border-success\/30 {
  border-color: hsl(var(--success) / 0.3);
}

.border-border\/30 {
  border-color: hsl(var(--border) / 0.3);
}

.border-medical-blue\/30 {
  border-color: hsl(var(--medical-blue) / 0.3);
}

/* Additional Theme Color Classes */

.bg-info {
  background-color: hsl(var(--info));
}

.bg-success {
  background-color: hsl(var(--success));
}

.bg-muted {
  background-color: hsl(var(--muted));
}

.bg-medical-blue {
  background-color: hsl(var(--medical-blue));
}

.border-info {
  border-color: hsl(var(--info));
}

.border-success {
  border-color: hsl(var(--success));
}

.border-border {
  border-color: hsl(var(--border));
}

.border-medical-blue {
  border-color: hsl(var(--medical-blue));
}

/* Emergency styling */

.border-emergency\/30{
  border-color: hsl(var(--border) / 0.7);
}

.bg-emergency-light {
  background-color: hsl(var(--muted));
}

.text-emergency {
  color: hsl(var(--foreground));
}

/* Enhanced Text Readability with Clear Visual Hierarchy */

.text-enhanced {
  font-weight: 500;
  line-height: 1.625;
  color: hsl(var(--foreground));
}

.text-enhanced-caption{
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 600;
  text-transform: uppercase;
  line-height: 1.5;
  letter-spacing: 0.05em;
  color: hsl(var(--primary));
}

/* Professional Text Alignment Classes */

/* Consistent Icon Alignment */

.icon-text-aligned{
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Professional Spacing Patterns */

.content-spacing-sm > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

@media (min-width: 768px){
  .content-spacing-sm > :not([hidden]) ~ :not([hidden]){
    --tw-space-y-reverse: 0;
    margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
  }
}

.content-spacing-md > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}

@media (min-width: 768px){
  .content-spacing-md > :not([hidden]) ~ :not([hidden]){
    --tw-space-y-reverse: 0;
    margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(2rem * var(--tw-space-y-reverse));
  }
}

/* Additional Enhanced Text Classes */

@media print {
  .link-enhanced-subtle {
    color: #666;
  }
}

.link-enhanced-subtle{
  color: hsl(var(--muted-foreground));
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
  animation-duration: 200ms;
}

.link-enhanced-subtle:hover {
  color: hsl(var(--foreground));
}

/* Professional Card Enhancements */

/* Technology Section Specific Styling */

/* Professional Animation Classes */

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.4s ease-out forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Professional Button Enhancements */

/* Button enhancements are handled by buttonVariants */

/* Professional Section Styling */

/* Enhanced Visual Hierarchy */

/* Enhanced Grid Layouts */

/* Professional Spacing Utilities */

/* Enhanced Color Utilities */

.enhanced-hover:hover{
  background-color: hsl(var(--accent) / 0.5);
}

.bg-enhanced-accent{
  background-color: hsl(var(--primary) / 0.1);
}

/* Visual Hierarchy Spacing */

.section-spacing{
  padding-top: 4rem;
  padding-bottom: 4rem;
}

@media (min-width: 768px){
  .section-spacing{
    padding-top: 5rem;
    padding-bottom: 5rem;
  }
}

@media (min-width: 1024px){
  .section-spacing{
    padding-top: 6rem;
    padding-bottom: 6rem;
  }
}

.content-spacing > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}

@media (min-width: 768px){
  .content-spacing > :not([hidden]) ~ :not([hidden]){
    --tw-space-y-reverse: 0;
    margin-top: calc(3rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(3rem * var(--tw-space-y-reverse));
  }
}

@media (min-width: 1024px){
  .content-spacing > :not([hidden]) ~ :not([hidden]){
    --tw-space-y-reverse: 0;
    margin-top: calc(4rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(4rem * var(--tw-space-y-reverse));
  }
}

/* Enhanced Text Content Spacing */

.text-content-spacing > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}

@media (min-width: 768px){
  .text-content-spacing > :not([hidden]) ~ :not([hidden]){
    --tw-space-y-reverse: 0;
    margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(2rem * var(--tw-space-y-reverse));
  }
}

.text-content-spacing p{
  margin-bottom: 1.5rem;
  line-height: 1.625;
}

.text-content-spacing h2{
  margin-top: 3rem;
  margin-bottom: 1.5rem;
}

.text-content-spacing h3{
  margin-top: 2rem;
  margin-bottom: 1rem;
}

/* Enhanced Card Hierarchy */

/* Enhanced Medical Card System */

.medical-content-card{
  border-radius: 0.75rem;
  border-width: 1px;
  border-color: hsl(var(--border) / 0.4);
  background-color: hsl(var(--card));
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  animation-duration: 300ms;
}

.medical-content-card:hover{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.medical-content-card {
  padding: 2.5rem;
  margin-bottom: 2rem;
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
}

.medical-content-card-sm{
  border-radius: var(--radius);
  border-width: 1px;
  border-color: hsl(var(--border) / 0.4);
  background-color: hsl(var(--card));
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  animation-duration: 300ms;
}

.medical-content-card-sm:hover{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.medical-content-card-sm {
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.medical-content-card h1{
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 700;
  line-height: 1.25;
}

@media (min-width: 768px){
  .medical-content-card h1{
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
}

.medical-content-card h1 {
  color: hsl(var(--foreground));
  border-bottom: 2px solid hsl(var(--primary) / 0.3);
  padding-bottom: 0.5rem;
  font-display: swap;
  margin-bottom: 2rem;
}

.medical-content-card h2{
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 600;
  line-height: 1.375;
}

@media (min-width: 768px){
  .medical-content-card h2{
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

.medical-content-card h2 {
  color: hsl(var(--foreground));
  margin-top: 2rem;
  font-display: swap;
  margin-bottom: 1.5rem;
}

.medical-content-card h3 {
  margin-bottom: 1rem;
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 600;
  color: hsl(var(--foreground));
}

@media (min-width: 768px){
  .medical-content-card h3{
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

.medical-content-card h4 {
  margin-bottom: 0.75rem;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 500;
  color: hsl(var(--foreground));
}

@media (min-width: 768px){
  .medical-content-card h4{
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

.medical-content-card p{
  font-size: 1rem;
  line-height: 1.5rem;
  color: hsl(var(--foreground) / 0.9);
  margin-bottom: 1.25rem;
  line-height: 1.7;
  font-display: swap;
}

.medical-content-card ul,
  .medical-content-card ol{
  margin-bottom: 1.5rem;
}

.medical-content-card ul > :not([hidden]) ~ :not([hidden]),
  .medical-content-card ol > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.medical-content-card ul,
  .medical-content-card ol{
  padding-left: 1.5rem;
}

@media print {
  .medical-content-card li {
    color: #666;
  }
}

.medical-content-card li{
  line-height: 1.625;
  color: hsl(var(--muted-foreground));
}

/* All

  .medical-subheading {
    @apply text-foreground font-semibold text-xl md:text-2xl lg:text-3xl leading-tight;
    letter-spacing: -0.015em;
    margin-bottom: 1rem;
  }

  .medical-body {
    @apply text-muted-foreground text-base md:text-lg leading-relaxed;
    line-height: 1.7;
    margin-bottom: 1.5rem;
  }

  .medical-body-sm {
    @apply text-muted-foreground text-sm md:text-base leading-relaxed;
    line-height: 1.6;
    margin-bottom: 1rem;
  }

  .medical-caption {
    @apply text-muted-foreground text-sm italic leading-relaxed;
    margin-top: 0.5rem;
  }

  .medical-label {
    @apply text-foreground font-medium text-sm uppercase tracking-wide;
    letter-spacing: 0.05em;
  }

  .medical-lead {
    @apply text-foreground text-lg md:text-xl leading-relaxed font-medium;
    line-height: 1.6;
    margin-bottom: 2rem;
  }

  /* Enhanced Text Utilities */

.text-enhanced-heading {
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 700;
  line-height: 1.25;
  color: hsl(var(--foreground));
}

@media (min-width: 768px){
  .text-enhanced-heading{
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
}

@media (min-width: 1024px){
  .text-enhanced-heading{
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
}

.text-enhanced-heading {
  letter-spacing: -0.025em;
}

.text-enhanced-subheading {
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 600;
  line-height: 1.25;
  color: hsl(var(--foreground));
}

@media (min-width: 768px){
  .text-enhanced-subheading{
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1024px){
  .text-enhanced-subheading{
    font-size: 1.5rem;
    line-height: 2rem;
  }
}

.text-enhanced-subheading {
  letter-spacing: -0.015em;
}

@media print {
  .text-enhanced-body {
    color: #666;
  }
}

.text-enhanced-body{
  font-size: 1rem;
  line-height: 1.5rem;
  line-height: 1.625;
  color: hsl(var(--muted-foreground));
}

@media (min-width: 768px){
  .text-enhanced-body{
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

.text-enhanced-body {
  line-height: 1.7;
}

@media print {
  .text-enhanced-muted {
    color: #666;
  }
}

.text-enhanced-muted{
  font-size: 0.875rem;
  line-height: 1.25rem;
  line-height: 1.625;
  color: hsl(var(--muted-foreground));
}

/* Professional List Styling */

.medical-list > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.medical-list{
  padding-left: 1.5rem;
}

@media print {
  .medical-list li {
    color: #666;
  }
}

.medical-list li{
  position: relative;
  line-height: 1.625;
  color: hsl(var(--muted-foreground));
}

.medical-list li::before {
  content: "•";
  position: absolute;
  left: -1rem;
  font-weight: 700;
  color: hsl(var(--primary));
}

/* Enhanced Card System Variants */

.card-feature{
  border-radius: 0.75rem;
  border-width: 1px;
  border-color: hsl(var(--border) / 0.4);
  background-color: hsl(var(--card));
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  animation-duration: 300ms;
}

@media (hover: none) and (pointer: coarse) {
  .card-feature:hover {
    box-shadow: none;
  }
}

.card-feature:hover{
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.card-feature {
  padding: 2rem;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}

.card-service{
  border-radius: 0.75rem;
  border-width: 1px;
  border-color: hsl(var(--border) / 0.4);
  background-color: hsl(var(--card));
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  animation-duration: 300ms;
}

@media (hover: none) and (pointer: coarse) {
  .card-service:hover {
    box-shadow: none;
  }
}

.card-service:hover{
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.card-service {
  padding: 2.5rem;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}

.card-service:hover {
  transform: translateY(-3px);
  border-color: hsl(var(--primary) / 0.4);
}

.card-highlight{
  border-radius: 0.75rem;
  border-width: 1px;
  border-color: hsl(var(--primary) / 0.2);
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
  --tw-gradient-from: hsl(var(--primary) / 0.05) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: hsl(var(--primary) / 0.1) var(--tw-gradient-to-position);
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  animation-duration: 300ms;
}

@media (hover: none) and (pointer: coarse) {
  .card-highlight:hover {
    box-shadow: none;
  }
}

.card-highlight:hover{
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.card-highlight {
  padding: 2rem;
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
}

.card-subtle{
  border-radius: var(--radius);
  border-width: 1px;
  border-color: hsl(var(--border) / 0.2);
  background-color: hsl(var(--muted) / 0.3);
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  animation-duration: 300ms;
}

.card-subtle:hover{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.card-subtle {
  padding: 1.5rem;
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
}

/* Comprehensive Responsive Design System */

/* Mobile-First Responsive Containers */

/* Responsive Typography */

/* Responsive Spacing */

/* Responsive Grid Systems */

/* Mobile Touch Targets */

.touch-target{
  min-height: 44px;
  min-width: 44px;
  touch-action: manipulation;
}

/* Responsive Buttons */

/* Responsive Images */

/* WCAG AA Accessibility Compliance System */

/* Focus States - Enhanced for Accessibility */

/* High Contrast Mode Support */

@media (prefers-contrast: high) {
  .medical-card {
    border-width: 2px;
    border-color: hsl(var(--border));
  }
}

/* Reduced Motion Support */

@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .medical-card-interactive:hover {
    transform: none;
  }
}

/* Screen Reader Only Content */

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Skip Links */

.skip-link{
  position: absolute;
  top: -10rem;
  left: 1.5rem;
  z-index: 50;
  border-radius: calc(var(--radius) - 2px);
  background-color: hsl(var(--primary));
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-weight: 500;
  color: hsl(var(--primary-foreground));
  transition: top 0.3s;
}

.skip-link:focus{
  top: 1.5rem;
}

/* Enhanced Color Contrast */

/* Accessible Form Elements */

/* Accessible Interactive States */

/* ARIA Live Regions */

/* Accessible Color Combinations */

/* Cross-Browser Compatibility Enhancements */

/* Webkit/Safari Specific */

@supports (-webkit-backdrop-filter: blur(10px)) {
  .medical-card,
    .medical-content-card,
    .card-elevated,
    .card-interactive {
    -webkit-backdrop-filter: blur(8px);
  }
}

/* Firefox Specific */

@-moz-document url-prefix() {
  .medical-card,
    .medical-content-card {
    background-color: hsl(var(--card) / 0.95);
  }
}

/* Edge/IE Fallbacks */

@supports not ((-webkit-backdrop-filter: blur(8px)) or (backdrop-filter: blur(8px))) {
  .medical-card,
    .medical-content-card,
    .card-elevated,
    .card-interactive {
    background-color: hsl(var(--card) / 0.98);
  }
}

/* Touch Device Optimizations */

@media (hover: none) and (pointer: coarse) {
  .medical-card-interactive:hover {
    transform: none;
  }

  .interactive-accessible:hover {
    background-color: transparent;
  }
}

/* High DPI Display Support */

@media (min-resolution: 192dpi) {
  .medical-card,
    .medical-content-card {
    border-width: 0.5px;
  }
}

/* Print Styles */

@media print {
  .medical-card,
    .medical-content-card {
    background: white !important;
    border: 1px solid #000 !important;
    box-shadow: none !important;
    -webkit-backdrop-filter: none !important;
            backdrop-filter: none !important;
  }

  .text-muted-foreground {
    color: #666 !important;
  }
}

/* Browser-Specific Button Resets */

/* Input Field Consistency */

input,
  textarea,
  select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-clip: padding-box;
}

/* Scrollbar Styling for Webkit */

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted) / 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.7);
}

/* Firefox Scrollbar */

* {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--muted-foreground) / 0.5) hsl(var(--muted) / 0.3);
}

/* Performance Optimizations */

/* GPU Acceleration for Smooth Animations */

.medical-card-interactive,

  /* Optimize Backdrop Filters */
  .medical-card,
  .medical-content-card,
  .card-elevated {
  contain: layout style paint;
}

/* Efficient Transitions */

/* Reduce Paint Operations */

.medical-card,
  .medical-content-card {
  isolation: isolate;
}

/* Optimize Font Loading */

.medical-heading,
  .medical-subheading,
  .medical-body {
  font-display: swap;
}

/* Critical CSS Optimizations */

/* Lazy Loading Optimizations */

/* Reduce Reflow/Repaint */

/* Memory Efficient Shadows */

/* Efficient Grid Layouts */

/* Optimized Flexbox */

/* Reduce Layout Thrashing */

/* Efficient Color Calculations */

/* Performance Monitoring */

@media (prefers-reduced-data: reduce) {
  .medical-card,
    .medical-content-card {
    -webkit-backdrop-filter: none;
            backdrop-filter: none;
    background-color: hsl(var(--card));
  }

  .shadow-optimized,
    .shadow-optimized-md,
    .shadow-optimized-lg {
    box-shadow: none;
    border: 1px solid hsl(var(--border));
  }
}

/* Enhanced Interactive Elements */

.medical-card-interactive{
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  animation-duration: 300ms;
}

@media (hover: none) and (pointer: coarse) {
  .medical-card-interactive:hover {
    box-shadow: none;
  }
}

.medical-card-interactive:hover{
  border-color: hsl(var(--primary) / 0.3);
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transform: translateY(-2px);
}

/* Comprehensive Spacing System */

.spacing-lg > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}

@media (min-width: 768px){
  .spacing-lg > :not([hidden]) ~ :not([hidden]){
    --tw-space-y-reverse: 0;
    margin-top: calc(3rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(3rem * var(--tw-space-y-reverse));
  }
}

@media (min-width: 1024px){
  .spacing-lg > :not([hidden]) ~ :not([hidden]){
    --tw-space-y-reverse: 0;
    margin-top: calc(4rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(4rem * var(--tw-space-y-reverse));
  }
}

/* Section Spacing */

.section-padding-sm{
  padding-top: 2rem;
  padding-bottom: 2rem;
}

@media (min-width: 768px){
  .section-padding-sm{
    padding-top: 3rem;
    padding-bottom: 3rem;
  }
}

.section-padding-lg{
  padding-top: 4rem;
  padding-bottom: 4rem;
}

@media (min-width: 768px){
  .section-padding-lg{
    padding-top: 5rem;
    padding-bottom: 5rem;
  }
}

@media (min-width: 1024px){
  .section-padding-lg{
    padding-top: 6rem;
    padding-bottom: 6rem;
  }
}

/* Container Spacing */

.container-spacing{
  margin-left: auto;
  margin-right: auto;
  max-width: 80rem;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 768px){
  .container-spacing{
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px){
  .container-spacing{
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

/* Content Width Variants */

.content-width-standard{
  margin-left: auto;
  margin-right: auto;
  max-width: 56rem;
}

/* Grid Spacing Variants */

/* Margin Utilities */

/* Padding Utilities */

/* Professional Layout Patterns */

/* Enhanced Button Spacing and Visibility */

/* Enhanced Card Styling */

/* Medical Content Cards - Enhanced for Dark Theme */

.medical-content-card{
  border-radius: 0.75rem;
  border-width: 1px;
  border-color: hsl(var(--border) / 0.4);
  background-color: hsl(var(--card));
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  -webkit-backdrop-filter: blur(12px);
          backdrop-filter: blur(12px);
  position: relative;
}

.dark .medical-content-card {
  background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--card) / 0.95) 100%);
  border-color: hsl(var(--border) / 0.6);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Enhanced Dark Mode Text Readability */

.dark .text-enhanced-body {
  color: hsl(var(--foreground) / 0.92);
}

.dark .text-enhanced-muted {
  color: hsl(var(--muted-foreground) / 0.85);
}

.dark .text-enhanced-heading {
  color: hsl(var(--foreground));
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.medical-image-container {
  overflow: hidden;
  border-radius: var(--radius);
  border-width: 1px;
  border-color: hsl(var(--border) / 0.3);
  background: hsl(var(--background));
}

.dark .medical-image-container {
  background: linear-gradient(135deg, hsl(var(--muted)) 0%, hsl(var(--card)) 100%);
  border-color: hsl(var(--border) / 0.5);
}

.link-enhanced-subtle{
  color: hsl(var(--foreground) / 0.8);
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
  animation-duration: 200ms;
}

.link-enhanced-subtle:hover{
  color: hsl(var(--primary));
}

/* Professional Button Styling - Enhanced for Medical Standards */

/* Enhanced Badge Styling */

.badge-secondary{
  display: inline-flex;
  align-items: center;
  border-radius: 9999px;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  border-width: 1px;
  border-color: hsl(var(--secondary) / 0.2);
  background-color: hsl(var(--secondary) / 0.1);
  color: hsl(var(--secondary-foreground));
}

.badge-warning{
  display: inline-flex;
  align-items: center;
  border-radius: 9999px;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  border-width: 1px;
  border-color: hsl(var(--warning) / 0.3);
  background-color: hsl(var(--warning-light));
  color: hsl(var(--warning));
}

.sr-only{
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.pointer-events-none{
  pointer-events: none;
}

.pointer-events-auto{
  pointer-events: auto;
}

.visible{
  visibility: visible;
}

.invisible{
  visibility: hidden;
}

.collapse{
  visibility: collapse;
}

.static{
  position: static;
}

.fixed{
  position: fixed;
}

.absolute{
  position: absolute;
}

.relative{
  position: relative;
}

.sticky{
  position: sticky;
}

.-inset-2{
  inset: -0.5rem;
}

.inset-0{
  inset: 0px;
}

.inset-x-0{
  left: 0px;
  right: 0px;
}

.inset-y-0{
  top: 0px;
  bottom: 0px;
}

.-bottom-12{
  bottom: -3rem;
}

.-bottom-4{
  bottom: -1rem;
}

.-left-12{
  left: -3rem;
}

.-left-4{
  left: -1rem;
}

.-right-12{
  right: -3rem;
}

.-right-4{
  right: -1rem;
}

.-top-12{
  top: -3rem;
}

.-top-4{
  top: -1rem;
}

.bottom-0{
  bottom: 0px;
}

.bottom-10{
  bottom: 2.5rem;
}

.bottom-4{
  bottom: 1rem;
}

.bottom-mobile-lg{
  bottom: 1rem;
}

.left-0{
  left: 0px;
}

.left-1{
  left: 0.25rem;
}

.left-1\/2{
  left: 50%;
}

.left-2{
  left: 0.5rem;
}

.left-3{
  left: 0.75rem;
}

.left-4{
  left: 1rem;
}

.left-6{
  left: 1.5rem;
}

.left-\[50\%\]{
  left: 50%;
}

.right-0{
  right: 0px;
}

.right-1{
  right: 0.25rem;
}

.right-10{
  right: 2.5rem;
}

.right-2{
  right: 0.5rem;
}

.right-4{
  right: 1rem;
}

.right-40{
  right: 10rem;
}

.top-0{
  top: 0px;
}

.top-1\/2{
  top: 50%;
}

.top-10{
  top: 2.5rem;
}

.top-12{
  top: 3rem;
}

.top-2{
  top: 0.5rem;
}

.top-24{
  top: 6rem;
}

.top-4{
  top: 1rem;
}

.top-8{
  top: 2rem;
}

.top-\[1px\]{
  top: 1px;
}

.top-\[50\%\]{
  top: 50%;
}

.top-\[60\%\]{
  top: 60%;
}

.top-full{
  top: 100%;
}

.isolate{
  isolation: isolate;
}

.z-0{
  z-index: 0;
}

.z-10{
  z-index: 10;
}

.z-40{
  z-index: 40;
}

.z-50{
  z-index: 50;
}

.z-\[1\]{
  z-index: 1;
}

.order-1{
  order: 1;
}

.order-2{
  order: 2;
}

.order-first{
  order: -9999;
}

.float-right{
  float: right;
}

.m-0{
  margin: 0px;
}

.-mx-1{
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}

.mx-2{
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.mx-3{
  margin-left: 0.75rem;
  margin-right: 0.75rem;
}

.mx-4{
  margin-left: 1rem;
  margin-right: 1rem;
}

.mx-auto{
  margin-left: auto;
  margin-right: auto;
}

.my-1{
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}

.my-6{
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

.my-8{
  margin-top: 2rem;
  margin-bottom: 2rem;
}

.-ml-4{
  margin-left: -1rem;
}

.-mt-4{
  margin-top: -1rem;
}

.mb-0{
  margin-bottom: 0px;
}

.mb-1{
  margin-bottom: 0.25rem;
}

.mb-10{
  margin-bottom: 2.5rem;
}

.mb-12{
  margin-bottom: 3rem;
}

.mb-16{
  margin-bottom: 4rem;
}

.mb-2{
  margin-bottom: 0.5rem;
}

.mb-20{
  margin-bottom: 5rem;
}

.mb-3{
  margin-bottom: 0.75rem;
}

.mb-4{
  margin-bottom: 1rem;
}

.mb-5{
  margin-bottom: 1.25rem;
}

.mb-6{
  margin-bottom: 1.5rem;
}

.mb-8{
  margin-bottom: 2rem;
}

.mb-mobile-lg{
  margin-bottom: 1rem;
}

.mb-mobile-md{
  margin-bottom: 0.75rem;
}

.mb-mobile-sm{
  margin-bottom: 0.5rem;
}

.mb-mobile-xl{
  margin-bottom: 1.5rem;
}

.mb-mobile-xs{
  margin-bottom: 0.25rem;
}

.ml-1{
  margin-left: 0.25rem;
}

.ml-11{
  margin-left: 2.75rem;
}

.ml-14{
  margin-left: 3.5rem;
}

.ml-2{
  margin-left: 0.5rem;
}

.ml-3{
  margin-left: 0.75rem;
}

.ml-4{
  margin-left: 1rem;
}

.ml-auto{
  margin-left: auto;
}

.mr-1{
  margin-right: 0.25rem;
}

.mr-2{
  margin-right: 0.5rem;
}

.mr-3{
  margin-right: 0.75rem;
}

.mr-4{
  margin-right: 1rem;
}

.mr-mobile-md{
  margin-right: 0.75rem;
}

.mr-mobile-sm{
  margin-right: 0.5rem;
}

.mt-0\.5{
  margin-top: 0.125rem;
}

.mt-1{
  margin-top: 0.25rem;
}

.mt-1\.5{
  margin-top: 0.375rem;
}

.mt-10{
  margin-top: 2.5rem;
}

.mt-12{
  margin-top: 3rem;
}

.mt-16{
  margin-top: 4rem;
}

.mt-2{
  margin-top: 0.5rem;
}

.mt-24{
  margin-top: 6rem;
}

.mt-3{
  margin-top: 0.75rem;
}

.mt-4{
  margin-top: 1rem;
}

.mt-6{
  margin-top: 1.5rem;
}

.mt-8{
  margin-top: 2rem;
}

.mt-auto{
  margin-top: auto;
}

.mt-mobile-lg{
  margin-top: 1rem;
}

.mt-mobile-md{
  margin-top: 0.75rem;
}

.line-clamp-2{
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.block{
  display: block;
}

.inline-block{
  display: inline-block;
}

.inline{
  display: inline;
}

.flex{
  display: flex;
}

.inline-flex{
  display: inline-flex;
}

.table{
  display: table;
}

.grid{
  display: grid;
}

.contents{
  display: contents;
}

.hidden{
  display: none;
}

.aspect-\[4\/3\]{
  aspect-ratio: 4/3;
}

.aspect-square{
  aspect-ratio: 1 / 1;
}

.aspect-video{
  aspect-ratio: 16 / 9;
}

.h-1{
  height: 0.25rem;
}

.h-1\.5{
  height: 0.375rem;
}

.h-10{
  height: 2.5rem;
}

.h-11{
  height: 2.75rem;
}

.h-12{
  height: 3rem;
}

.h-14{
  height: 3.5rem;
}

.h-16{
  height: 4rem;
}

.h-2{
  height: 0.5rem;
}

.h-2\.5{
  height: 0.625rem;
}

.h-20{
  height: 5rem;
}

.h-24{
  height: 6rem;
}

.h-3{
  height: 0.75rem;
}

.h-3\.5{
  height: 0.875rem;
}

.h-32{
  height: 8rem;
}

.h-4{
  height: 1rem;
}

.h-40{
  height: 10rem;
}

.h-48{
  height: 12rem;
}

.h-5{
  height: 1.25rem;
}

.h-52{
  height: 13rem;
}

.h-6{
  height: 1.5rem;
}

.h-64{
  height: 16rem;
}

.h-7{
  height: 1.75rem;
}

.h-8{
  height: 2rem;
}

.h-80{
  height: 20rem;
}

.h-9{
  height: 2.25rem;
}

.h-96{
  height: 24rem;
}

.h-\[1px\]{
  height: 1px;
}

.h-\[350px\]{
  height: 350px;
}

.h-\[400px\]{
  height: 400px;
}

.h-\[var\(--radix-navigation-menu-viewport-height\)\]{
  height: var(--radix-navigation-menu-viewport-height);
}

.h-\[var\(--radix-select-trigger-height\)\]{
  height: var(--radix-select-trigger-height);
}

.h-auto{
  height: auto;
}

.h-full{
  height: 100%;
}

.h-px{
  height: 1px;
}

.h-screen{
  height: 100vh;
}

.max-h-48{
  max-height: 12rem;
}

.max-h-96{
  max-height: 24rem;
}

.max-h-\[250px\]{
  max-height: 250px;
}

.max-h-\[300px\]{
  max-height: 300px;
}

.max-h-\[400px\]{
  max-height: 400px;
}

.max-h-\[80vh\]{
  max-height: 80vh;
}

.max-h-\[90vh\]{
  max-height: 90vh;
}

.max-h-\[calc\(100vh-2rem\)\]{
  max-height: calc(100vh - 2rem);
}

.min-h-\[100px\]{
  min-height: 100px;
}

.min-h-\[120px\]{
  min-height: 120px;
}

.min-h-\[44px\]{
  min-height: 44px;
}

.min-h-\[48px\]{
  min-height: 48px;
}

.min-h-\[52px\]{
  min-height: 52px;
}

.min-h-\[56px\]{
  min-height: 56px;
}

.min-h-\[600px\]{
  min-height: 600px;
}

.min-h-\[80px\]{
  min-height: 80px;
}

.min-h-screen{
  min-height: 100vh;
}

.w-0\.5{
  width: 0.125rem;
}

.w-1{
  width: 0.25rem;
}

.w-1\.5{
  width: 0.375rem;
}

.w-1\/2{
  width: 50%;
}

.w-1\/3{
  width: 33.333333%;
}

.w-10{
  width: 2.5rem;
}

.w-11{
  width: 2.75rem;
}

.w-12{
  width: 3rem;
}

.w-14{
  width: 3.5rem;
}

.w-16{
  width: 4rem;
}

.w-2{
  width: 0.5rem;
}

.w-2\.5{
  width: 0.625rem;
}

.w-20{
  width: 5rem;
}

.w-24{
  width: 6rem;
}

.w-3{
  width: 0.75rem;
}

.w-3\.5{
  width: 0.875rem;
}

.w-3\/4{
  width: 75%;
}

.w-32{
  width: 8rem;
}

.w-4{
  width: 1rem;
}

.w-48{
  width: 12rem;
}

.w-5{
  width: 1.25rem;
}

.w-6{
  width: 1.5rem;
}

.w-64{
  width: 16rem;
}

.w-7{
  width: 1.75rem;
}

.w-72{
  width: 18rem;
}

.w-8{
  width: 2rem;
}

.w-9{
  width: 2.25rem;
}

.w-\[100px\]{
  width: 100px;
}

.w-\[120px\]{
  width: 120px;
}

.w-\[140px\]{
  width: 140px;
}

.w-\[160px\]{
  width: 160px;
}

.w-\[1px\]{
  width: 1px;
}

.w-\[calc\(100vw-2rem\)\]{
  width: calc(100vw - 2rem);
}

.w-auto{
  width: auto;
}

.w-fit{
  width: -moz-fit-content;
  width: fit-content;
}

.w-full{
  width: 100%;
}

.w-max{
  width: -moz-max-content;
  width: max-content;
}

.w-px{
  width: 1px;
}

.min-w-0{
  min-width: 0px;
}

.min-w-\[120px\]{
  min-width: 120px;
}

.min-w-\[12rem\]{
  min-width: 12rem;
}

.min-w-\[200px\]{
  min-width: 200px;
}

.min-w-\[220px\]{
  min-width: 220px;
}

.min-w-\[8rem\]{
  min-width: 8rem;
}

.min-w-\[var\(--radix-select-trigger-width\)\]{
  min-width: var(--radix-select-trigger-width);
}

.min-w-fit{
  min-width: -moz-fit-content;
  min-width: fit-content;
}

.max-w-2xl{
  max-width: 42rem;
}

.max-w-3xl{
  max-width: 48rem;
}

.max-w-4xl{
  max-width: 56rem;
}

.max-w-5xl{
  max-width: 64rem;
}

.max-w-6xl{
  max-width: 72rem;
}

.max-w-7xl{
  max-width: 80rem;
}

.max-w-\[calc\(100vw-2rem\)\]{
  max-width: calc(100vw - 2rem);
}

.max-w-full{
  max-width: 100%;
}

.max-w-lg{
  max-width: 32rem;
}

.max-w-max{
  max-width: -moz-max-content;
  max-width: max-content;
}

.max-w-md{
  max-width: 28rem;
}

.max-w-none{
  max-width: none;
}

.max-w-prose{
  max-width: 65ch;
}

.max-w-sm{
  max-width: 24rem;
}

.max-w-xl{
  max-width: 36rem;
}

.flex-1{
  flex: 1 1 0%;
}

.flex-shrink-0{
  flex-shrink: 0;
}

.shrink-0{
  flex-shrink: 0;
}

.grow{
  flex-grow: 1;
}

.grow-0{
  flex-grow: 0;
}

.basis-full{
  flex-basis: 100%;
}

.caption-bottom{
  caption-side: bottom;
}

.border-collapse{
  border-collapse: collapse;
}

.-translate-x-1\/2{
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-full{
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1\/2{
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-2{
  --tw-translate-y: -0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-0{
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-\[-50\%\]{
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-full{
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-0{
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-\[-50\%\]{
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-0{
  --tw-rotate: 0deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-45{
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-90{
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-100{
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-105{
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-110{
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-125{
  --tw-scale-x: 1.25;
  --tw-scale-y: 1.25;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-\[1\.02\]{
  --tw-scale-x: 1.02;
  --tw-scale-y: 1.02;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform{
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform-gpu{
  transform: translate3d(var(--tw-translate-x), var(--tw-translate-y), 0) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes bounce{
  0%, 100%{
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8,0,1,1);
  }

  50%{
    transform: none;
    animation-timing-function: cubic-bezier(0,0,0.2,1);
  }
}

.animate-bounce{
  animation: bounce 1s infinite;
}

@keyframes fade-in{
  0%{
    opacity: 0;
    transform: translateY(10px);
  }

  100%{
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in{
  animation: fade-in 0.6s ease-out;
}

@keyframes pulse{
  50%{
    opacity: .5;
  }
}

.animate-pulse{
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes spin{
  to{
    transform: rotate(360deg);
  }
}

.animate-spin{
  animation: spin 1s linear infinite;
}

@keyframes wave{
  0%{
    transform: translateX(0) translateZ(0) scaleY(1);
  }

  50%{
    transform: translateX(-25%) translateZ(0) scaleY(0.8);
  }

  100%{
    transform: translateX(-50%) translateZ(0) scaleY(1);
  }
}

.animate-wave{
  animation: wave 12s -2s linear infinite;
}

.cursor-default{
  cursor: default;
}

.cursor-pointer{
  cursor: pointer;
}

.touch-none{
  touch-action: none;
}

.touch-manipulation{
  touch-action: manipulation;
}

.select-none{
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.resize-y{
  resize: vertical;
}

.resize{
  resize: both;
}

.scroll-mt-24{
  scroll-margin-top: 6rem;
}

.list-inside{
  list-style-position: inside;
}

.list-decimal{
  list-style-type: decimal;
}

.list-disc{
  list-style-type: disc;
}

.list-none{
  list-style-type: none;
}

.grid-cols-1{
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2{
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3{
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4{
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.flex-row{
  flex-direction: row;
}

.flex-col{
  flex-direction: column;
}

.flex-col-reverse{
  flex-direction: column-reverse;
}

.flex-wrap{
  flex-wrap: wrap;
}

.items-start{
  align-items: flex-start;
}

.items-end{
  align-items: flex-end;
}

.items-center{
  align-items: center;
}

.items-stretch{
  align-items: stretch;
}

.justify-start{
  justify-content: flex-start;
}

.justify-end{
  justify-content: flex-end;
}

.justify-center{
  justify-content: center;
}

.justify-between{
  justify-content: space-between;
}

.justify-around{
  justify-content: space-around;
}

.justify-evenly{
  justify-content: space-evenly;
}

.gap-0{
  gap: 0px;
}

.gap-1{
  gap: 0.25rem;
}

.gap-1\.5{
  gap: 0.375rem;
}

.gap-10{
  gap: 2.5rem;
}

.gap-12{
  gap: 3rem;
}

.gap-16{
  gap: 4rem;
}

.gap-2{
  gap: 0.5rem;
}

.gap-3{
  gap: 0.75rem;
}

.gap-4{
  gap: 1rem;
}

.gap-6{
  gap: 1.5rem;
}

.gap-8{
  gap: 2rem;
}

.gap-mobile-lg{
  gap: 1rem;
}

.gap-mobile-md{
  gap: 0.75rem;
}

.gap-mobile-sm{
  gap: 0.5rem;
}

.gap-mobile-xl{
  gap: 1.5rem;
}

.space-x-1 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-3 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-4 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-8 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(2rem * var(--tw-space-x-reverse));
  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-mobile-md > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-y-0 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0px * var(--tw-space-y-reverse));
}

.space-y-1 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}

.space-y-1\.5 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));
}

.space-y-10 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(2.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2.5rem * var(--tw-space-y-reverse));
}

.space-y-12 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(3rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(3rem * var(--tw-space-y-reverse));
}

.space-y-16 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(4rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(4rem * var(--tw-space-y-reverse));
}

.space-y-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

.space-y-3 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.space-y-4 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

.space-y-5 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));
}

.space-y-6 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}

.space-y-7 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.75rem * var(--tw-space-y-reverse));
}

.space-y-8 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}

.space-y-mobile-lg > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

.space-y-mobile-md > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.space-y-mobile-sm > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

.space-y-mobile-xl > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}

.overflow-auto{
  overflow: auto;
}

.overflow-hidden{
  overflow: hidden;
}

.overflow-x-auto{
  overflow-x: auto;
}

.overflow-y-auto{
  overflow-y: auto;
}

.overflow-x-hidden{
  overflow-x: hidden;
}

.truncate{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.whitespace-normal{
  white-space: normal;
}

.whitespace-nowrap{
  white-space: nowrap;
}

.whitespace-pre-line{
  white-space: pre-line;
}

.whitespace-pre-wrap{
  white-space: pre-wrap;
}

.text-wrap{
  text-wrap: wrap;
}

.break-words{
  overflow-wrap: break-word;
}

.rounded{
  border-radius: 0.25rem;
}

.rounded-2xl{
  border-radius: 1rem;
}

.rounded-\[inherit\]{
  border-radius: inherit;
}

.rounded-full{
  border-radius: 9999px;
}

.rounded-lg{
  border-radius: var(--radius);
}

.rounded-md{
  border-radius: calc(var(--radius) - 2px);
}

.rounded-none{
  border-radius: 0px;
}

.rounded-sm{
  border-radius: calc(var(--radius) - 4px);
}

.rounded-xl{
  border-radius: 0.75rem;
}

.rounded-r-lg{
  border-top-right-radius: var(--radius);
  border-bottom-right-radius: var(--radius);
}

.rounded-t-\[10px\]{
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.rounded-t-lg{
  border-top-left-radius: var(--radius);
  border-top-right-radius: var(--radius);
}

.rounded-t-xl{
  border-top-left-radius: 0.75rem;
  border-top-right-radius: 0.75rem;
}

.rounded-tl-sm{
  border-top-left-radius: calc(var(--radius) - 4px);
}

.border{
  border-width: 1px;
}

.border-0{
  border-width: 0px;
}

.border-2{
  border-width: 2px;
}

.border-4{
  border-width: 4px;
}

.border-y{
  border-top-width: 1px;
  border-bottom-width: 1px;
}

.border-b{
  border-bottom-width: 1px;
}

.border-b-2{
  border-bottom-width: 2px;
}

.border-l{
  border-left-width: 1px;
}

.border-l-4{
  border-left-width: 4px;
}

.border-r{
  border-right-width: 1px;
}

.border-t{
  border-top-width: 1px;
}

.border-dashed{
  border-style: dashed;
}

.border-blue-200{
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
}

.border-blue-500{
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}

.border-border{
  border-color: hsl(var(--border));
}

.border-border\/20{
  border-color: hsl(var(--border) / 0.2);
}

.border-border\/30{
  border-color: hsl(var(--border) / 0.3);
}

.border-border\/40{
  border-color: hsl(var(--border) / 0.4);
}

.border-border\/50{
  border-color: hsl(var(--border) / 0.5);
}

.border-border\/60{
  border-color: hsl(var(--border) / 0.6);
}

.border-border\/70{
  border-color: hsl(var(--border) / 0.7);
}

.border-cyan-500{
  --tw-border-opacity: 1;
  border-color: rgb(6 182 212 / var(--tw-border-opacity, 1));
}

.border-error{
  border-color: hsl(var(--error));
}

.border-error\/30{
  border-color: hsl(var(--error) / 0.3);
}

.border-error\/50{
  border-color: hsl(var(--error) / 0.5);
}

.border-gray-500{
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity, 1));
}

.border-green-200{
  --tw-border-opacity: 1;
  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));
}

.border-green-500{
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
}

.border-indigo-500{
  --tw-border-opacity: 1;
  border-color: rgb(99 102 241 / var(--tw-border-opacity, 1));
}

.border-info{
  border-color: hsl(var(--info));
}

.border-info\/20{
  border-color: hsl(var(--info) / 0.2);
}

.border-info\/30{
  border-color: hsl(var(--info) / 0.3);
}

.border-info\/50{
  border-color: hsl(var(--info) / 0.5);
}

.border-info\/70{
  border-color: hsl(var(--info) / 0.7);
}

.border-input{
  border-color: hsl(var(--input));
}

.border-medical-blue{
  border-color: hsl(var(--medical-blue));
}

.border-medical-blue\/30{
  border-color: hsl(var(--medical-blue) / 0.3);
}

.border-medical-blue\/50{
  border-color: hsl(var(--medical-blue) / 0.5);
}

.border-orange-500{
  --tw-border-opacity: 1;
  border-color: rgb(249 115 22 / var(--tw-border-opacity, 1));
}

.border-pink-500{
  --tw-border-opacity: 1;
  border-color: rgb(236 72 153 / var(--tw-border-opacity, 1));
}

.border-primary{
  border-color: hsl(var(--primary));
}

.border-primary\/10{
  border-color: hsl(var(--primary) / 0.1);
}

.border-primary\/20{
  border-color: hsl(var(--primary) / 0.2);
}

.border-primary\/30{
  border-color: hsl(var(--primary) / 0.3);
}

.border-primary\/40{
  border-color: hsl(var(--primary) / 0.4);
}

.border-primary\/50{
  border-color: hsl(var(--primary) / 0.5);
}

.border-purple-200{
  --tw-border-opacity: 1;
  border-color: rgb(233 213 255 / var(--tw-border-opacity, 1));
}

.border-purple-500{
  --tw-border-opacity: 1;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}

.border-red-200{
  --tw-border-opacity: 1;
  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));
}

.border-red-500{
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}

.border-success{
  border-color: hsl(var(--success));
}

.border-success\/20{
  border-color: hsl(var(--success) / 0.2);
}

.border-success\/30{
  border-color: hsl(var(--success) / 0.3);
}

.border-success\/50{
  border-color: hsl(var(--success) / 0.5);
}

.border-success\/70{
  border-color: hsl(var(--success) / 0.7);
}

.border-transparent{
  border-color: transparent;
}

.border-warning\/20{
  border-color: hsl(var(--warning) / 0.2);
}

.border-warning\/30{
  border-color: hsl(var(--warning) / 0.3);
}

.border-l-destructive{
  border-left-color: hsl(var(--destructive));
}

.border-l-info{
  border-left-color: hsl(var(--info));
}

.border-l-muted{
  border-left-color: hsl(var(--muted));
}

.border-l-orange-500{
  --tw-border-opacity: 1;
  border-left-color: rgb(249 115 22 / var(--tw-border-opacity, 1));
}

.border-l-primary{
  border-left-color: hsl(var(--primary));
}

.border-l-purple-500{
  --tw-border-opacity: 1;
  border-left-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}

.border-l-secondary{
  border-left-color: hsl(var(--secondary));
}

.border-l-success{
  border-left-color: hsl(var(--success));
}

.border-l-transparent{
  border-left-color: transparent;
}

.border-l-warning{
  border-left-color: hsl(var(--warning));
}

.border-t-transparent{
  border-top-color: transparent;
}

.bg-accent{
  background-color: hsl(var(--accent));
}

.bg-accent\/10{
  background-color: hsl(var(--accent) / 0.1);
}

.bg-accent\/5{
  background-color: hsl(var(--accent) / 0.05);
}

.bg-background{
  background-color: hsl(var(--background));
}

.bg-background\/50{
  background-color: hsl(var(--background) / 0.5);
}

.bg-background\/60{
  background-color: hsl(var(--background) / 0.6);
}

.bg-background\/70{
  background-color: hsl(var(--background) / 0.7);
}

.bg-background\/80{
  background-color: hsl(var(--background) / 0.8);
}

.bg-background\/90{
  background-color: hsl(var(--background) / 0.9);
}

.bg-background\/95{
  background-color: hsl(var(--background) / 0.95);
}

.bg-blue-100{
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}

.bg-blue-50{
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}

.bg-border{
  background-color: hsl(var(--border));
}

.bg-border\/50{
  background-color: hsl(var(--border) / 0.5);
}

.bg-card{
  background-color: hsl(var(--card));
}

.bg-card\/30{
  background-color: hsl(var(--card) / 0.3);
}

.bg-card\/50{
  background-color: hsl(var(--card) / 0.5);
}

.bg-card\/70{
  background-color: hsl(var(--card) / 0.7);
}

.bg-card\/80{
  background-color: hsl(var(--card) / 0.8);
}

.bg-card\/90{
  background-color: hsl(var(--card) / 0.9);
}

.bg-card\/95{
  background-color: hsl(var(--card) / 0.95);
}

.bg-current{
  background-color: currentColor;
}

.bg-cyan-50{
  --tw-bg-opacity: 1;
  background-color: rgb(236 254 255 / var(--tw-bg-opacity, 1));
}

.bg-error{
  background-color: hsl(var(--error));
}

.bg-error-light{
  background-color: hsl(var(--error-light));
}

.bg-error-light\/20{
  background-color: hsl(var(--error-light) / 0.2);
}

.bg-error-light\/30{
  background-color: hsl(var(--error-light) / 0.3);
}

.bg-foreground{
  background-color: hsl(var(--foreground));
}

.bg-foreground\/40{
  background-color: hsl(var(--foreground) / 0.4);
}

.bg-foreground\/50{
  background-color: hsl(var(--foreground) / 0.5);
}

.bg-foreground\/70{
  background-color: hsl(var(--foreground) / 0.7);
}

.bg-foreground\/80{
  background-color: hsl(var(--foreground) / 0.8);
}

.bg-foreground\/90{
  background-color: hsl(var(--foreground) / 0.9);
}

.bg-gray-100{
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.bg-gray-50{
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.bg-gray-500{
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}

.bg-green-100{
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}

.bg-green-50{
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}

.bg-green-500{
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}

.bg-indigo-50{
  --tw-bg-opacity: 1;
  background-color: rgb(238 242 255 / var(--tw-bg-opacity, 1));
}

.bg-info{
  background-color: hsl(var(--info));
}

.bg-info-light{
  background-color: hsl(var(--info-light));
}

.bg-info-light\/10{
  background-color: hsl(var(--info-light) / 0.1);
}

.bg-info-light\/20{
  background-color: hsl(var(--info-light) / 0.2);
}

.bg-info-light\/30{
  background-color: hsl(var(--info-light) / 0.3);
}

.bg-info-light\/80{
  background-color: hsl(var(--info-light) / 0.8);
}

.bg-info\/10{
  background-color: hsl(var(--info) / 0.1);
}

.bg-info\/20{
  background-color: hsl(var(--info) / 0.2);
}

.bg-info\/5{
  background-color: hsl(var(--info) / 0.05);
}

.bg-info\/50{
  background-color: hsl(var(--info) / 0.5);
}

.bg-info\/80{
  background-color: hsl(var(--info) / 0.8);
}

.bg-medical-blue{
  background-color: hsl(var(--medical-blue));
}

.bg-medical-blue-light{
  background-color: hsl(var(--medical-blue-light));
}

.bg-medical-blue\/10{
  background-color: hsl(var(--medical-blue) / 0.1);
}

.bg-muted{
  background-color: hsl(var(--muted));
}

.bg-muted-foreground{
  background-color: hsl(var(--muted-foreground));
}

.bg-muted-foreground\/30{
  background-color: hsl(var(--muted-foreground) / 0.3);
}

.bg-muted\/10{
  background-color: hsl(var(--muted) / 0.1);
}

.bg-muted\/20{
  background-color: hsl(var(--muted) / 0.2);
}

.bg-muted\/30{
  background-color: hsl(var(--muted) / 0.3);
}

.bg-muted\/40{
  background-color: hsl(var(--muted) / 0.4);
}

.bg-muted\/5{
  background-color: hsl(var(--muted) / 0.05);
}

.bg-muted\/50{
  background-color: hsl(var(--muted) / 0.5);
}

.bg-muted\/80{
  background-color: hsl(var(--muted) / 0.8);
}

.bg-orange-100{
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));
}

.bg-orange-50{
  --tw-bg-opacity: 1;
  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));
}

.bg-orange-500{
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
}

.bg-pink-100{
  --tw-bg-opacity: 1;
  background-color: rgb(252 231 243 / var(--tw-bg-opacity, 1));
}

.bg-pink-50{
  --tw-bg-opacity: 1;
  background-color: rgb(253 242 248 / var(--tw-bg-opacity, 1));
}

.bg-popover{
  background-color: hsl(var(--popover));
}

.bg-primary{
  background-color: hsl(var(--primary));
}

.bg-primary\/10{
  background-color: hsl(var(--primary) / 0.1);
}

.bg-primary\/15{
  background-color: hsl(var(--primary) / 0.15);
}

.bg-primary\/20{
  background-color: hsl(var(--primary) / 0.2);
}

.bg-primary\/30{
  background-color: hsl(var(--primary) / 0.3);
}

.bg-primary\/5{
  background-color: hsl(var(--primary) / 0.05);
}

.bg-primary\/50{
  background-color: hsl(var(--primary) / 0.5);
}

.bg-purple-100{
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
}

.bg-purple-50{
  --tw-bg-opacity: 1;
  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));
}

.bg-red-100{
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}

.bg-red-50{
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}

.bg-red-500{
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}

.bg-secondary{
  background-color: hsl(var(--secondary));
}

.bg-secondary\/10{
  background-color: hsl(var(--secondary) / 0.1);
}

.bg-secondary\/5{
  background-color: hsl(var(--secondary) / 0.05);
}

.bg-success{
  background-color: hsl(var(--success));
}

.bg-success-light{
  background-color: hsl(var(--success-light));
}

.bg-success-light\/30{
  background-color: hsl(var(--success-light) / 0.3);
}

.bg-success\/10{
  background-color: hsl(var(--success) / 0.1);
}

.bg-success\/5{
  background-color: hsl(var(--success) / 0.05);
}

.bg-success\/80{
  background-color: hsl(var(--success) / 0.8);
}

.bg-transparent{
  background-color: transparent;
}

.bg-warning{
  background-color: hsl(var(--warning));
}

.bg-warning-light{
  background-color: hsl(var(--warning-light));
}

.bg-warning-light\/30{
  background-color: hsl(var(--warning-light) / 0.3);
}

.bg-warning\/10{
  background-color: hsl(var(--warning) / 0.1);
}

.bg-yellow-100{
  --tw-bg-opacity: 1;
  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));
}

.bg-yellow-500{
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}

.bg-gradient-to-bl{
  background-image: linear-gradient(to bottom left, var(--tw-gradient-stops));
}

.bg-gradient-to-br{
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.bg-gradient-to-r{
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.bg-gradient-to-t{
  background-image: linear-gradient(to top, var(--tw-gradient-stops));
}

.from-background{
  --tw-gradient-from: hsl(var(--background)) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--background) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-background\/20{
  --tw-gradient-from: hsl(var(--background) / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--background) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-background\/30{
  --tw-gradient-from: hsl(var(--background) / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--background) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-background\/80{
  --tw-gradient-from: hsl(var(--background) / 0.8) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--background) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-black\/10{
  --tw-gradient-from: rgb(0 0 0 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-black\/20{
  --tw-gradient-from: rgb(0 0 0 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-black\/60{
  --tw-gradient-from: rgb(0 0 0 / 0.6) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-blue-50{
  --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-card\/90{
  --tw-gradient-from: hsl(var(--card) / 0.9) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--card) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-info-light\/20{
  --tw-gradient-from: hsl(var(--info-light) / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--info-light) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-info-light\/70{
  --tw-gradient-from: hsl(var(--info-light) / 0.7) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--info-light) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-info-light\/80{
  --tw-gradient-from: hsl(var(--info-light) / 0.8) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--info-light) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-muted{
  --tw-gradient-from: hsl(var(--muted)) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--muted) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-muted\/10{
  --tw-gradient-from: hsl(var(--muted) / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--muted) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-muted\/20{
  --tw-gradient-from: hsl(var(--muted) / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--muted) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-muted\/30{
  --tw-gradient-from: hsl(var(--muted) / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--muted) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-primary{
  --tw-gradient-from: hsl(var(--primary)) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-primary\/10{
  --tw-gradient-from: hsl(var(--primary) / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-primary\/20{
  --tw-gradient-from: hsl(var(--primary) / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-primary\/5{
  --tw-gradient-from: hsl(var(--primary) / 0.05) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-purple-50{
  --tw-gradient-from: #faf5ff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(250 245 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-slate-900{
  --tw-gradient-from: #0f172a var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(15 23 42 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-white{
  --tw-gradient-from: #fff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-white\/80{
  --tw-gradient-from: rgb(255 255 255 / 0.8) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.via-background{
  --tw-gradient-to: hsl(var(--background) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--background)) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-background\/95{
  --tw-gradient-to: hsl(var(--background) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--background) / 0.95) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-card{
  --tw-gradient-to: hsl(var(--card) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--card)) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-info-light{
  --tw-gradient-to: hsl(var(--info-light) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--info-light)) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-info-light\/20{
  --tw-gradient-to: hsl(var(--info-light) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--info-light) / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-info-light\/30{
  --tw-gradient-to: hsl(var(--info-light) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--info-light) / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-info-light\/50{
  --tw-gradient-to: hsl(var(--info-light) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--info-light) / 0.5) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-info-light\/60{
  --tw-gradient-to: hsl(var(--info-light) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--info-light) / 0.6) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-muted{
  --tw-gradient-to: hsl(var(--muted) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--muted)) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-muted\/30{
  --tw-gradient-to: hsl(var(--muted) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--muted) / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-primary\/15{
  --tw-gradient-to: hsl(var(--primary) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--primary) / 0.15) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-primary\/5{
  --tw-gradient-to: hsl(var(--primary) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--primary) / 0.05) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-slate-800{
  --tw-gradient-to: rgb(30 41 59 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #1e293b var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-transparent{
  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), transparent var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-white{
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #fff var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.to-background{
  --tw-gradient-to: hsl(var(--background)) var(--tw-gradient-to-position);
}

.to-indigo-50{
  --tw-gradient-to: #eef2ff var(--tw-gradient-to-position);
}

.to-info-light{
  --tw-gradient-to: hsl(var(--info-light)) var(--tw-gradient-to-position);
}

.to-info-light\/10{
  --tw-gradient-to: hsl(var(--info-light) / 0.1) var(--tw-gradient-to-position);
}

.to-info-light\/20{
  --tw-gradient-to: hsl(var(--info-light) / 0.2) var(--tw-gradient-to-position);
}

.to-info-light\/80{
  --tw-gradient-to: hsl(var(--info-light) / 0.8) var(--tw-gradient-to-position);
}

.to-muted{
  --tw-gradient-to: hsl(var(--muted)) var(--tw-gradient-to-position);
}

.to-muted\/10{
  --tw-gradient-to: hsl(var(--muted) / 0.1) var(--tw-gradient-to-position);
}

.to-muted\/20{
  --tw-gradient-to: hsl(var(--muted) / 0.2) var(--tw-gradient-to-position);
}

.to-muted\/30{
  --tw-gradient-to: hsl(var(--muted) / 0.3) var(--tw-gradient-to-position);
}

.to-muted\/80{
  --tw-gradient-to: hsl(var(--muted) / 0.8) var(--tw-gradient-to-position);
}

.to-pink-50{
  --tw-gradient-to: #fdf2f8 var(--tw-gradient-to-position);
}

.to-primary\/10{
  --tw-gradient-to: hsl(var(--primary) / 0.1) var(--tw-gradient-to-position);
}

.to-primary\/5{
  --tw-gradient-to: hsl(var(--primary) / 0.05) var(--tw-gradient-to-position);
}

.to-primary\/80{
  --tw-gradient-to: hsl(var(--primary) / 0.8) var(--tw-gradient-to-position);
}

.to-secondary\/5{
  --tw-gradient-to: hsl(var(--secondary) / 0.05) var(--tw-gradient-to-position);
}

.to-slate-900{
  --tw-gradient-to: #0f172a var(--tw-gradient-to-position);
}

.to-transparent{
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}

.to-white{
  --tw-gradient-to: #fff var(--tw-gradient-to-position);
}

.bg-cover{
  background-size: cover;
}

.bg-clip-text{
  -webkit-background-clip: text;
          background-clip: text;
}

.bg-center{
  background-position: center;
}

.bg-no-repeat{
  background-repeat: no-repeat;
}

.fill-current{
  fill: currentColor;
}

.fill-primary{
  fill: hsl(var(--primary));
}

.fill-white{
  fill: #fff;
}

.fill-yellow-400{
  fill: #facc15;
}

.object-contain{
  -o-object-fit: contain;
     object-fit: contain;
}

.object-cover{
  -o-object-fit: cover;
     object-fit: cover;
}

.p-0{
  padding: 0px;
}

.p-1{
  padding: 0.25rem;
}

.p-10{
  padding: 2.5rem;
}

.p-12{
  padding: 3rem;
}

.p-2{
  padding: 0.5rem;
}

.p-2\.5{
  padding: 0.625rem;
}

.p-3{
  padding: 0.75rem;
}

.p-4{
  padding: 1rem;
}

.p-5{
  padding: 1.25rem;
}

.p-6{
  padding: 1.5rem;
}

.p-7{
  padding: 1.75rem;
}

.p-8{
  padding: 2rem;
}

.p-\[1px\]{
  padding: 1px;
}

.p-mobile-lg{
  padding: 1rem;
}

.p-mobile-md{
  padding: 0.75rem;
}

.p-mobile-sm{
  padding: 0.5rem;
}

.p-mobile-xs{
  padding: 0.25rem;
}

.px-0{
  padding-left: 0px;
  padding-right: 0px;
}

.px-1{
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.px-10{
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}

.px-12{
  padding-left: 3rem;
  padding-right: 3rem;
}

.px-2{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-2\.5{
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}

.px-3{
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.px-4{
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-5{
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

.px-6{
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.px-8{
  padding-left: 2rem;
  padding-right: 2rem;
}

.px-mobile-lg{
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-mobile-md{
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.py-0\.5{
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}

.py-1{
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-1\.5{
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.py-12{
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.py-16{
  padding-top: 4rem;
  padding-bottom: 4rem;
}

.py-2{
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-20{
  padding-top: 5rem;
  padding-bottom: 5rem;
}

.py-24{
  padding-top: 6rem;
  padding-bottom: 6rem;
}

.py-3{
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.py-32{
  padding-top: 8rem;
  padding-bottom: 8rem;
}

.py-4{
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-5{
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}

.py-6{
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-8{
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.py-mobile-2xl{
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.py-mobile-lg{
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-mobile-md{
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.py-mobile-sm{
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-mobile-xl{
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-mobile-xs{
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.pb-10{
  padding-bottom: 2.5rem;
}

.pb-16{
  padding-bottom: 4rem;
}

.pb-2{
  padding-bottom: 0.5rem;
}

.pb-3{
  padding-bottom: 0.75rem;
}

.pb-4{
  padding-bottom: 1rem;
}

.pb-6{
  padding-bottom: 1.5rem;
}

.pb-8{
  padding-bottom: 2rem;
}

.pb-mobile-md{
  padding-bottom: 0.75rem;
}

.pl-0{
  padding-left: 0px;
}

.pl-10{
  padding-left: 2.5rem;
}

.pl-12{
  padding-left: 3rem;
}

.pl-2\.5{
  padding-left: 0.625rem;
}

.pl-4{
  padding-left: 1rem;
}

.pl-5{
  padding-left: 1.25rem;
}

.pl-6{
  padding-left: 1.5rem;
}

.pl-7{
  padding-left: 1.75rem;
}

.pl-8{
  padding-left: 2rem;
}

.pr-10{
  padding-right: 2.5rem;
}

.pr-2{
  padding-right: 0.5rem;
}

.pr-2\.5{
  padding-right: 0.625rem;
}

.pr-4{
  padding-right: 1rem;
}

.pr-6{
  padding-right: 1.5rem;
}

.pt-0{
  padding-top: 0px;
}

.pt-1{
  padding-top: 0.25rem;
}

.pt-2{
  padding-top: 0.5rem;
}

.pt-20{
  padding-top: 5rem;
}

.pt-3{
  padding-top: 0.75rem;
}

.pt-4{
  padding-top: 1rem;
}

.pt-6{
  padding-top: 1.5rem;
}

.pt-8{
  padding-top: 2rem;
}

.pt-mobile-lg{
  padding-top: 1rem;
}

.text-left{
  text-align: left;
}

.text-center{
  text-align: center;
}

.text-right{
  text-align: right;
}

.align-middle{
  vertical-align: middle;
}

.font-mono{
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

.text-2xl{
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-3xl{
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-4xl{
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.text-5xl{
  font-size: 3rem;
  line-height: 1;
}

.text-6xl{
  font-size: 3.75rem;
  line-height: 1;
}

.text-9xl{
  font-size: 8rem;
  line-height: 1;
}

.text-\[0\.8rem\]{
  font-size: 0.8rem;
}

.text-base{
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg{
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-mobile-sm{
  font-size: 0.8rem;
  line-height: 1.2rem;
}

.text-sm{
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xl{
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xs{
  font-size: 0.75rem;
  line-height: 1rem;
}

.font-bold{
  font-weight: 700;
}

.font-extrabold{
  font-weight: 800;
}

.font-medium{
  font-weight: 500;
}

.font-normal{
  font-weight: 400;
}

.font-semibold{
  font-weight: 600;
}

.uppercase{
  text-transform: uppercase;
}

.capitalize{
  text-transform: capitalize;
}

.italic{
  font-style: italic;
}

.leading-none{
  line-height: 1;
}

.leading-relaxed{
  line-height: 1.625;
}

.leading-snug{
  line-height: 1.375;
}

.leading-tight{
  line-height: 1.25;
}

.tracking-tight{
  letter-spacing: -0.025em;
}

.tracking-wider{
  letter-spacing: 0.05em;
}

.tracking-widest{
  letter-spacing: 0.1em;
}

.text-accent-foreground{
  color: hsl(var(--accent-foreground));
}

.text-background{
  color: hsl(var(--background));
}

.text-blue-600{
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}

.text-blue-800{
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}

.text-blue-900{
  --tw-text-opacity: 1;
  color: rgb(30 58 138 / var(--tw-text-opacity, 1));
}

.text-card-foreground{
  color: hsl(var(--card-foreground));
}

.text-current{
  color: currentColor;
}

.text-cyan-900{
  --tw-text-opacity: 1;
  color: rgb(22 78 99 / var(--tw-text-opacity, 1));
}

.text-error-foreground{
  color: hsl(var(--error-foreground));
}

.text-foreground{
  color: hsl(var(--foreground));
}

.text-foreground\/60{
  color: hsl(var(--foreground) / 0.6);
}

.text-foreground\/70{
  color: hsl(var(--foreground) / 0.7);
}

.text-foreground\/75{
  color: hsl(var(--foreground) / 0.75);
}

.text-foreground\/80{
  color: hsl(var(--foreground) / 0.8);
}

.text-foreground\/90{
  color: hsl(var(--foreground) / 0.9);
}

.text-gray-800{
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}

.text-green-500{
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}

.text-green-600{
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}

.text-green-700{
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}

.text-green-800{
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}

.text-green-900{
  --tw-text-opacity: 1;
  color: rgb(20 83 45 / var(--tw-text-opacity, 1));
}

.text-indigo-900{
  --tw-text-opacity: 1;
  color: rgb(49 46 129 / var(--tw-text-opacity, 1));
}

.text-info{
  color: hsl(var(--info));
}

.text-info-foreground{
  color: hsl(var(--info-foreground));
}

.text-medical-blue{
  color: hsl(var(--medical-blue));
}

.text-medical-blue-foreground{
  color: hsl(var(--medical-blue-foreground));
}

.text-muted-foreground{
  color: hsl(var(--muted-foreground));
}

.text-orange-800{
  --tw-text-opacity: 1;
  color: rgb(154 52 18 / var(--tw-text-opacity, 1));
}

.text-pink-800{
  --tw-text-opacity: 1;
  color: rgb(157 23 77 / var(--tw-text-opacity, 1));
}

.text-pink-900{
  --tw-text-opacity: 1;
  color: rgb(131 24 67 / var(--tw-text-opacity, 1));
}

.text-popover-foreground{
  color: hsl(var(--popover-foreground));
}

.text-primary{
  color: hsl(var(--primary));
}

.text-primary-foreground{
  color: hsl(var(--primary-foreground));
}

.text-primary-foreground\/80{
  color: hsl(var(--primary-foreground) / 0.8);
}

.text-primary-foreground\/90{
  color: hsl(var(--primary-foreground) / 0.9);
}

.text-primary\/80{
  color: hsl(var(--primary) / 0.8);
}

.text-primary\/90{
  color: hsl(var(--primary) / 0.9);
}

.text-purple-600{
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}

.text-purple-800{
  --tw-text-opacity: 1;
  color: rgb(107 33 168 / var(--tw-text-opacity, 1));
}

.text-purple-900{
  --tw-text-opacity: 1;
  color: rgb(88 28 135 / var(--tw-text-opacity, 1));
}

.text-red-500{
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.text-red-600{
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}

.text-red-700{
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}

.text-red-800{
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}

.text-red-900{
  --tw-text-opacity: 1;
  color: rgb(127 29 29 / var(--tw-text-opacity, 1));
}

.text-secondary{
  color: hsl(var(--secondary));
}

.text-secondary-foreground{
  color: hsl(var(--secondary-foreground));
}

.text-slate-300{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity, 1));
}

.text-success{
  color: hsl(var(--success));
}

.text-success-foreground{
  color: hsl(var(--success-foreground));
}

.text-transparent{
  color: transparent;
}

.text-warning{
  color: hsl(var(--warning));
}

.text-warning-foreground{
  color: hsl(var(--warning-foreground));
}

.text-white{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.text-yellow-800{
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity, 1));
}

.underline{
  text-decoration-line: underline;
}

.line-through{
  text-decoration-line: line-through;
}

.underline-offset-4{
  text-underline-offset: 4px;
}

.opacity-0{
  opacity: 0;
}

.opacity-10{
  opacity: 0.1;
}

.opacity-100{
  opacity: 1;
}

.opacity-20{
  opacity: 0.2;
}

.opacity-5{
  opacity: 0.05;
}

.opacity-50{
  opacity: 0.5;
}

.opacity-60{
  opacity: 0.6;
}

.opacity-70{
  opacity: 0.7;
}

.opacity-80{
  opacity: 0.8;
}

.shadow-2xl{
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-inner{
  --tw-shadow: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: inset 0 2px 4px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-lg{
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-md{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-sm{
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-xl{
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-primary\/10{
  --tw-shadow-color: hsl(var(--primary) / 0.1);
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-primary\/25{
  --tw-shadow-color: hsl(var(--primary) / 0.25);
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-slate-200\/40{
  --tw-shadow-color: rgb(226 232 240 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.outline-none{
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.outline{
  outline-style: solid;
}

.ring{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-0{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-2{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-primary{
  --tw-ring-color: hsl(var(--primary));
}

.ring-primary\/20{
  --tw-ring-color: hsl(var(--primary) / 0.2);
}

.ring-primary\/50{
  --tw-ring-color: hsl(var(--primary) / 0.5);
}

.ring-ring{
  --tw-ring-color: hsl(var(--ring));
}

.ring-opacity-50{
  --tw-ring-opacity: 0.5;
}

.ring-offset-background{
  --tw-ring-offset-color: hsl(var(--background));
}

.blur{
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.blur-2xl{
  --tw-blur: blur(40px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.blur-3xl{
  --tw-blur: blur(64px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.blur-sm{
  --tw-blur: blur(4px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.blur-xl{
  --tw-blur: blur(24px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.brightness-110{
  --tw-brightness: brightness(1.1);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.contrast-125{
  --tw-contrast: contrast(1.25);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.drop-shadow-sm{
  --tw-drop-shadow: drop-shadow(0 1px 1px rgb(0 0 0 / 0.05));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.filter{
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.backdrop-blur-lg{
  --tw-backdrop-blur: blur(16px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-md{
  --tw-backdrop-blur: blur(12px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-sm{
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-xl{
  --tw-backdrop-blur: blur(24px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.transition{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-all{
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-colors{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-opacity{
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-shadow{
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-transform{
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-1000{
  transition-duration: 1000ms;
}

.duration-200{
  transition-duration: 200ms;
}

.duration-300{
  transition-duration: 300ms;
}

.duration-500{
  transition-duration: 500ms;
}

.duration-700{
  transition-duration: 700ms;
}

.ease-in-out{
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes enter{
  from{
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}

@keyframes exit{
  to{
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}

.animate-in{
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}

.fade-in-0{
  --tw-enter-opacity: 0;
}

.fade-in-80{
  --tw-enter-opacity: 0.8;
}

.zoom-in-95{
  --tw-enter-scale: .95;
}

.duration-1000{
  animation-duration: 1000ms;
}

.duration-200{
  animation-duration: 200ms;
}

.duration-300{
  animation-duration: 300ms;
}

.duration-500{
  animation-duration: 500ms;
}

.duration-700{
  animation-duration: 700ms;
}

.ease-in-out{
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.running{
  animation-play-state: running;
}

.\[animation-delay\:-4s\]{
  animation-delay: -4s;
}

.\[animation-delay\:100ms\]{
  animation-delay: 100ms;
}

.\[animation-delay\:200ms\]{
  animation-delay: 200ms;
}

.\[animation-delay\:300ms\]{
  animation-delay: 300ms;
}

/* Premium FAQ Animations */

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.1);
  }

  50% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.2);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-10px);
  }
}

/* Custom utility classes */

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Enhanced scrollbar */

::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, hsl(var(--primary)), hsl(var(--primary) / 0.8));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, hsl(var(--primary) / 0.9), hsl(var(--primary) / 0.7));
}

/* Enhanced Mobile Components */

.mobile-card{
  border-radius: 0.75rem;
  border-width: 1px;
  border-color: hsl(var(--border) / 0.4);
  background-color: hsl(var(--card));
  padding: 1.5rem;
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  animation-duration: 300ms;
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
}

.dark .mobile-card {
  background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--card) / 0.98) 100%);
  border-color: hsl(var(--border) / 0.5);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.mobile-

  .dark .mobile-

  .mobile-input{
  min-height: 48px;
  touch-action: manipulation;
  border-radius: var(--radius);
  border-width: 2px;
  border-color: hsl(var(--border) / 0.5);
  font-size: 1rem;
  line-height: 1.5rem;
}

.mobile-

  .dark .mobile-

  .mobile-input:focus{
  border-color: hsl(var(--primary));
}

.mobile-

  .dark .mobile-

  .mobile-input {
  padding: 12px 16px;
}

.mobile-text{
  font-size: 1rem;
  line-height: 1.5rem;
  line-height: 1.6;
}

.mobile-heading{
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 700;
  line-height: 1.25;
  margin-bottom: 1rem;
}

.mobile-subheading{
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-weight: 600;
  line-height: 1.375;
  margin-bottom: 0.75rem;
}

/* Enhanced Touch-friendly utilities */

.touch-target{
  display: flex;
  min-height: 48px;
  min-width: 48px;
  align-items: center;
  justify-content: center;
  position: relative;
}

.touch-feedback{
  touch-action: manipulation;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  animation-duration: 150ms;
}

.touch-feedback:active{
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.touch-feedback {
  -webkit-tap-highlight-color: transparent;
}

.touch-feedback:active {
  transform: scale(0.95);
  opacity: 0.8;
}

.swipe-container{
  --tw-pan-x: pan-x;
  touch-action: var(--tw-pan-x) var(--tw-pan-y) var(--tw-pinch-zoom);
  overflow-x: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.swipe-container::-webkit-scrollbar {
  display: none;
}

.swipe-container {
  -webkit-overflow-scrolling: touch;
  scroll-snap-type: x mandatory;
}

.swipe-item {
  scroll-snap-align: start;
  flex-shrink: 0;
}

.wave-animation {
  animation: wave 12s linear infinite;
  animation-delay: -2s;
  transform-origin: center bottom;
}

.page-transition-enter {
  opacity: 0;
  transform: translateY(10px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 400ms, transform 400ms;
}

.page-transition-exit {
  opacity: 1;
}

.page-transition-exit-active {
  opacity: 0;
  transition: opacity 300ms;
}

/* Mobile-specific utilities */

.mobile-safe-area {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

.mobile-scroll-smooth {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

.mobile-no-scroll {
  overflow: hidden;
  position: fixed;
  width: 100%;
}

.mobile-backdrop {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Hide scrollbars on mobile */

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Touch-specific styles */

@media (hover: none) and (pointer: coarse) {
  .hover-only {
    display: none;
  }

  .touch-only {
    display: block;
  }

  /* Larger touch targets on touch devices */

  /* Remove hover effects on touch devices */

  .hover\:scale-105:hover {
    transform: none;
  }

  .hover\:shadow-lg:hover {
    box-shadow: none;
  }
}

@media (hover: hover) and (pointer: fine) {
  .touch-only {
    display: none;
  }

  .hover-only {
    display: block;
  }
}

/* Enhanced Mobile typography improvements */

@media (max-width: 640px) {
  h1 {
    font-size: 2.25rem;
    line-height: 2.5rem;
    word-wrap: break-word;
    -webkit-hyphens: auto;
            hyphens: auto;
    margin-bottom: 1.5rem;
  }

  h2 {
    font-size: 1.875rem;
    line-height: 2.25rem;
    margin-bottom: 1.25rem;
  }

  h3 {
    font-size: 1.5rem;
    line-height: 1.875rem;
    margin-bottom: 1rem;
  }

  h4 {
    font-size: 1.25rem;
    line-height: 1.625rem;
    margin-bottom: 0.875rem;
  }

  p {
    font-size: 1rem;
    line-height: 1.6rem;
    word-wrap: break-word;
    margin-bottom: 1rem;
  }

  .lead {
    font-size: 1.125rem;
    line-height: 1.75rem;
    margin-bottom: 1.5rem;
  }

  /* Enhanced mobile dark mode text */

  .dark h1, .dark h2, .dark h3, .dark h4 {
    color: hsl(var(--foreground));
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .dark p {
    color: hsl(var(--foreground) / 0.9);
  }

  .dark .lead {
    color: hsl(var(--foreground) / 0.95);
  }
}

/* Responsive text overflow prevention */

.text-responsive {
  word-wrap: break-word;
  overflow-wrap: break-word;
  -webkit-hyphens: auto;
          hyphens: auto;
}

/* Breadcrumb responsive improvements */

.breadcrumb-responsive {
  word-break: break-word;
  overflow-wrap: break-word;
}

/* Accessibility Enhancements */

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Enhanced screen reader support */

.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  padding: 0.5rem 1rem;
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  border-radius: 4px;
  font-weight: 600;
  z-index: 9999;
}

/* ARIA live region styling */

.aria-live-region {
  position: absolute;
  left: -10000px;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

.aria-live-region.polite {
  speak: normal;
}

.aria-live-region.assertive {
  speak: assertive;
}

.skip-link {
  position: absolute;
  top: -100px;
  left: 6px;
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  padding: 8px 16px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
  transition: top 0.3s;
  opacity: 0;
  pointer-events: none;
}

.skip-link:focus {
  top: 6px;
  opacity: 1;
  pointer-events: auto;
}

/* Enhanced Focus management for accessibility */

.keyboard-navigation *:focus-visible {
  outline: 3px solid hsl(var(--ring));
  outline-offset: 2px;
  border-radius: 4px;
}

.keyboard-navigation

  /* Enhanced dark mode focus states */
  .dark .keyboard-navigation *:focus-visible {
  outline-color: hsl(var(--primary));
  box-shadow: 0 0 0 6px hsl(var(--primary) / 0.3);
}

/* Skip link enhancements */

.skip-link:focus {
  top: 6px;
  opacity: 1;
  pointer-events: auto;
  z-index: 9999;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* High contrast mode support */

@media (prefers-contrast: high) {
  :root {
    --border: 0 0% 0%;
    --input: 0 0% 0%;
    --foreground: 0 0% 0%;
    --background: 0 0% 100%;
    --primary: 210 100% 30%;
    --muted-foreground: 0 0% 20%;
  }

  .dark {
    --border: 0 0% 100%;
    --input: 0 0% 100%;
    --foreground: 0 0% 100%;
    --background: 0 0% 0%;
    --primary: 210 100% 80%;
    --muted-foreground: 0 0% 80%;
  }

  /* Enhanced contrast for interactive elements */

  .medical-card, .mobile-card {
    border-width: 2px;
  }
}

/* Reduced motion support */

@media (prefers-reduced-motion: reduce) {
  *,
    *::before,
    *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .wave-animation {
    animation: none;
  }
}

/* Cross-browser compatibility enhancements */

/* CSS Custom Properties fallbacks for older browsers */

.medical-card {
  background-color: white;
  /* Fallback */
  background: hsl(var(--card));
  border-color: #e0e0e0;
  /* Fallback */
  border-color: hsl(var(--border) / 0.4);
}

/* Webkit-specific enhancements */

@supports (-webkit-backdrop-filter: blur(8px)) {
  .medical-card, .mobile-card {
    -webkit-backdrop-filter: blur(8px);
  }
}

/* Firefox-specific enhancements */

@-moz-document url-prefix() {
  .medical-card, .mobile-card {
    background-color: hsl(var(--card) / 0.95);
  }

  .dark .medical-card, .dark .mobile-card {
    background-color: hsl(var(--card) / 0.95);
  }
}

/* Defensive CSS against external tool interference */

/* Prevent external tools from affecting our layout */

body > div:not(#root),
  body > iframe:not([src*="mineuro.com.au"]),
  body > [class*="extension-"],
  body > [id*="extension-"],
  body > [class*="widget-"],
  body > [id*="widget-"] {
  z-index: 999999 !important;
  pointer-events: auto !important;
}

/* Ensure our content stays properly aligned */

#root {
  position: relative;
  z-index: 1;
}

/* Prevent external styles from affecting our
  }

  /* Reduced motion support for accessibility */

@media (prefers-reduced-motion: reduce) {
}

/* Screen reader only content */

.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* Performance optimizations */

.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Cross-browser theme consistency */

/* Ensure consistent rendering across browsers */

* {
  box-sizing: border-box;
}

/* Safari-specific fixes */

@supports (-webkit-appearance: none) {
  input, select, textarea {
    -webkit-appearance: none;
    border-radius: 0;
  }
}

/* Edge/IE compatibility */

@supports (-ms-ime-align: auto) {
  .medical-card {
    background: hsl(var(--card));
    border: 1px solid hsl(var(--border));
  }

  .dark .medical-card {
    background: hsl(var(--card));
    border: 1px solid hsl(var(--border));
  }
}

/* Loading states */

.loading-skeleton {
  background: linear-gradient(90deg,
      hsl(var(--muted)) 25%,
      hsl(var(--muted-foreground) / 0.1) 50%,
      hsl(var(--muted)) 75%);
  background-size: 200% 100%;
  animation: loading-shimmer 2s infinite;
}

@keyframes loading-shimmer {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

/* Print styles */

@media print {
  .no-print {
    display: none !important;
  }

  body {
    background: white !important;
    color: black !important;
  }

  a[href]:after {
    content: " (" attr(href) ")";
  }

  .page-break {
    page-break-before: always;
  }
}

.file\:text-foreground::file-selector-button {
  color: hsl(var(--foreground));
}

@media print {
  .placeholder\:text-muted-foreground::-moz-placeholder {
    color: #666 !important;
  }
  .placeholder\:text-muted-foreground::placeholder {
    color: #666 !important;
  }
}

.after\:text-foreground::after {
  content: var(--tw-content);
  color: hsl(var(--foreground));
}

.hover\:text-info:hover {
  color: hsl(var(--info));
}

.hover\:text-foreground:hover {
  color: hsl(var(--foreground));
}

.hover\:bg-muted:hover {
  background-color: hsl(var(--muted));
}

@media print {
  .hover\:text-muted-foreground:hover {
    color: #666 !important;
  }
}

.group.toaster .group-\[\.toaster\]\:text-foreground {
  color: hsl(var(--foreground));
}

.group.toast .group-\[\.toast\]\:bg-muted {
  background-color: hsl(var(--muted));
}

.group.toaster .group-\[\.toaster\]\:border-border {
  border-color: hsl(var(--border));
}

@media print {
  .group.toast .group-\[\.toast\]\:text-muted-foreground {
    color: #666 !important;
  }

  .aria-selected\:text-muted-foreground[aria-selected="true"] {
    color: #666 !important;
  }
}

.data-\[state\=active\]\:text-foreground[data-state="active"] {
  color: hsl(var(--foreground));
}

.data-\[state\=selected\]\:bg-muted[data-state="selected"] {
  background-color: hsl(var(--muted));
}

@media print {
  .data-\[state\=open\]\:text-muted-foreground[data-state="open"] {
    color: #666 !important;
  }
}

.dark\:section-background-muted:is(.dark *){
  background-color: hsl(var(--muted) / 0.2);
  border-bottom: 1px solid hsl(var(--border) / 0.1);
}

.dark\:bg-muted-light:is(.dark *) {
  background-color: hsl(var(--muted));
}

.dark\:text-info:is(.dark *) {
  color: hsl(var(--info));
}

.dark\:text-success:is(.dark *) {
  color: hsl(var(--success));
}

.dark\:text-foreground:is(.dark *) {
  color: hsl(var(--foreground));
}

.dark\:text-medical-blue:is(.dark *) {
  color: hsl(var(--medical-blue));
}

.dark\:border-info\/30:is(.dark *) {
  border-color: hsl(var(--info) / 0.3);
}

.dark\:border-success\/30:is(.dark *) {
  border-color: hsl(var(--success) / 0.3);
}

.dark\:bg-info:is(.dark *) {
  background-color: hsl(var(--info));
}

.dark\:bg-success:is(.dark *) {
  background-color: hsl(var(--success));
}

.dark\:bg-muted:is(.dark *) {
  background-color: hsl(var(--muted));
}

.dark\:border-info:is(.dark *) {
  border-color: hsl(var(--info));
}

.dark\:border-success:is(.dark *) {
  border-color: hsl(var(--success));
}

.dark\:border-border:is(.dark *) {
  border-color: hsl(var(--border));
}

@media print {
  .dark\:text-muted-foreground:is(.dark *) {
    color: #666 !important;
  }
}

.dark\:hover\:bg-muted:hover:is(.dark *) {
  background-color: hsl(var(--muted));
}

@media (min-width: 768px){
  .md\:text-enhanced-heading {
    font-size: 1.5rem;
    line-height: 2rem;
    font-weight: 700;
    line-height: 1.25;
    color: hsl(var(--foreground));
  }

  @media (min-width: 768px){
    .md\:text-enhanced-heading{
      font-size: 1.875rem;
      line-height: 2.25rem;
    }
  }

  @media (min-width: 1024px){
    .md\:text-enhanced-heading{
      font-size: 2.25rem;
      line-height: 2.5rem;
    }
  }

  .md\:text-enhanced-heading {
    letter-spacing: -0.025em;
  }

  .dark .md\:text-enhanced-heading {
    color: hsl(var(--foreground));
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
}

@media (min-width: 1024px){
  .lg\:text-enhanced-heading {
    font-size: 1.5rem;
    line-height: 2rem;
    font-weight: 700;
    line-height: 1.25;
    color: hsl(var(--foreground));
  }

  @media (min-width: 768px){
    .lg\:text-enhanced-heading{
      font-size: 1.875rem;
      line-height: 2.25rem;
    }
  }

  @media (min-width: 1024px){
    .lg\:text-enhanced-heading{
      font-size: 2.25rem;
      line-height: 2.5rem;
    }
  }

  .lg\:text-enhanced-heading {
    letter-spacing: -0.025em;
  }

  .dark .lg\:text-enhanced-heading {
    color: hsl(var(--foreground));
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
}

.\[\&\>\*\]\:text-foreground>* {
  color: hsl(var(--foreground));
}

.\[\&\>\*\]\:text-medical-blue-foreground>* {
  color: hsl(var(--medical-blue-foreground));
}

@media print {
  .\[\&\>\*\]\:text-muted-foreground>* {
    color: #666 !important;
  }
}

.\[\&\>svg\]\:text-foreground>svg {
  color: hsl(var(--foreground));
}

@media print {
  .\[\&_\[cmdk-group-heading\]\]\:text-muted-foreground [cmdk-group-heading] {
    color: #666 !important;
  }
}

.file\:border-0::file-selector-button{
  border-width: 0px;
}

.file\:bg-transparent::file-selector-button{
  background-color: transparent;
}

.file\:text-sm::file-selector-button{
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.file\:font-medium::file-selector-button{
  font-weight: 500;
}

.file\:text-foreground::file-selector-button{
  color: hsl(var(--foreground));
}

.placeholder\:text-muted-foreground::-moz-placeholder{
  color: hsl(var(--muted-foreground));
}

.placeholder\:text-muted-foreground::placeholder{
  color: hsl(var(--muted-foreground));
}

.after\:absolute::after{
  content: var(--tw-content);
  position: absolute;
}

.after\:inset-y-0::after{
  content: var(--tw-content);
  top: 0px;
  bottom: 0px;
}

.after\:left-1\/2::after{
  content: var(--tw-content);
  left: 50%;
}

.after\:ml-1::after{
  content: var(--tw-content);
  margin-left: 0.25rem;
}

.after\:w-1::after{
  content: var(--tw-content);
  width: 0.25rem;
}

.after\:-translate-x-1\/2::after{
  content: var(--tw-content);
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.after\:text-foreground::after{
  content: var(--tw-content);
  color: hsl(var(--foreground));
}

.after\:content-\[\'\*\'\]::after{
  --tw-content: '*';
  content: var(--tw-content);
}

.first\:rounded-l-md:first-child{
  border-top-left-radius: calc(var(--radius) - 2px);
  border-bottom-left-radius: calc(var(--radius) - 2px);
}

.first\:border-l:first-child{
  border-left-width: 1px;
}

.first\:border-t-0:first-child{
  border-top-width: 0px;
}

.first\:pt-0:first-child{
  padding-top: 0px;
}

.last\:mb-0:last-child{
  margin-bottom: 0px;
}

.last\:rounded-r-md:last-child{
  border-top-right-radius: calc(var(--radius) - 2px);
  border-bottom-right-radius: calc(var(--radius) - 2px);
}

.last\:border-b-0:last-child{
  border-bottom-width: 0px;
}

.last\:pb-0:last-child{
  padding-bottom: 0px;
}

.focus-within\:relative:focus-within{
  position: relative;
}

.focus-within\:z-20:focus-within{
  z-index: 20;
}

.hover\:scale-105:hover{
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-110:hover{
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-\[1\.01\]:hover{
  --tw-scale-x: 1.01;
  --tw-scale-y: 1.01;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-\[1\.02\]:hover{
  --tw-scale-x: 1.02;
  --tw-scale-y: 1.02;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:gap-3:hover{
  gap: 0.75rem;
}

.hover\:border-border\/60:hover{
  border-color: hsl(var(--border) / 0.6);
}

.hover\:border-info\/50:hover{
  border-color: hsl(var(--info) / 0.5);
}

.hover\:border-primary\/20:hover{
  border-color: hsl(var(--primary) / 0.2);
}

.hover\:border-primary\/30:hover{
  border-color: hsl(var(--primary) / 0.3);
}

.hover\:border-primary\/40:hover{
  border-color: hsl(var(--primary) / 0.4);
}

.hover\:border-primary\/80:hover{
  border-color: hsl(var(--primary) / 0.8);
}

.hover\:border-success\/50:hover{
  border-color: hsl(var(--success) / 0.5);
}

.hover\:bg-accent:hover{
  background-color: hsl(var(--accent));
}

.hover\:bg-accent\/80:hover{
  background-color: hsl(var(--accent) / 0.8);
}

.hover\:bg-background\/10:hover{
  background-color: hsl(var(--background) / 0.1);
}

.hover\:bg-background\/80:hover{
  background-color: hsl(var(--background) / 0.8);
}

.hover\:bg-background\/90:hover{
  background-color: hsl(var(--background) / 0.9);
}

.hover\:bg-card:hover{
  background-color: hsl(var(--card));
}

.hover\:bg-card\/90:hover{
  background-color: hsl(var(--card) / 0.9);
}

.hover\:bg-error\/90:hover{
  background-color: hsl(var(--error) / 0.9);
}

.hover\:bg-info\/80:hover{
  background-color: hsl(var(--info) / 0.8);
}

.hover\:bg-info\/90:hover{
  background-color: hsl(var(--info) / 0.9);
}

.hover\:bg-medical-blue\/90:hover{
  background-color: hsl(var(--medical-blue) / 0.9);
}

.hover\:bg-muted:hover{
  background-color: hsl(var(--muted));
}

.hover\:bg-muted-foreground\/50:hover{
  background-color: hsl(var(--muted-foreground) / 0.5);
}

.hover\:bg-muted\/50:hover{
  background-color: hsl(var(--muted) / 0.5);
}

.hover\:bg-muted\/70:hover{
  background-color: hsl(var(--muted) / 0.7);
}

.hover\:bg-muted\/80:hover{
  background-color: hsl(var(--muted) / 0.8);
}

.hover\:bg-orange-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(234 88 12 / var(--tw-bg-opacity, 1));
}

.hover\:bg-primary:hover{
  background-color: hsl(var(--primary));
}

.hover\:bg-primary\/10:hover{
  background-color: hsl(var(--primary) / 0.1);
}

.hover\:bg-primary\/5:hover{
  background-color: hsl(var(--primary) / 0.05);
}

.hover\:bg-primary\/80:hover{
  background-color: hsl(var(--primary) / 0.8);
}

.hover\:bg-primary\/90:hover{
  background-color: hsl(var(--primary) / 0.9);
}

.hover\:bg-secondary\/80:hover{
  background-color: hsl(var(--secondary) / 0.8);
}

.hover\:bg-success\/80:hover{
  background-color: hsl(var(--success) / 0.8);
}

.hover\:bg-success\/90:hover{
  background-color: hsl(var(--success) / 0.9);
}

.hover\:bg-warning\/90:hover{
  background-color: hsl(var(--warning) / 0.9);
}

.hover\:text-accent-foreground:hover{
  color: hsl(var(--accent-foreground));
}

.hover\:text-foreground:hover{
  color: hsl(var(--foreground));
}

.hover\:text-info:hover{
  color: hsl(var(--info));
}

.hover\:text-muted-foreground:hover{
  color: hsl(var(--muted-foreground));
}

.hover\:text-primary:hover{
  color: hsl(var(--primary));
}

.hover\:text-primary-foreground:hover{
  color: hsl(var(--primary-foreground));
}

.hover\:text-primary\/80:hover{
  color: hsl(var(--primary) / 0.8);
}

.hover\:underline:hover{
  text-decoration-line: underline;
}

.hover\:no-underline:hover{
  text-decoration-line: none;
}

.hover\:opacity-100:hover{
  opacity: 1;
}

.hover\:shadow-2xl:hover{
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-lg:hover{
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-md:hover{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-xl:hover{
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:not-sr-only:focus{
  position: static;
  width: auto;
  height: auto;
  padding: 0;
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

.focus\:absolute:focus{
  position: absolute;
}

.focus\:left-4:focus{
  left: 1rem;
}

.focus\:top-4:focus{
  top: 1rem;
}

.focus\:border-primary:focus{
  border-color: hsl(var(--primary));
}

.focus\:border-primary\/60:focus{
  border-color: hsl(var(--primary) / 0.6);
}

.focus\:bg-accent:focus{
  background-color: hsl(var(--accent));
}

.focus\:bg-primary:focus{
  background-color: hsl(var(--primary));
}

.focus\:text-accent-foreground:focus{
  color: hsl(var(--accent-foreground));
}

.focus\:text-primary-foreground:focus{
  color: hsl(var(--primary-foreground));
}

.focus\:outline-none:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-2:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-4:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-destructive:focus{
  --tw-ring-color: hsl(var(--destructive));
}

.focus\:ring-primary:focus{
  --tw-ring-color: hsl(var(--primary));
}

.focus\:ring-primary\/20:focus{
  --tw-ring-color: hsl(var(--primary) / 0.2);
}

.focus\:ring-ring:focus{
  --tw-ring-color: hsl(var(--ring));
}

.focus\:ring-offset-2:focus{
  --tw-ring-offset-width: 2px;
}

.focus-visible\:outline-none:focus-visible{
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-visible\:ring-1:focus-visible{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-2:focus-visible{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-destructive:focus-visible{
  --tw-ring-color: hsl(var(--destructive));
}

.focus-visible\:ring-ring:focus-visible{
  --tw-ring-color: hsl(var(--ring));
}

.focus-visible\:ring-offset-1:focus-visible{
  --tw-ring-offset-width: 1px;
}

.focus-visible\:ring-offset-2:focus-visible{
  --tw-ring-offset-width: 2px;
}

.focus-visible\:ring-offset-background:focus-visible{
  --tw-ring-offset-color: hsl(var(--background));
}

.disabled\:pointer-events-none:disabled{
  pointer-events: none;
}

.disabled\:cursor-not-allowed:disabled{
  cursor: not-allowed;
}

.disabled\:opacity-50:disabled{
  opacity: 0.5;
}

.group:hover .group-hover\:-translate-x-1{
  --tw-translate-x: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:translate-x-1{
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-105{
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-110{
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:bg-primary{
  background-color: hsl(var(--primary));
}

.group:hover .group-hover\:bg-primary\/20{
  background-color: hsl(var(--primary) / 0.2);
}

.group:hover .group-hover\:text-primary{
  color: hsl(var(--primary));
}

.group:hover .group-hover\:text-primary-foreground{
  color: hsl(var(--primary-foreground));
}

.group:hover .group-hover\:text-primary\/80{
  color: hsl(var(--primary) / 0.8);
}

.group:hover .group-hover\:opacity-100{
  opacity: 1;
}

.group.toaster .group-\[\.toaster\]\:border-border{
  border-color: hsl(var(--border));
}

.group.toast .group-\[\.toast\]\:bg-muted{
  background-color: hsl(var(--muted));
}

.group.toast .group-\[\.toast\]\:bg-primary{
  background-color: hsl(var(--primary));
}

.group.toaster .group-\[\.toaster\]\:bg-background{
  background-color: hsl(var(--background));
}

.group.toast .group-\[\.toast\]\:text-muted-foreground{
  color: hsl(var(--muted-foreground));
}

.group.toast .group-\[\.toast\]\:text-primary-foreground{
  color: hsl(var(--primary-foreground));
}

.group.toaster .group-\[\.toaster\]\:text-foreground{
  color: hsl(var(--foreground));
}

.group.toaster .group-\[\.toaster\]\:shadow-lg{
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.peer:disabled ~ .peer-disabled\:cursor-not-allowed{
  cursor: not-allowed;
}

.peer:disabled ~ .peer-disabled\:opacity-70{
  opacity: 0.7;
}

.has-\[\:disabled\]\:opacity-50:has(:disabled){
  opacity: 0.5;
}

.aria-selected\:bg-accent[aria-selected="true"]{
  background-color: hsl(var(--accent));
}

.aria-selected\:bg-accent\/50[aria-selected="true"]{
  background-color: hsl(var(--accent) / 0.5);
}

.aria-selected\:text-accent-foreground[aria-selected="true"]{
  color: hsl(var(--accent-foreground));
}

.aria-selected\:text-muted-foreground[aria-selected="true"]{
  color: hsl(var(--muted-foreground));
}

.aria-selected\:opacity-100[aria-selected="true"]{
  opacity: 1;
}

.aria-selected\:opacity-30[aria-selected="true"]{
  opacity: 0.3;
}

.data-\[disabled\=true\]\:pointer-events-none[data-disabled="true"]{
  pointer-events: none;
}

.data-\[disabled\]\:pointer-events-none[data-disabled]{
  pointer-events: none;
}

.data-\[panel-group-direction\=vertical\]\:h-px[data-panel-group-direction="vertical"]{
  height: 1px;
}

.data-\[panel-group-direction\=vertical\]\:w-full[data-panel-group-direction="vertical"]{
  width: 100%;
}

.data-\[side\=bottom\]\:translate-y-1[data-side="bottom"]{
  --tw-translate-y: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=left\]\:-translate-x-1[data-side="left"]{
  --tw-translate-x: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=right\]\:translate-x-1[data-side="right"]{
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=top\]\:-translate-y-1[data-side="top"]{
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[state\=checked\]\:translate-x-5[data-state="checked"]{
  --tw-translate-x: 1.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[state\=unchecked\]\:translate-x-0[data-state="unchecked"]{
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes accordion-up{
  from{
    height: var(--radix-accordion-content-height);
  }

  to{
    height: 0;
  }
}

.data-\[state\=closed\]\:animate-accordion-up[data-state="closed"]{
  animation: accordion-up 0.2s ease-out;
}

@keyframes accordion-down{
  from{
    height: 0;
  }

  to{
    height: var(--radix-accordion-content-height);
  }
}

.data-\[state\=open\]\:animate-accordion-down[data-state="open"]{
  animation: accordion-down 0.2s ease-out;
}

.data-\[panel-group-direction\=vertical\]\:flex-col[data-panel-group-direction="vertical"]{
  flex-direction: column;
}

.data-\[state\=checked\]\:border-primary[data-state="checked"]{
  border-color: hsl(var(--primary));
}

.data-\[active\]\:bg-accent\/50[data-active]{
  background-color: hsl(var(--accent) / 0.5);
}

.data-\[selected\=\'true\'\]\:bg-accent[data-selected='true']{
  background-color: hsl(var(--accent));
}

.data-\[state\=active\]\:bg-background[data-state="active"]{
  background-color: hsl(var(--background));
}

.data-\[state\=active\]\:bg-primary[data-state="active"]{
  background-color: hsl(var(--primary));
}

.data-\[state\=checked\]\:bg-primary[data-state="checked"]{
  background-color: hsl(var(--primary));
}

.data-\[state\=on\]\:bg-accent[data-state="on"]{
  background-color: hsl(var(--accent));
}

.data-\[state\=open\]\:bg-accent[data-state="open"]{
  background-color: hsl(var(--accent));
}

.data-\[state\=open\]\:bg-accent\/50[data-state="open"]{
  background-color: hsl(var(--accent) / 0.5);
}

.data-\[state\=open\]\:bg-secondary[data-state="open"]{
  background-color: hsl(var(--secondary));
}

.data-\[state\=selected\]\:bg-muted[data-state="selected"]{
  background-color: hsl(var(--muted));
}

.data-\[state\=unchecked\]\:bg-input[data-state="unchecked"]{
  background-color: hsl(var(--input));
}

.data-\[selected\=true\]\:text-accent-foreground[data-selected="true"]{
  color: hsl(var(--accent-foreground));
}

.data-\[state\=active\]\:text-foreground[data-state="active"]{
  color: hsl(var(--foreground));
}

.data-\[state\=checked\]\:text-primary-foreground[data-state="checked"]{
  color: hsl(var(--primary-foreground));
}

.data-\[state\=on\]\:text-accent-foreground[data-state="on"]{
  color: hsl(var(--accent-foreground));
}

.data-\[state\=open\]\:text-accent-foreground[data-state="open"]{
  color: hsl(var(--accent-foreground));
}

.data-\[state\=open\]\:text-muted-foreground[data-state="open"]{
  color: hsl(var(--muted-foreground));
}

.data-\[disabled\=true\]\:opacity-50[data-disabled="true"]{
  opacity: 0.5;
}

.data-\[disabled\]\:opacity-50[data-disabled]{
  opacity: 0.5;
}

.data-\[state\=active\]\:shadow-sm[data-state="active"]{
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.data-\[state\=closed\]\:duration-300[data-state="closed"]{
  transition-duration: 300ms;
}

.data-\[state\=open\]\:duration-500[data-state="open"]{
  transition-duration: 500ms;
}

.data-\[motion\^\=from-\]\:animate-in[data-motion^="from-"]{
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}

.data-\[state\=open\]\:animate-in[data-state="open"]{
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}

.data-\[state\=visible\]\:animate-in[data-state="visible"]{
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}

.data-\[motion\^\=to-\]\:animate-out[data-motion^="to-"]{
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}

.data-\[state\=closed\]\:animate-out[data-state="closed"]{
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}

.data-\[state\=hidden\]\:animate-out[data-state="hidden"]{
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}

.data-\[motion\^\=from-\]\:fade-in[data-motion^="from-"]{
  --tw-enter-opacity: 0;
}

.data-\[motion\^\=to-\]\:fade-out[data-motion^="to-"]{
  --tw-exit-opacity: 0;
}

.data-\[state\=closed\]\:fade-out-0[data-state="closed"]{
  --tw-exit-opacity: 0;
}

.data-\[state\=hidden\]\:fade-out[data-state="hidden"]{
  --tw-exit-opacity: 0;
}

.data-\[state\=open\]\:fade-in-0[data-state="open"]{
  --tw-enter-opacity: 0;
}

.data-\[state\=visible\]\:fade-in[data-state="visible"]{
  --tw-enter-opacity: 0;
}

.data-\[state\=closed\]\:zoom-out-95[data-state="closed"]{
  --tw-exit-scale: .95;
}

.data-\[state\=open\]\:zoom-in-90[data-state="open"]{
  --tw-enter-scale: .9;
}

.data-\[state\=open\]\:zoom-in-95[data-state="open"]{
  --tw-enter-scale: .95;
}

.data-\[state\=closed\]\:slide-out-to-bottom[data-state="closed"]{
  --tw-exit-translate-y: 100%;
}

.data-\[state\=closed\]\:slide-out-to-left[data-state="closed"]{
  --tw-exit-translate-x: -100%;
}

.data-\[state\=closed\]\:slide-out-to-right[data-state="closed"]{
  --tw-exit-translate-x: 100%;
}

.data-\[state\=closed\]\:slide-out-to-top[data-state="closed"]{
  --tw-exit-translate-y: -100%;
}

.data-\[state\=closed\]\:slide-out-to-top-\[48\%\][data-state="closed"]{
  --tw-exit-translate-y: -48%;
}

.data-\[state\=open\]\:slide-in-from-bottom[data-state="open"]{
  --tw-enter-translate-y: 100%;
}

.data-\[state\=open\]\:slide-in-from-left[data-state="open"]{
  --tw-enter-translate-x: -100%;
}

.data-\[state\=open\]\:slide-in-from-right[data-state="open"]{
  --tw-enter-translate-x: 100%;
}

.data-\[state\=open\]\:slide-in-from-top[data-state="open"]{
  --tw-enter-translate-y: -100%;
}

.data-\[state\=open\]\:slide-in-from-top-\[48\%\][data-state="open"]{
  --tw-enter-translate-y: -48%;
}

.data-\[state\=closed\]\:duration-300[data-state="closed"]{
  animation-duration: 300ms;
}

.data-\[state\=open\]\:duration-500[data-state="open"]{
  animation-duration: 500ms;
}

.data-\[panel-group-direction\=vertical\]\:after\:left-0[data-panel-group-direction="vertical"]::after{
  content: var(--tw-content);
  left: 0px;
}

.data-\[panel-group-direction\=vertical\]\:after\:h-1[data-panel-group-direction="vertical"]::after{
  content: var(--tw-content);
  height: 0.25rem;
}

.data-\[panel-group-direction\=vertical\]\:after\:w-full[data-panel-group-direction="vertical"]::after{
  content: var(--tw-content);
  width: 100%;
}

.data-\[panel-group-direction\=vertical\]\:after\:-translate-y-1\/2[data-panel-group-direction="vertical"]::after{
  content: var(--tw-content);
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[panel-group-direction\=vertical\]\:after\:translate-x-0[data-panel-group-direction="vertical"]::after{
  content: var(--tw-content);
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-state="open"] .group-data-\[state\=open\]\:rotate-180{
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.dark\:border-blue-400:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}

.dark\:border-blue-800:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(30 64 175 / var(--tw-border-opacity, 1));
}

.dark\:border-border:is(.dark *){
  border-color: hsl(var(--border));
}

.dark\:border-border\/50:is(.dark *){
  border-color: hsl(var(--border) / 0.5);
}

.dark\:border-border\/60:is(.dark *){
  border-color: hsl(var(--border) / 0.6);
}

.dark\:border-border\/70:is(.dark *){
  border-color: hsl(var(--border) / 0.7);
}

.dark\:border-cyan-400:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(34 211 238 / var(--tw-border-opacity, 1));
}

.dark\:border-green-400:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(74 222 128 / var(--tw-border-opacity, 1));
}

.dark\:border-green-800:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(22 101 52 / var(--tw-border-opacity, 1));
}

.dark\:border-indigo-400:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(129 140 248 / var(--tw-border-opacity, 1));
}

.dark\:border-info:is(.dark *){
  border-color: hsl(var(--info));
}

.dark\:border-info\/30:is(.dark *){
  border-color: hsl(var(--info) / 0.3);
}

.dark\:border-info\/50:is(.dark *){
  border-color: hsl(var(--info) / 0.5);
}

.dark\:border-pink-400:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(244 114 182 / var(--tw-border-opacity, 1));
}

.dark\:border-primary\/20:is(.dark *){
  border-color: hsl(var(--primary) / 0.2);
}

.dark\:border-primary\/30:is(.dark *){
  border-color: hsl(var(--primary) / 0.3);
}

.dark\:border-purple-400:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(192 132 252 / var(--tw-border-opacity, 1));
}

.dark\:border-purple-800:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(107 33 168 / var(--tw-border-opacity, 1));
}

.dark\:border-red-400:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1));
}

.dark\:border-red-800:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(153 27 27 / var(--tw-border-opacity, 1));
}

.dark\:border-success:is(.dark *){
  border-color: hsl(var(--success));
}

.dark\:border-success\/30:is(.dark *){
  border-color: hsl(var(--success) / 0.3);
}

.dark\:border-success\/50:is(.dark *){
  border-color: hsl(var(--success) / 0.5);
}

.dark\:bg-blue-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(30 58 138 / var(--tw-bg-opacity, 1));
}

.dark\:bg-blue-950\/20:is(.dark *){
  background-color: rgb(23 37 84 / 0.2);
}

.dark\:bg-blue-950\/30:is(.dark *){
  background-color: rgb(23 37 84 / 0.3);
}

.dark\:bg-cyan-950\/30:is(.dark *){
  background-color: rgb(8 51 68 / 0.3);
}

.dark\:bg-gray-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}

.dark\:bg-gray-950\/20:is(.dark *){
  background-color: rgb(3 7 18 / 0.2);
}

.dark\:bg-green-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(20 83 45 / var(--tw-bg-opacity, 1));
}

.dark\:bg-green-950\/30:is(.dark *){
  background-color: rgb(5 46 22 / 0.3);
}

.dark\:bg-indigo-950\/30:is(.dark *){
  background-color: rgb(30 27 75 / 0.3);
}

.dark\:bg-info:is(.dark *){
  background-color: hsl(var(--info));
}

.dark\:bg-info-light\/20:is(.dark *){
  background-color: hsl(var(--info-light) / 0.2);
}

.dark\:bg-info\/20:is(.dark *){
  background-color: hsl(var(--info) / 0.2);
}

.dark\:bg-info\/30:is(.dark *){
  background-color: hsl(var(--info) / 0.3);
}

.dark\:bg-info\/50:is(.dark *){
  background-color: hsl(var(--info) / 0.5);
}

.dark\:bg-medical-blue\/20:is(.dark *){
  background-color: hsl(var(--medical-blue) / 0.2);
}

.dark\:bg-muted:is(.dark *){
  background-color: hsl(var(--muted));
}

.dark\:bg-muted\/20:is(.dark *){
  background-color: hsl(var(--muted) / 0.2);
}

.dark\:bg-muted\/30:is(.dark *){
  background-color: hsl(var(--muted) / 0.3);
}

.dark\:bg-muted\/40:is(.dark *){
  background-color: hsl(var(--muted) / 0.4);
}

.dark\:bg-muted\/50:is(.dark *){
  background-color: hsl(var(--muted) / 0.5);
}

.dark\:bg-muted\/60:is(.dark *){
  background-color: hsl(var(--muted) / 0.6);
}

.dark\:bg-muted\/70:is(.dark *){
  background-color: hsl(var(--muted) / 0.7);
}

.dark\:bg-muted\/80:is(.dark *){
  background-color: hsl(var(--muted) / 0.8);
}

.dark\:bg-muted\/90:is(.dark *){
  background-color: hsl(var(--muted) / 0.9);
}

.dark\:bg-orange-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(124 45 18 / var(--tw-bg-opacity, 1));
}

.dark\:bg-orange-950\/20:is(.dark *){
  background-color: rgb(67 20 7 / 0.2);
}

.dark\:bg-pink-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(131 24 67 / var(--tw-bg-opacity, 1));
}

.dark\:bg-pink-950\/30:is(.dark *){
  background-color: rgb(80 7 36 / 0.3);
}

.dark\:bg-primary\/10:is(.dark *){
  background-color: hsl(var(--primary) / 0.1);
}

.dark\:bg-primary\/20:is(.dark *){
  background-color: hsl(var(--primary) / 0.2);
}

.dark\:bg-primary\/5:is(.dark *){
  background-color: hsl(var(--primary) / 0.05);
}

.dark\:bg-purple-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(88 28 135 / var(--tw-bg-opacity, 1));
}

.dark\:bg-purple-950\/30:is(.dark *){
  background-color: rgb(59 7 100 / 0.3);
}

.dark\:bg-red-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(127 29 29 / var(--tw-bg-opacity, 1));
}

.dark\:bg-red-950\/20:is(.dark *){
  background-color: rgb(69 10 10 / 0.2);
}

.dark\:bg-red-950\/30:is(.dark *){
  background-color: rgb(69 10 10 / 0.3);
}

.dark\:bg-success:is(.dark *){
  background-color: hsl(var(--success));
}

.dark\:bg-success-light\/20:is(.dark *){
  background-color: hsl(var(--success-light) / 0.2);
}

.dark\:bg-success\/20:is(.dark *){
  background-color: hsl(var(--success) / 0.2);
}

.dark\:bg-success\/30:is(.dark *){
  background-color: hsl(var(--success) / 0.3);
}

.dark\:bg-success\/50:is(.dark *){
  background-color: hsl(var(--success) / 0.5);
}

.dark\:bg-yellow-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(113 63 18 / var(--tw-bg-opacity, 1));
}

.dark\:from-blue-950\/30:is(.dark *){
  --tw-gradient-from: rgb(23 37 84 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(23 37 84 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-info-light\/20:is(.dark *){
  --tw-gradient-from: hsl(var(--info-light) / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--info-light) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-info-light\/30:is(.dark *){
  --tw-gradient-from: hsl(var(--info-light) / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--info-light) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-info-light\/50:is(.dark *){
  --tw-gradient-from: hsl(var(--info-light) / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--info-light) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-muted:is(.dark *){
  --tw-gradient-from: hsl(var(--muted)) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--muted) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-muted\/10:is(.dark *){
  --tw-gradient-from: hsl(var(--muted) / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--muted) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-muted\/80:is(.dark *){
  --tw-gradient-from: hsl(var(--muted) / 0.8) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--muted) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-primary\/10:is(.dark *){
  --tw-gradient-from: hsl(var(--primary) / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-primary\/20:is(.dark *){
  --tw-gradient-from: hsl(var(--primary) / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-purple-950\/30:is(.dark *){
  --tw-gradient-from: rgb(59 7 100 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 7 100 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:via-background:is(.dark *){
  --tw-gradient-to: hsl(var(--background) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--background)) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.dark\:via-info-light\/10:is(.dark *){
  --tw-gradient-to: hsl(var(--info-light) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--info-light) / 0.1) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.dark\:via-info-light\/20:is(.dark *){
  --tw-gradient-to: hsl(var(--info-light) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--info-light) / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.dark\:via-muted:is(.dark *){
  --tw-gradient-to: hsl(var(--muted) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--muted)) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.dark\:via-muted\/90:is(.dark *){
  --tw-gradient-to: hsl(var(--muted) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--muted) / 0.9) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.dark\:to-background:is(.dark *){
  --tw-gradient-to: hsl(var(--background)) var(--tw-gradient-to-position);
}

.dark\:to-indigo-950\/30:is(.dark *){
  --tw-gradient-to: rgb(30 27 75 / 0.3) var(--tw-gradient-to-position);
}

.dark\:to-info-light\/20:is(.dark *){
  --tw-gradient-to: hsl(var(--info-light) / 0.2) var(--tw-gradient-to-position);
}

.dark\:to-info-light\/30:is(.dark *){
  --tw-gradient-to: hsl(var(--info-light) / 0.3) var(--tw-gradient-to-position);
}

.dark\:to-muted:is(.dark *){
  --tw-gradient-to: hsl(var(--muted)) var(--tw-gradient-to-position);
}

.dark\:to-muted\/80:is(.dark *){
  --tw-gradient-to: hsl(var(--muted) / 0.8) var(--tw-gradient-to-position);
}

.dark\:to-pink-950\/30:is(.dark *){
  --tw-gradient-to: rgb(80 7 36 / 0.3) var(--tw-gradient-to-position);
}

.dark\:to-primary\/20:is(.dark *){
  --tw-gradient-to: hsl(var(--primary) / 0.2) var(--tw-gradient-to-position);
}

.dark\:fill-background:is(.dark *){
  fill: hsl(var(--background));
}

.dark\:text-blue-100:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(219 234 254 / var(--tw-text-opacity, 1));
}

.dark\:text-blue-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity, 1));
}

.dark\:text-blue-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}

.dark\:text-cyan-100:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(207 250 254 / var(--tw-text-opacity, 1));
}

.dark\:text-foreground:is(.dark *){
  color: hsl(var(--foreground));
}

.dark\:text-gray-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}

.dark\:text-green-100:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(220 252 231 / var(--tw-text-opacity, 1));
}

.dark\:text-green-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(187 247 208 / var(--tw-text-opacity, 1));
}

.dark\:text-green-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}

.dark\:text-indigo-100:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(224 231 255 / var(--tw-text-opacity, 1));
}

.dark\:text-info:is(.dark *){
  color: hsl(var(--info));
}

.dark\:text-medical-blue:is(.dark *){
  color: hsl(var(--medical-blue));
}

.dark\:text-muted-foreground:is(.dark *){
  color: hsl(var(--muted-foreground));
}

.dark\:text-orange-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(254 215 170 / var(--tw-text-opacity, 1));
}

.dark\:text-pink-100:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(252 231 243 / var(--tw-text-opacity, 1));
}

.dark\:text-pink-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(251 207 232 / var(--tw-text-opacity, 1));
}

.dark\:text-primary-foreground:is(.dark *){
  color: hsl(var(--primary-foreground));
}

.dark\:text-purple-100:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(243 232 255 / var(--tw-text-opacity, 1));
}

.dark\:text-purple-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(233 213 255 / var(--tw-text-opacity, 1));
}

.dark\:text-purple-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(192 132 252 / var(--tw-text-opacity, 1));
}

.dark\:text-red-100:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(254 226 226 / var(--tw-text-opacity, 1));
}

.dark\:text-red-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(254 202 202 / var(--tw-text-opacity, 1));
}

.dark\:text-red-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}

.dark\:text-success:is(.dark *){
  color: hsl(var(--success));
}

.dark\:text-yellow-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(254 240 138 / var(--tw-text-opacity, 1));
}

.dark\:opacity-10:is(.dark *){
  opacity: 0.1;
}

.dark\:shadow-slate-900\/60:is(.dark *){
  --tw-shadow-color: rgb(15 23 42 / 0.6);
  --tw-shadow: var(--tw-shadow-colored);
}

.dark\:hover\:border-border\/60:hover:is(.dark *){
  border-color: hsl(var(--border) / 0.6);
}

.dark\:hover\:bg-muted:hover:is(.dark *){
  background-color: hsl(var(--muted));
}

.dark\:hover\:bg-muted\/80:hover:is(.dark *){
  background-color: hsl(var(--muted) / 0.8);
}

.dark\:hover\:bg-muted\/90:hover:is(.dark *){
  background-color: hsl(var(--muted) / 0.9);
}

@media (min-width: 640px){
  .sm\:mt-0{
    margin-top: 0px;
  }

  .sm\:inline{
    display: inline;
  }

  .sm\:w-auto{
    width: auto;
  }

  .sm\:max-w-sm{
    max-width: 24rem;
  }

  .sm\:flex-none{
    flex: none;
  }

  .sm\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:flex-row{
    flex-direction: row;
  }

  .sm\:justify-end{
    justify-content: flex-end;
  }

  .sm\:gap-2\.5{
    gap: 0.625rem;
  }

  .sm\:space-x-2 > :not([hidden]) ~ :not([hidden]){
    --tw-space-x-reverse: 0;
    margin-right: calc(0.5rem * var(--tw-space-x-reverse));
    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-x-4 > :not([hidden]) ~ :not([hidden]){
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-y-0 > :not([hidden]) ~ :not([hidden]){
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .sm\:rounded-lg{
    border-radius: var(--radius);
  }

  .sm\:px-6{
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:text-left{
    text-align: left;
  }
}

@media (min-width: 768px){
  .md\:absolute{
    position: absolute;
  }

  .md\:col-span-2{
    grid-column: span 2 / span 2;
  }

  .md\:h-12{
    height: 3rem;
  }

  .md\:h-5{
    height: 1.25rem;
  }

  .md\:h-80{
    height: 20rem;
  }

  .md\:h-\[300px\]{
    height: 300px;
  }

  .md\:w-1\/2{
    width: 50%;
  }

  .md\:w-5{
    width: 1.25rem;
  }

  .md\:w-\[var\(--radix-navigation-menu-viewport-width\)\]{
    width: var(--radix-navigation-menu-viewport-width);
  }

  .md\:w-auto{
    width: auto;
  }

  .md\:grid-cols-1{
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .md\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4{
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:flex-row{
    flex-direction: row;
  }

  .md\:items-start{
    align-items: flex-start;
  }

  .md\:gap-12{
    gap: 3rem;
  }

  .md\:p-10{
    padding: 2.5rem;
  }

  .md\:p-12{
    padding: 3rem;
  }

  .md\:py-24{
    padding-top: 6rem;
    padding-bottom: 6rem;
  }

  .md\:text-left{
    text-align: left;
  }

  .md\:text-2xl{
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .md\:text-4xl{
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .md\:text-5xl{
    font-size: 3rem;
    line-height: 1;
  }

  .md\:text-6xl{
    font-size: 3.75rem;
    line-height: 1;
  }

  .md\:text-base{
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .md\:text-lg{
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .md\:text-xl{
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1024px){
  .lg\:sticky{
    position: sticky;
  }

  .lg\:top-8{
    top: 2rem;
  }

  .lg\:order-1{
    order: 1;
  }

  .lg\:order-2{
    order: 2;
  }

  .lg\:col-span-1{
    grid-column: span 1 / span 1;
  }

  .lg\:col-span-2{
    grid-column: span 2 / span 2;
  }

  .lg\:col-span-3{
    grid-column: span 3 / span 3;
  }

  .lg\:mb-0{
    margin-bottom: 0px;
  }

  .lg\:ml-8{
    margin-left: 2rem;
  }

  .lg\:flex{
    display: flex;
  }

  .lg\:hidden{
    display: none;
  }

  .lg\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4{
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:grid-cols-5{
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .lg\:grid-cols-6{
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .lg\:flex-row{
    flex-direction: row;
  }

  .lg\:items-center{
    align-items: center;
  }

  .lg\:justify-between{
    justify-content: space-between;
  }

  .lg\:gap-10{
    gap: 2.5rem;
  }

  .lg\:text-2xl{
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .lg\:text-3xl{
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .lg\:text-4xl{
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .lg\:text-5xl{
    font-size: 3rem;
    line-height: 1;
  }

  .lg\:text-6xl{
    font-size: 3.75rem;
    line-height: 1;
  }

  .lg\:text-7xl{
    font-size: 4.5rem;
    line-height: 1;
  }

  .lg\:text-base{
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

@media (min-width: 1280px){
  .xl\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .xl\:grid-cols-4{
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

@media (max-width: 639px){
  .mobile\:min-h-\[140px\]{
    min-height: 140px;
  }

  .mobile\:min-h-\[44px\]{
    min-height: 44px;
  }

  .mobile\:min-h-\[48px\]{
    min-height: 48px;
  }

  .mobile\:min-h-\[52px\]{
    min-height: 52px;
  }

  .mobile\:min-h-\[56px\]{
    min-height: 56px;
  }

  .mobile\:min-w-\[44px\]{
    min-width: 44px;
  }

  .mobile\:min-w-\[48px\]{
    min-width: 48px;
  }

  .mobile\:min-w-\[52px\]{
    min-width: 52px;
  }

  .mobile\:py-2{
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }

  .mobile\:text-base{
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .mobile\:text-lg{
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .mobile\:text-sm{
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}

.\[\&\:has\(\[aria-selected\]\)\]\:bg-accent:has([aria-selected]){
  background-color: hsl(var(--accent));
}

.first\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-l-md:has([aria-selected]):first-child{
  border-top-left-radius: calc(var(--radius) - 2px);
  border-bottom-left-radius: calc(var(--radius) - 2px);
}

.last\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-r-md:has([aria-selected]):last-child{
  border-top-right-radius: calc(var(--radius) - 2px);
  border-bottom-right-radius: calc(var(--radius) - 2px);
}

.\[\&\:has\(\[aria-selected\]\.day-outside\)\]\:bg-accent\/50:has([aria-selected].day-outside){
  background-color: hsl(var(--accent) / 0.5);
}

.\[\&\:has\(\[aria-selected\]\.day-range-end\)\]\:rounded-r-md:has([aria-selected].day-range-end){
  border-top-right-radius: calc(var(--radius) - 2px);
  border-bottom-right-radius: calc(var(--radius) - 2px);
}

.\[\&\:has\(\[role\=checkbox\]\)\]\:pr-0:has([role=checkbox]){
  padding-right: 0px;
}

.\[\&\>\*\]\:text-accent-foreground>*{
  color: hsl(var(--accent-foreground));
}

.\[\&\>\*\]\:text-error-foreground>*{
  color: hsl(var(--error-foreground));
}

.\[\&\>\*\]\:text-foreground>*{
  color: hsl(var(--foreground));
}

.\[\&\>\*\]\:text-info-foreground>*{
  color: hsl(var(--info-foreground));
}

.\[\&\>\*\]\:text-medical-blue-foreground>*{
  color: hsl(var(--medical-blue-foreground));
}

.\[\&\>\*\]\:text-muted-foreground>*{
  color: hsl(var(--muted-foreground));
}

.\[\&\>\*\]\:text-primary>*{
  color: hsl(var(--primary));
}

.\[\&\>\*\]\:text-primary-foreground>*{
  color: hsl(var(--primary-foreground));
}

.\[\&\>\*\]\:text-secondary-foreground>*{
  color: hsl(var(--secondary-foreground));
}

.\[\&\>\*\]\:text-success-foreground>*{
  color: hsl(var(--success-foreground));
}

.\[\&\>\*\]\:text-warning-foreground>*{
  color: hsl(var(--warning-foreground));
}

.\[\&\>\*\]\:opacity-100>*{
  opacity: 1;
}

.hover\:\[\&\>\*\]\:text-accent-foreground>*:hover{
  color: hsl(var(--accent-foreground));
}

.hover\:\[\&\>\*\]\:text-primary-foreground>*:hover{
  color: hsl(var(--primary-foreground));
}

.\[\&\>span\]\:line-clamp-1>span{
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.\[\&\>svg\+div\]\:translate-y-\[-3px\]>svg+div{
  --tw-translate-y: -3px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.\[\&\>svg\]\:absolute>svg{
  position: absolute;
}

.\[\&\>svg\]\:left-4>svg{
  left: 1rem;
}

.\[\&\>svg\]\:top-4>svg{
  top: 1rem;
}

.\[\&\>svg\]\:size-3\.5>svg{
  width: 0.875rem;
  height: 0.875rem;
}

.\[\&\>svg\]\:text-foreground>svg{
  color: hsl(var(--foreground));
}

.\[\&\>svg\~\*\]\:pl-7>svg~*{
  padding-left: 1.75rem;
}

.\[\&\>tr\]\:last\:border-b-0:last-child>tr{
  border-bottom-width: 0px;
}

.\[\&\[data-panel-group-direction\=vertical\]\>div\]\:rotate-90[data-panel-group-direction=vertical]>div{
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.\[\&\[data-state\=open\]\>svg\]\:rotate-180[data-state=open]>svg{
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.\[\&\[data-state\=open\]\]\:border-b[data-state=open]{
  border-bottom-width: 1px;
}

.\[\&\[data-state\=open\]\]\:border-border\/50[data-state=open]{
  border-color: hsl(var(--border) / 0.5);
}

.\[\&\[data-state\=open\]\]\:text-primary[data-state=open]{
  color: hsl(var(--primary));
}

.\[\&_\[cmdk-group-heading\]\]\:px-2 [cmdk-group-heading]{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.\[\&_\[cmdk-group-heading\]\]\:py-1\.5 [cmdk-group-heading]{
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.\[\&_\[cmdk-group-heading\]\]\:text-xs [cmdk-group-heading]{
  font-size: 0.75rem;
  line-height: 1rem;
}

.\[\&_\[cmdk-group-heading\]\]\:font-medium [cmdk-group-heading]{
  font-weight: 500;
}

.\[\&_\[cmdk-group-heading\]\]\:text-muted-foreground [cmdk-group-heading]{
  color: hsl(var(--muted-foreground));
}

.\[\&_\[cmdk-group\]\:not\(\[hidden\]\)_\~\[cmdk-group\]\]\:pt-0 [cmdk-group]:not([hidden]) ~[cmdk-group]{
  padding-top: 0px;
}

.\[\&_\[cmdk-group\]\]\:px-2 [cmdk-group]{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.\[\&_\[cmdk-input-wrapper\]_svg\]\:h-5 [cmdk-input-wrapper] svg{
  height: 1.25rem;
}

.\[\&_\[cmdk-input-wrapper\]_svg\]\:w-5 [cmdk-input-wrapper] svg{
  width: 1.25rem;
}

.\[\&_\[cmdk-input\]\]\:h-12 [cmdk-input]{
  height: 3rem;
}

.\[\&_\[cmdk-item\]\]\:px-2 [cmdk-item]{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.\[\&_\[cmdk-item\]\]\:py-3 [cmdk-item]{
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.\[\&_\[cmdk-item\]_svg\]\:h-5 [cmdk-item] svg{
  height: 1.25rem;
}

.\[\&_\[cmdk-item\]_svg\]\:w-5 [cmdk-item] svg{
  width: 1.25rem;
}

.\[\&_p\]\:leading-relaxed p{
  line-height: 1.625;
}

.\[\&_svg\]\:pointer-events-none svg{
  pointer-events: none;
}

.\[\&_svg\]\:size-4 svg{
  width: 1rem;
  height: 1rem;
}

.\[\&_svg\]\:shrink-0 svg{
  flex-shrink: 0;
}

.\[\&_tr\:last-child\]\:border-0 tr:last-child{
  border-width: 0px;
}

.\[\&_tr\]\:border-b tr{
  border-bottom-width: 1px;
}
