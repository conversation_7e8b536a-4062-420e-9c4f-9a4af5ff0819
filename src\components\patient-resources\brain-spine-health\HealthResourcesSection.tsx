import { <PERSON>, Lock, BookOpen, ArrowRight } from 'lucide-react';
import React, { useState } from 'react';
import { Link } from 'react-router-dom';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { TabsContent } from '@/components/ui/tabs';

interface HealthResource {
  id: string;
  name: string;
  description: string;
  type: 'assessment' | 'education' | 'program' | 'anatomy' | 'recovery';
  requiresLogin: boolean;
  path: string;
  category: string;
  featured: boolean;
}

interface HealthResourcesSectionProps {
  resources: HealthResource[];
}

const HealthResourcesSection: React.FC<HealthResourcesSectionProps> = ({ resources }) => {
  const [searchTerm, setSearchTerm] = useState('');

  const filteredResources = resources.filter(resource =>
    resource.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    resource.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    resource.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'assessment': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'education': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'program': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'anatomy': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'recovery': return 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const featuredResources = filteredResources.filter(resource => resource.featured);
  const regularResources = filteredResources.filter(resource => !resource.featured);

  return (
    <TabsContent value="resources" className="mt-0 space-y-16">
      <div className="text-center space-y-6">
        <h3 className="text-3xl md:text-4xl font-bold text-foreground tracking-tight">
          Health Resources & Tools
        </h3>
        <p className="text-lg md:text-xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
          Access comprehensive resources to support your brain and spine health journey,
          from educational materials to interactive assessment tools.
        </p>
        <div className="w-16 h-1 bg-gradient-to-r from-primary to-primary/50 mx-auto rounded-full"></div>
      </div>

      {/* Enhanced Search Bar */}
      <div className="max-w-2xl mx-auto">
        <div className="relative group">
          <Search className="absolute left-5 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground group-focus-within:text-primary transition-colors duration-200" />
          <Input
            type="text"
            placeholder="Search health resources and tools..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-14 pr-6 py-4 text-base border-2 border-border/40 rounded-2xl focus:border-primary/60 focus:ring-4 focus:ring-primary/10 transition-all duration-300 bg-card/50 backdrop-blur-sm shadow-sm hover:shadow-md"
            aria-label="Search health resources and tools"
          />
          <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-primary/5 to-primary/10 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 -z-10"></div>
        </div>
      </div>

      {/* Featured Resources */}
      {featuredResources.length > 0 && (
        <section className="space-y-10" aria-labelledby="featured-resources-heading">
          <div className="text-center space-y-4">
            <h4 id="featured-resources-heading" className="text-2xl md:text-3xl font-bold text-primary">
              Featured Resources
            </h4>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Curated selection of our most valuable health resources and tools
            </p>
          </div>
          <div className="grid gap-8 sm:grid-cols-1 md:grid-cols-2">
            {featuredResources.map((resource) => (
              <Card key={resource.id} className="group relative overflow-hidden border-2 border-primary/20 bg-gradient-to-br from-primary/5 via-card to-primary/10 hover:shadow-2xl hover:border-primary/40 transition-all duration-500 hover:scale-[1.02] hover:-translate-y-1">
                <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-primary/10 to-transparent rounded-bl-full"></div>
                <CardHeader className="relative pb-6">
                  <div className="flex items-start justify-between mb-6">
                    <div className="space-y-4">
                      <Badge className={`${getTypeColor(resource.type)} font-semibold px-3 py-1.5 text-sm`}>
                        {resource.type}
                      </Badge>
                      <CardTitle className="text-xl md:text-2xl font-bold text-primary leading-tight">
                        {resource.name}
                      </CardTitle>
                    </div>
                    {resource.requiresLogin && (
                      <div className="p-3 rounded-full bg-muted/30 border border-border/50">
                        <Lock className="h-5 w-5 text-muted-foreground" />
                      </div>
                    )}
                  </div>
                  <CardDescription className="text-base text-muted-foreground leading-relaxed">
                    {resource.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="flex items-center justify-between gap-4">
                    <span className="text-sm font-medium text-muted-foreground bg-background/60 px-4 py-2 rounded-full border border-border/30">
                      {resource.category}
                    </span>
                    <Button asChild variant="default" size="lg" className="font-semibold shadow-lg hover:shadow-xl transition-all duration-300">
                      <Link to={resource.path} className="flex items-center gap-3">
                        <BookOpen className="h-5 w-5" />
                        Access Resource
                        <ArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
                      </Link>
                    </Button>
                  </div>
                </CardContent>
                </Card>
              ))}
            </div>
          </section>
        )}

      {/* Regular Resources */}
      {regularResources.length > 0 && (
        <section className="space-y-10" aria-labelledby="all-resources-heading">
          <div className="text-center space-y-4">
            <h4 id="all-resources-heading" className="text-2xl md:text-3xl font-bold text-foreground">
              All Resources
            </h4>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Comprehensive collection of health resources and educational materials
            </p>
          </div>
          <div className="grid gap-8 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
            {regularResources.map((resource) => (
              <Card key={resource.id} className="group h-full bg-card/80 backdrop-blur-sm border border-border/50 hover:border-border hover:shadow-xl transition-all duration-500 hover:scale-[1.02] hover:-translate-y-1">
                <CardHeader className="pb-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="space-y-3 flex-1">
                      <Badge className={`${getTypeColor(resource.type)} font-medium px-3 py-1.5`}>
                        {resource.type}
                      </Badge>
                      <CardTitle className="text-lg md:text-xl font-bold text-foreground leading-tight group-hover:text-primary transition-colors duration-300">
                        {resource.name}
                      </CardTitle>
                    </div>
                    {resource.requiresLogin && (
                      <div className="p-2.5 rounded-full bg-muted/40 border border-border/30">
                        <Lock className="h-4 w-4 text-muted-foreground" />
                      </div>
                    )}
                  </div>
                  <CardDescription className="text-muted-foreground leading-relaxed">
                    {resource.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-0 mt-auto">
                  <div className="flex items-center justify-between gap-3">
                    <span className="text-sm font-medium text-muted-foreground bg-muted/40 px-3 py-2 rounded-full border border-border/20">
                      {resource.category}
                    </span>
                    <Button asChild variant="outline" size="sm" className="font-medium hover:bg-primary hover:text-primary-foreground transition-all duration-300">
                      <Link to={resource.path} className="flex items-center gap-2">
                        <BookOpen className="h-4 w-4" />
                        Access
                        <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                      </Link>
                    </Button>
                  </div>
                </CardContent>
                </Card>
              ))}
            </div>
          </section>
        )}

      {filteredResources.length === 0 && (
        <div className="text-center py-20">
          <div className="max-w-lg mx-auto space-y-6">
            <div className="relative">
              <div className="p-6 rounded-full bg-gradient-to-br from-muted/40 to-muted/20 w-fit mx-auto border border-border/30">
                <Search className="h-12 w-12 text-muted-foreground" />
              </div>
              <div className="absolute inset-0 rounded-full bg-gradient-to-r from-primary/10 to-primary/5 blur-xl"></div>
            </div>
            <div className="space-y-3">
              <h4 className="text-xl md:text-2xl font-bold text-foreground">No resources found</h4>
              <p className="text-muted-foreground leading-relaxed">
                No resources found matching your search criteria. Try adjusting your search terms or browse all available resources.
              </p>
            </div>
            <Button
              variant="outline"
              onClick={() => setSearchTerm('')}
              className="mt-6 font-medium"
            >
              Clear Search
            </Button>
          </div>
        </div>
      )}
    </TabsContent>
  );
};

export default HealthResourcesSection;
