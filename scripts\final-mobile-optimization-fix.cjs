const fs = require('fs');
const path = require('path');

// Function to remove all extra closing braces in mobile-optimization.ts
function fixMobileOptimization() {
  const filePath = path.join(process.cwd(), 'src/lib/mobile-optimization.ts');
  
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Split content into lines for analysis
    const lines = content.split('\n');
    const cleanedLines = [];
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const nextLine = i < lines.length - 1 ? lines[i + 1] : '';
      const lineAfterNext = i < lines.length - 2 ? lines[i + 2] : '';
      const lineAfterThat = i < lines.length - 3 ? lines[i + 3] : '';
      
      // Pattern: "  }\n  }\n\n\n  /**" - extra closing brace before method
      if (line.trim() === '}' && 
          nextLine.trim() === '}' && 
          lineAfterNext.trim() === '' &&
          lineAfterThat.trim() === '' &&
          i + 4 < lines.length &&
          lines[i + 4].trim().startsWith('/**')) {
        
        // Keep the first closing brace, skip the extra one
        cleanedLines.push(line);
        // Skip the extra brace (nextLine)
        modified = true;
        console.log(`Removed extra closing brace at line ${i + 2}`);
        continue;
      }
      
      cleanedLines.push(line);
    }
    
    if (modified) {
      const newContent = cleanedLines.join('\n');
      fs.writeFileSync(filePath, newContent, 'utf8');
      console.log(`✅ Fixed mobile-optimization.ts - removed ${modified} extra braces`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing mobile-optimization.ts:`, error.message);
    return false;
  }
}

// Main execution
console.log('🔧 Final fix for mobile-optimization.ts...');

const fixed = fixMobileOptimization();

if (fixed) {
  console.log('\n🎯 Next steps:');
  console.log('1. Run npm run build to test the build process');
} else {
  console.log('\n✅ No extra braces found in mobile-optimization.ts');
}
