import React from 'react';

import {
  MinimallyInvasiveIcon,
  ImagingNavigationIcon,
  SurgicalAdvantagesIcon,
  RoboticSurgeryIcon
} from '@/components/shared/MedicalIcons';
import { DeviceInfo } from '@/contexts/DeviceContext';
import type { ServicesOverviewProps } from '@/types/homepage';

// Removed unused imports - these components are not used in this file

/**
 * Services Overview Component
 * Displays the comprehensive services overview section with technology cards
 * Preserves all original content and styling from Index.tsx lines 137-437
 * Now uses Foundation Phase shared components for consistency
 */
const ServicesOverview: React.FC<ServicesOverviewProps> = ({ deviceInfo, services }) => {
  return (
    <section className="section-spacing section-background-muted">
      <div className="section-container">
        <div className="section-header-spacing text-center">
          <span className="text-primary font-semibold uppercase tracking-wider text-sm border-b-2 border-primary/30 pb-2 inline-block mb-6">
            Advanced Technology
          </span>
          <h2 className="text-headline mb-6">
            Cutting-Edge Neurosurgical Technologies
          </h2>
          <p className="lead max-w-4xl mx-auto">
            Medical technology advances facilitated less invasive procedures and treatment of brain and spine lesions previously considered inoperable due to their size or critical location. This approach provides many benefits for our patients, including minimally-invasive access, increased accuracy, reduced surgery time, faster recovery times, and earlier return to normal activities.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {services.map((service) => (
            <ServiceCard
              key={service.id}
              service={service}
              deviceInfo={deviceInfo}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

/**
 * Individual Service Card Component
 * Renders each service with its icon, title, description, and link
 * Now uses Foundation Phase shared components for consistency
 */
interface ServiceCardProps {
  service: {
    id: string;
    title: string;
    description: string;
    icon: string;
    link: string;
    animationDelay: string;
    gradientColors: string;
  };
  deviceInfo?: DeviceInfo; // Optional since it's not used in the component
}

const ServiceCard: React.FC<ServiceCardProps> = ({ service, deviceInfo: _deviceInfo }) => {
  const renderIcon = (iconType: string) => {
    const iconClassName = "w-14 h-14 text-primary drop-shadow-sm transition-all duration-300";

    switch (iconType) {
      case 'minimal-invasive':
        return <MinimallyInvasiveIcon className={iconClassName} />;

      case 'imaging-navigation':
        return <ImagingNavigationIcon className={iconClassName} />;

      case 'advantages':
        return <SurgicalAdvantagesIcon className={iconClassName} />;

      case 'robotic-surgery':
        return <RoboticSurgeryIcon className={iconClassName} />;

      default:
        return null;
    }
  };

  return (
    <div
      className="medical-card-feature h-full flex flex-col animate-fade-in"
      style={{ animationDelay: service.animationDelay }}
    >
      <div className="flex justify-center mb-6">
        <div className="w-20 h-20 rounded-2xl bg-primary/10 border border-primary/20 flex items-center justify-center shadow-lg backdrop-blur-sm transition-all duration-300 hover:shadow-xl hover:scale-110">
          {renderIcon(service.icon)}
        </div>
      </div>

      <div className="flex-1 text-center space-y-4">
        <h3 className="text-title text-xl font-semibold">
          {service.title}
        </h3>
        <p className="text-foreground/80 leading-relaxed">
          {service.description}
        </p>
      </div>

      {service.link && (
        <div className="mt-6 text-center">
          <a
            href={service.link}
            className="text-primary font-semibold hover:text-primary/80 transition-colors duration-200 inline-flex items-center"
          >
            Learn More
            <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </a>
        </div>
      )}
    </div>
  );
};

ServicesOverview.displayName = 'ServicesOverview';

export default ServicesOverview;
