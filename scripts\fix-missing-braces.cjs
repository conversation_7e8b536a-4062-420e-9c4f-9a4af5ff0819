const fs = require('fs');
const path = require('path');

// Function to recursively find all TypeScript and TSX files
function findTsFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      if (!['node_modules', '.git', 'dist', 'build'].includes(file)) {
        findTsFiles(filePath, fileList);
      }
    } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// Function to fix missing closing braces in a file
function fixMissingBraces(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Pattern to detect missing closing brace before method declaration
    // Look for: } followed by comment block followed by method declaration without proper closing
    const missingBracePattern = /(\s*}\s*)\n(\s*\/\*\*[\s\S]*?\*\/\s*)\n(\s*(?:private|public|protected)?\s*\w+\s*\([^)]*\)\s*:\s*\w+)/g;
    
    if (missingBracePattern.test(content)) {
      content = content.replace(missingBracePattern, '$1\n  }\n\n$2\n$3');
      modified = true;
      console.log(`Fixed missing closing brace before method in: ${filePath}`);
    }
    
    // Pattern to detect missing closing brace before function declaration
    const missingBraceFunctionPattern = /(\s*}\s*)\n(\s*\/\*\*[\s\S]*?\*\/\s*)\n(\s*(?:export\s+)?function\s+\w+)/g;
    
    if (missingBraceFunctionPattern.test(content)) {
      content = content.replace(missingBraceFunctionPattern, '$1\n  }\n\n$2\n$3');
      modified = true;
      console.log(`Fixed missing closing brace before function in: ${filePath}`);
    }
    
    // Pattern to detect missing closing brace at end of try-catch blocks
    const missingTryBracePattern = /(\s*}\s*catch\s*\([^)]*\)\s*{[\s\S]*?}\s*)\n(\s*\/\*\*[\s\S]*?\*\/\s*)\n(\s*(?:private|public|protected)?\s*\w+)/g;
    
    if (missingTryBracePattern.test(content)) {
      content = content.replace(missingTryBracePattern, '$1\n  }\n\n$2\n$3');
      modified = true;
      console.log(`Fixed missing closing brace after try-catch in: ${filePath}`);
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Main execution
console.log('🔧 Fixing missing closing braces...');

const srcDir = path.join(process.cwd(), 'src');
const tsFiles = findTsFiles(srcDir);

let fixedCount = 0;
let totalFiles = tsFiles.length;

tsFiles.forEach(filePath => {
  if (fixMissingBraces(filePath)) {
    fixedCount++;
  }
});

console.log(`\n✅ Processed ${totalFiles} files`);
console.log(`🔧 Fixed missing braces in ${fixedCount} files`);

if (fixedCount > 0) {
  console.log('\n🎯 Next steps:');
  console.log('1. Run npm run build to test the build process');
  console.log('2. Run npm run type-check to verify TypeScript compilation');
}
