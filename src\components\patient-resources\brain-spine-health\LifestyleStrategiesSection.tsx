import { Microscope, Activity } from 'lucide-react';
import React from 'react';

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { TabsContent } from '@/components/ui/tabs';

interface LifestyleCategory {
  title: string;
  description: string;
  tips: string[];
  icon: React.ComponentType<{ className?: string }>;
}

interface LifestyleStrategiesSectionProps {
  categories: LifestyleCategory[];
}

const LifestyleStrategiesSection: React.FC<LifestyleStrategiesSectionProps> = ({ categories }) => {
  return (
    <TabsContent value="lifestyle" className="mt-0 space-y-16">
      <div className="text-center space-y-6">
        <h3 className="text-3xl md:text-4xl font-bold text-foreground tracking-tight">
          Lifestyle Strategies for Optimal Health
        </h3>
        <p className="text-lg md:text-xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
          Evidence-based lifestyle modifications that support neurological health,
          prevent complications, and enhance recovery outcomes.
        </p>
        <div className="w-16 h-1 bg-gradient-to-r from-primary to-primary/50 mx-auto rounded-full"></div>
      </div>

      <div className="space-y-16">
        {/* Main Lifestyle Categories */}
        <div className="grid gap-8 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
        {categories.map((category, index) => {
          const Icon = category.icon;
          const colors = [
            {
              bg: 'bg-gradient-to-br from-red-50 to-red-100/50 dark:from-red-950/30 dark:to-red-900/20',
              border: 'border-red-200/60 dark:border-red-800/60',
              icon: 'text-red-600 dark:text-red-400',
              title: 'text-red-900 dark:text-red-100',
              accent: 'border-red-500 dark:border-red-400',
              text: 'text-muted-foreground',
              glow: 'shadow-red-500/20'
            },
            {
              bg: 'bg-gradient-to-br from-blue-50 to-blue-100/50 dark:from-blue-950/30 dark:to-blue-900/20',
              border: 'border-blue-200/60 dark:border-blue-800/60',
              icon: 'text-blue-600 dark:text-blue-400',
              title: 'text-blue-900 dark:text-blue-100',
              accent: 'border-blue-500 dark:border-blue-400',
                text: 'text-muted-foreground'
              },
              {
                bg: 'bg-green-50 dark:bg-green-950/30',
                border: 'border-green-200 dark:border-green-800',
                icon: 'text-green-600 dark:text-green-400',
                title: 'text-green-900 dark:text-green-100',
                accent: 'border-green-500 dark:border-green-400',
                text: 'text-muted-foreground'
              },
              {
                bg: 'bg-purple-50 dark:bg-purple-950/30',
                border: 'border-purple-200 dark:border-purple-800',
                icon: 'text-purple-600 dark:text-purple-400',
                title: 'text-purple-900 dark:text-purple-100',
                accent: 'border-purple-500 dark:border-purple-400',
                text: 'text-muted-foreground'
              }
            ];
            const colorScheme = colors[index % colors.length];

            return (
              <Card key={index} className={`medical-card h-full bg-card ${colorScheme.border} shadow-lg hover:shadow-xl transition-all duration-300`}>
                <CardHeader className={`text-center ${colorScheme.bg} rounded-t-xl`}>
                  <Icon className={`h-14 w-14 ${colorScheme.icon} mx-auto mb-4`} />
                  <CardTitle className={`text-xl font-semibold ${colorScheme.title}`}>{category.title}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-5 p-6">
                  <p className={`${colorScheme.text} leading-relaxed`}>{category.description}</p>
                  <div>
                    <h4 className={`font-semibold text-base mb-3 ${colorScheme.title}`}>Key Strategies:</h4>
                    <ul className="space-y-2">
                      {category.tips.map((tip, tipIndex) => (
                        <li key={tipIndex} className={`flex items-start gap-3 p-3 rounded-lg ${colorScheme.bg} border-l-4 ${colorScheme.accent}`}>
                          <span className={`w-2 h-2 ${colorScheme.icon.replace('text-', 'bg-').replace('dark:text-', 'dark:bg-')} rounded-full mt-2 flex-shrink-0`} />
                          <span className={colorScheme.text}>{tip}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Nutrition and Sleep Section */}
        <div className="grid gap-8 md:grid-cols-2 mt-12">
          <Card className="medical-card bg-card border-blue-200 dark:border-blue-800 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 rounded-t-xl">
              <CardTitle className="flex items-center gap-3 text-blue-900 dark:text-blue-100">
                <Microscope className="h-7 w-7 text-blue-600 dark:text-blue-400" />
                <span className="text-lg font-semibold">Nutrition for Neurological Health</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-5 p-6">
              <p className="text-muted-foreground leading-relaxed">
                Proper nutrition supports brain function, reduces inflammation, and promotes healing of neural tissues.
              </p>
              <div className="grid gap-4">
                <div className="bg-blue-50 dark:bg-blue-950/30 p-4 rounded-lg border-l-4 border-blue-500 dark:border-blue-400">
                  <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">Brain-Healthy Foods:</h4>
                  <p className="text-muted-foreground">Omega-3 fatty fish, berries, leafy greens, nuts, and whole grains</p>
                </div>
                <div className="bg-green-50 dark:bg-green-950/30 p-4 rounded-lg border-l-4 border-green-500 dark:border-green-400">
                  <h4 className="font-semibold text-green-900 dark:text-green-100 mb-2">Anti-Inflammatory Foods:</h4>
                  <p className="text-muted-foreground">Turmeric, ginger, olive oil, and colourful vegetables</p>
                </div>
                <div className="bg-cyan-50 dark:bg-cyan-950/30 p-4 rounded-lg border-l-4 border-cyan-500 dark:border-cyan-400">
                  <h4 className="font-semibold text-cyan-900 dark:text-cyan-100 mb-2">Hydration:</h4>
                  <p className="text-muted-foreground">8-10 glasses of water daily for optimal brain function</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="medical-card bg-card border-purple-200 dark:border-purple-800 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardHeader className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-950/30 dark:to-pink-950/30 rounded-t-xl">
              <CardTitle className="flex items-center gap-3 text-purple-900 dark:text-purple-100">
                <Activity className="h-7 w-7 text-purple-600 dark:text-purple-400" />
                <span className="text-lg font-semibold">Sleep and Recovery</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-5 p-6">
              <p className="text-muted-foreground leading-relaxed">
                Quality sleep is essential for brain detoxification, memory consolidation, and spinal disc rehydration.
              </p>
              <div className="grid gap-4">
                <div className="bg-purple-50 dark:bg-purple-950/30 p-4 rounded-lg border-l-4 border-purple-500 dark:border-purple-400">
                  <h4 className="font-semibold text-purple-900 dark:text-purple-100 mb-2">Sleep Duration:</h4>
                  <p className="text-muted-foreground">7-9 hours nightly for optimal neurological function</p>
                </div>
                <div className="bg-indigo-50 dark:bg-indigo-950/30 p-4 rounded-lg border-l-4 border-indigo-500 dark:border-indigo-400">
                  <h4 className="font-semibold text-indigo-900 dark:text-indigo-100 mb-2">Sleep Hygiene:</h4>
                  <p className="text-muted-foreground">Consistent schedule, cool environment, limit screen time</p>
                </div>
                <div className="bg-pink-50 dark:bg-pink-950/30 p-4 rounded-lg border-l-4 border-pink-500 dark:border-pink-400">
                  <h4 className="font-semibold text-pink-900 dark:text-pink-100 mb-2">Spinal Positioning:</h4>
                  <p className="text-muted-foreground">Supportive mattress and pillow for proper spinal alignment</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </TabsContent>
  );
};

export default LifestyleStrategiesSection;
