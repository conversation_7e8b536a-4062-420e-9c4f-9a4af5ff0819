import { <PERSON><PERSON>, Clock, ArrowRight, Activity, CheckCircle, Stethoscope } from 'lucide-react';
import React from 'react';
import { Link } from 'react-router-dom';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import type { SpineCondition } from '@/data/spine-conditions';

interface ConditionsGridProps {
  conditions: SpineCondition[];
}

const ConditionsGrid: React.FC<ConditionsGridProps> = ({ conditions }) => {
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'mild': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'moderate': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'severe': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  return (
    <section className="py-16">
      <div className="container max-w-7xl">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {conditions.map((condition) => (
            <Card
              key={condition.id}
              className="group hover:shadow-xl transition-all duration-300 border-2 hover:border-primary/20 h-full flex flex-col"
            >
              <CardHeader className="pb-4">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <div className="p-2 rounded-lg bg-primary/10 group-hover:bg-primary/20 transition-colors">
                      <Layers className="h-5 w-5 text-primary" />
                    </div>
                    <Badge className={getSeverityColor(condition.severity)}>
                      {condition.severity}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <Clock className="h-3 w-3" />
                    {condition.estimatedReadTime}
                  </div>
                </div>

                <CardTitle className="text-xl font-bold group-hover:text-primary transition-colors mb-2">
                  {condition.name}
                </CardTitle>

                <div className="flex items-center gap-2 mb-3">
                  <Badge variant="outline" className="text-xs">
                    {condition.region}
                  </Badge>
                  <span className="text-xs text-muted-foreground">
                    {condition.prevalence}
                  </span>
                </div>

                <CardDescription className="text-sm leading-relaxed">
                  {condition.description}
                </CardDescription>
              </CardHeader>

              <CardContent className="flex-1 pb-4">
                <div className="space-y-4">
                  {/* Symptoms */}
                  <div>
                    <h4 className="font-semibold text-sm mb-2 flex items-center gap-2">
                      <Activity className="h-4 w-4 text-primary" />
                      Common Symptoms
                    </h4>
                    <div className="flex flex-wrap gap-1">
                      {condition.symptoms.slice(0, 3).map((symptom, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {symptom}
                        </Badge>
                      ))}
                      {condition.symptoms.length > 3 && (
                        <Badge variant="secondary" className="text-xs">
                          +{condition.symptoms.length - 3} more
                        </Badge>
                      )}
                    </div>
                  </div>

                  {/* Treatment Options */}
                  <div>
                    <h4 className="font-semibold text-sm mb-2 flex items-center gap-2">
                      <Stethoscope className="h-4 w-4 text-primary" />
                      Treatment Options
                    </h4>
                    <div className="flex flex-wrap gap-1">
                      {condition.treatmentOptions.slice(0, 2).map((treatment, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {treatment}
                        </Badge>
                      ))}
                      {condition.treatmentOptions.length > 2 && (
                        <Badge variant="outline" className="text-xs">
                          +{condition.treatmentOptions.length - 2} more
                        </Badge>
                      )}
                    </div>
                  </div>

                  {/* Key Features */}
                  <div>
                    <h4 className="font-semibold text-sm mb-2 flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      Key Features
                    </h4>
                    <ul className="text-xs text-muted-foreground space-y-1">
                      {condition.keyFeatures.slice(0, 2).map((feature, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <span className="w-1 h-1 bg-current rounded-full mt-2 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Value Proposition */}
                  <div className="bg-primary/5 p-3 rounded-lg">
                    <p className="text-xs text-muted-foreground italic">
                      {condition.valueProposition}
                    </p>
                  </div>
                </div>
              </CardContent>

              <div className="p-6 pt-0">
                <Button asChild className="w-full transition-colors">
                  <Link to={condition.path}>
                    Learn More
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ConditionsGrid;
